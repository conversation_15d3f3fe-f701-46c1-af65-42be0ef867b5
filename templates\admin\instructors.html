{% extends "base.html" %}

{% block title %}إدارة المدرسين - {{ platform_name }}{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/admin.css') }}" rel="stylesheet">
<style>
    .instructor-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
    }
    
    .instructor-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    
    .instructor-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
        font-weight: bold;
    }
    
    .permission-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 20px;
        margin: 0.125rem;
        display: inline-block;
    }
    
    .permission-enabled {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .permission-disabled {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        transition: transform 0.3s ease;
    }
    
    .stats-card:hover {
        transform: translateY(-3px);
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .filter-section {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .permissions-modal .form-check {
        margin-bottom: 1rem;
    }
    
    .stage-checkbox {
        margin: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-chalkboard-teacher text-primary me-2"></i>
                        إدارة المدرسين
                    </h1>
                    <p class="text-muted mb-0">إدارة المدرسين وصلاحياتهم في المنصة</p>
                </div>
                <button class="btn btn-primary" onclick="refreshData()">
                    <i class="fas fa-sync-alt me-2"></i>
                    تحديث البيانات
                </button>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4" id="statisticsSection">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number" id="totalInstructors">0</div>
                <div>إجمالي المدرسين</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                <div class="stats-number" id="activeInstructors">0</div>
                <div>المدرسين النشطين</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);">
                <div class="stats-number" id="canCreateCourses">0</div>
                <div>يمكنهم إنشاء كورسات</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                <div class="stats-number" id="canCreateGeneral">0</div>
                <div>يمكنهم إنشاء كورسات عامة</div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="filter-section">
        <div class="row">
            <div class="col-md-4">
                <label class="form-label">البحث</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="searchInput" placeholder="البحث بالاسم أو البريد الإلكتروني">
                </div>
            </div>
            <div class="col-md-3">
                <label class="form-label">التخصص</label>
                <select class="form-select" id="specializationFilter">
                    <option value="">جميع التخصصات</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">الحالة</label>
                <select class="form-select" id="statusFilter">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                    <i class="fas fa-times me-2"></i>
                    مسح الفلاتر
                </button>
            </div>
        </div>
    </div>

    <!-- قائمة المدرسين -->
    <div class="row" id="instructorsContainer">
        <!-- سيتم تحميل المدرسين هنا -->
    </div>

    <!-- Loading Spinner -->
    <div class="text-center py-5" id="loadingSpinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
        <p class="mt-2 text-muted">جاري تحميل بيانات المدرسين...</p>
    </div>

    <!-- رسالة عدم وجود بيانات -->
    <div class="text-center py-5 d-none" id="noDataMessage">
        <i class="fas fa-users fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">لا توجد بيانات مدرسين</h5>
        <p class="text-muted">لم يتم العثور على أي مدرسين في النظام</p>
    </div>
</div>

<!-- Modal تعديل الصلاحيات -->
<div class="modal fade" id="permissionsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-key me-2"></i>
                    تعديل صلاحيات المدرس
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body permissions-modal">
                <form id="permissionsForm">
                    <input type="hidden" id="instructorId">
                    
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="fw-bold mb-3">معلومات المدرس</h6>
                            <div class="d-flex align-items-center">
                                <div class="instructor-avatar me-3" id="modalInstructorAvatar">
                                    A
                                </div>
                                <div>
                                    <h6 class="mb-1" id="modalInstructorName">اسم المدرس</h6>
                                    <p class="text-muted mb-0" id="modalInstructorEmail">البريد الإلكتروني</p>
                                    <small class="text-muted" id="modalInstructorSpecialization">التخصص</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">الصلاحيات الأساسية</h6>
                            
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="canCreateCourses">
                                <label class="form-check-label" for="canCreateCourses">
                                    <i class="fas fa-plus-circle text-success me-2"></i>
                                    يمكن إنشاء كورسات
                                </label>
                            </div>
                            
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="canManageStudents">
                                <label class="form-check-label" for="canManageStudents">
                                    <i class="fas fa-users text-info me-2"></i>
                                    يمكن إدارة الطلاب
                                </label>
                            </div>
                            
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="canCreateGeneral">
                                <label class="form-check-label" for="canCreateGeneral">
                                    <i class="fas fa-globe text-warning me-2"></i>
                                    يمكن إنشاء كورسات عامة
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">المراحل المسموحة</h6>
                            <div id="stagesContainer">
                                <!-- سيتم إضافة المراحل هنا -->
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-primary" onclick="savePermissions()">
                    <i class="fas fa-save me-2"></i>
                    حفظ التغييرات
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/admin-instructors.js') }}"></script>
{% endblock %}
