#!/usr/bin/env python3
"""
إنشاء حساب أدمن جديد
"""

import os
import sys
from datetime import datetime, timezone
from utils.firebase_utils import FirebaseManager

def create_admin_account():
    """إنشاء حساب أدمن جديد"""
    
    # بيانات الأدمن
    admin_data = {
        'email': '<EMAIL>',
        'password': 'admin123',
        'full_name': 'مدير النظام',
        'first_name': 'مدير',
        'last_name': 'النظام',
        'role': 'admin',
        'active': True,
        'telegram_id': 'admin_platform',
        'created_at': datetime.now(timezone.utc).isoformat(),
        'updated_at': datetime.now(timezone.utc).isoformat()
    }
    
    try:
        # تهيئة Firebase
        firebase_manager = FirebaseManager()
        
        # التحقق من وجود الأدمن
        existing_admin = firebase_manager.get_user_by_email(admin_data['email'])
        if existing_admin:
            print(f"✅ حساب الأدمن موجود بالفعل: {admin_data['email']}")
            return existing_admin
        
        # إنشاء الحساب
        user_id = firebase_manager.create_user(admin_data)
        
        if user_id:
            print(f"✅ تم إنشاء حساب الأدمن بنجاح!")
            print(f"📧 البريد الإلكتروني: {admin_data['email']}")
            print(f"🔑 كلمة المرور: {admin_data['password']}")
            print(f"🆔 معرف المستخدم: {user_id}")
            return user_id
        else:
            print("❌ فشل في إنشاء حساب الأدمن")
            return None
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء حساب الأدمن: {e}")
        return None

def create_test_instructor():
    """إنشاء حساب مدرس للاختبار"""
    
    instructor_data = {
        'email': '<EMAIL>',
        'password': 'instructor123',
        'full_name': 'د. أحمد محمد',
        'first_name': 'أحمد',
        'last_name': 'محمد',
        'role': 'instructor',
        'active': True,
        'telegram_id': 'instructor_test',
        'specialization_id': None,  # سيتم تحديده لاحقاً
        'permissions': {
            'can_teach_all_levels': True,
            'allowed_levels': [],
            'can_teach_general_courses': True
        },
        'created_at': datetime.now(timezone.utc).isoformat(),
        'updated_at': datetime.now(timezone.utc).isoformat()
    }
    
    try:
        firebase_manager = FirebaseManager()
        
        # التحقق من وجود المدرس
        existing_instructor = firebase_manager.get_user_by_email(instructor_data['email'])
        if existing_instructor:
            print(f"✅ حساب المدرس موجود بالفعل: {instructor_data['email']}")
            return existing_instructor
        
        # إنشاء الحساب
        user_id = firebase_manager.create_user(instructor_data)
        
        if user_id:
            print(f"✅ تم إنشاء حساب المدرس بنجاح!")
            print(f"📧 البريد الإلكتروني: {instructor_data['email']}")
            print(f"🔑 كلمة المرور: {instructor_data['password']}")
            print(f"🆔 معرف المستخدم: {user_id}")
            return user_id
        else:
            print("❌ فشل في إنشاء حساب المدرس")
            return None
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء حساب المدرس: {e}")
        return None

def create_test_student():
    """إنشاء حساب طالب للاختبار"""
    
    student_data = {
        'email': '<EMAIL>',
        'password': 'student123',
        'full_name': 'محمد علي',
        'first_name': 'محمد',
        'last_name': 'علي',
        'role': 'student',
        'active': True,
        'telegram_id': 'student_test',
        'specialization_id': None,  # سيتم تحديده لاحقاً
        'assigned_instructor': None,  # سيتم ربطه بالمدرس لاحقاً
        'created_at': datetime.now(timezone.utc).isoformat(),
        'updated_at': datetime.now(timezone.utc).isoformat()
    }
    
    try:
        firebase_manager = FirebaseManager()
        
        # التحقق من وجود الطالب
        existing_student = firebase_manager.get_user_by_email(student_data['email'])
        if existing_student:
            print(f"✅ حساب الطالب موجود بالفعل: {student_data['email']}")
            return existing_student
        
        # إنشاء الحساب
        user_id = firebase_manager.create_user(student_data)
        
        if user_id:
            print(f"✅ تم إنشاء حساب الطالب بنجاح!")
            print(f"📧 البريد الإلكتروني: {student_data['email']}")
            print(f"🔑 كلمة المرور: {student_data['password']}")
            print(f"🆔 معرف المستخدم: {user_id}")
            return user_id
        else:
            print("❌ فشل في إنشاء حساب الطالب")
            return None
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء حساب الطالب: {e}")
        return None

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إنشاء حسابات الاختبار...")
    print("=" * 50)
    
    # إنشاء حساب الأدمن
    print("1️⃣ إنشاء حساب الأدمن...")
    admin_id = create_admin_account()
    
    print("\n" + "=" * 50)
    
    # إنشاء حساب المدرس
    print("2️⃣ إنشاء حساب المدرس...")
    instructor_id = create_test_instructor()
    
    print("\n" + "=" * 50)
    
    # إنشاء حساب الطالب
    print("3️⃣ إنشاء حساب الطالب...")
    student_id = create_test_student()
    
    print("\n" + "=" * 50)
    print("✅ تم الانتهاء من إنشاء حسابات الاختبار!")
    
    if admin_id and instructor_id and student_id:
        print("\n📋 ملخص الحسابات المنشأة:")
        print(f"👨‍💼 الأدمن: <EMAIL> / admin123")
        print(f"👨‍🏫 المدرس: <EMAIL> / instructor123")
        print(f"👨‍🎓 الطالب: <EMAIL> / student123")
        
        # ربط الطالب بالمدرس
        try:
            firebase_manager = FirebaseManager()
            success = firebase_manager.update_student_instructor(student_id, instructor_id)
            if success:
                print(f"\n🔗 تم ربط الطالب بالمدرس بنجاح!")
            else:
                print(f"\n⚠️ فشل في ربط الطالب بالمدرس")
        except Exception as e:
            print(f"\n❌ خطأ في ربط الطالب بالمدرس: {e}")

if __name__ == "__main__":
    main()
