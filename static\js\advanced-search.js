/**
 * نظام البحث والتصفية المتقدم
 * يدعم البحث الفوري، حفظ الفلاتر، والتصفية المتقدمة
 */

class AdvancedSearchManager {
    constructor(options = {}) {
        this.options = {
            apiEndpoint: '/api/courses/search',
            searchInputId: 'searchInput',
            resultsContainerId: 'searchResults',
            filtersContainerId: 'searchFilters',
            statsContainerId: 'searchStats',
            debounceDelay: 300,
            saveFilters: true,
            updateUrl: true,
            ...options
        };

        this.searchState = {
            query: '',
            filters: {
                specialization: '',
                stage: '',
                instructor: '',
                status: '',
                sortBy: 'created_at',
                sortOrder: 'desc'
            },
            page: 1,
            limit: 12,
            total: 0,
            loading: false
        };

        this.debounceTimer = null;
        this.init();
    }

    init() {
        this.setupElements();
        this.setupEventListeners();
        this.loadSavedFilters();
        this.loadFromUrl();
        this.performSearch();
    }

    setupElements() {
        this.searchInput = document.getElementById(this.options.searchInputId);
        this.resultsContainer = document.getElementById(this.options.resultsContainerId);
        this.filtersContainer = document.getElementById(this.options.filtersContainerId);
        this.statsContainer = document.getElementById(this.options.statsContainerId);

        if (!this.searchInput || !this.resultsContainer) {
            console.error('عناصر البحث المطلوبة غير موجودة');
            return;
        }
    }

    setupEventListeners() {
        // البحث الفوري
        if (this.searchInput) {
            this.searchInput.addEventListener('input', (e) => {
                this.handleSearchInput(e.target.value);
            });
        }

        // فلاتر التصفية
        document.addEventListener('change', (e) => {
            if (e.target.matches('.search-filter')) {
                this.handleFilterChange(e.target);
            }
        });

        // أزرار الترتيب
        document.addEventListener('click', (e) => {
            if (e.target.matches('.sort-btn')) {
                this.handleSortChange(e.target);
            }
        });

        // مسح الفلاتر
        document.addEventListener('click', (e) => {
            if (e.target.matches('.clear-filters-btn')) {
                this.clearFilters();
            }
        });

        // تصدير النتائج
        document.addEventListener('click', (e) => {
            if (e.target.matches('.export-results-btn')) {
                this.exportResults();
            }
        });

        // pagination
        document.addEventListener('click', (e) => {
            if (e.target.matches('.pagination-btn')) {
                const page = parseInt(e.target.dataset.page);
                this.changePage(page);
            }
        });
    }

    handleSearchInput(query) {
        this.searchState.query = query;
        this.searchState.page = 1;

        // إلغاء البحث السابق
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }

        // بحث جديد مع تأخير
        this.debounceTimer = setTimeout(() => {
            this.performSearch();
        }, this.options.debounceDelay);
    }

    handleFilterChange(filterElement) {
        const filterName = filterElement.name;
        const filterValue = filterElement.value;

        this.searchState.filters[filterName] = filterValue;
        this.searchState.page = 1;

        this.performSearch();
        this.saveFilters();
    }

    handleSortChange(sortButton) {
        const sortBy = sortButton.dataset.sortBy;
        const currentOrder = this.searchState.filters.sortOrder;
        
        // تبديل الترتيب إذا كان نفس العمود
        if (this.searchState.filters.sortBy === sortBy) {
            this.searchState.filters.sortOrder = currentOrder === 'asc' ? 'desc' : 'asc';
        } else {
            this.searchState.filters.sortBy = sortBy;
            this.searchState.filters.sortOrder = 'desc';
        }

        this.updateSortButtons();
        this.performSearch();
    }

    async performSearch() {
        if (this.searchState.loading) return;

        this.searchState.loading = true;
        this.showLoading();

        try {
            const params = new URLSearchParams({
                q: this.searchState.query,
                page: this.searchState.page,
                limit: this.searchState.limit,
                ...this.searchState.filters
            });

            const response = await fetch(`${this.options.apiEndpoint}?${params}`);
            const data = await response.json();

            if (data.success) {
                this.displayResults(data.results);
                this.updateStats(data.stats);
                this.updatePagination(data.pagination);
                this.updateUrl();

                // تحديث إحصائيات الاستخدام
                this.updateUsageStats(data.stats.total);
            } else {
                this.showError(data.message || 'حدث خطأ في البحث');
            }
        } catch (error) {
            console.error('خطأ في البحث:', error);
            this.showError('حدث خطأ في الاتصال بالخادم');
        } finally {
            this.searchState.loading = false;
            this.hideLoading();
        }
    }

    displayResults(results) {
        if (!this.resultsContainer) return;

        if (results.length === 0) {
            this.showEmptyState();
            return;
        }

        const resultsHtml = results.map(item => this.createResultCard(item)).join('');
        this.resultsContainer.innerHTML = `
            <div class="row g-4">
                ${resultsHtml}
            </div>
        `;
    }

    createResultCard(course) {
        const enrollmentBadge = course.enrollment_info ? 
            '<span class="badge bg-success">مسجل</span>' : 
            '<span class="badge bg-primary">متاح</span>';

        const stageBadge = course.is_general ? 
            '<span class="badge bg-info">عام</span>' : 
            `<span class="badge bg-secondary">المرحلة ${course.stage}</span>`;

        return `
            <div class="col-md-6 col-lg-4">
                <div class="card course-card h-100 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h5 class="card-title">${course.title}</h5>
                            ${enrollmentBadge}
                        </div>
                        
                        <p class="card-text text-muted small mb-3">
                            ${course.description || 'لا يوجد وصف'}
                        </p>
                        
                        <div class="course-meta mb-3">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-user text-primary me-2"></i>
                                <span class="small">${course.instructor_name || 'غير محدد'}</span>
                            </div>
                            
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-graduation-cap text-success me-2"></i>
                                <span class="small">${course.specialization_name || 'كورس عام'}</span>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                ${stageBadge}
                                <small class="text-muted">
                                    ${new Date(course.created_at).toLocaleDateString('ar-SA')}
                                </small>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <a href="/courses/${course.id}" class="btn btn-primary">
                                ${course.enrollment_info ? 'متابعة الدراسة' : 'عرض الكورس'}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    updateStats(stats) {
        if (!this.statsContainer) return;

        this.statsContainer.innerHTML = `
            <div class="search-stats">
                <div class="row g-3">
                    <div class="col-auto">
                        <span class="badge bg-primary fs-6">
                            ${stats.total} نتيجة
                        </span>
                    </div>
                    <div class="col-auto">
                        <span class="badge bg-success fs-6">
                            ${stats.enrolled || 0} مسجل
                        </span>
                    </div>
                    <div class="col-auto">
                        <span class="badge bg-info fs-6">
                            ${stats.available || 0} متاح
                        </span>
                    </div>
                </div>
            </div>
        `;
    }

    updatePagination(pagination) {
        const paginationContainer = document.getElementById('searchPagination');
        if (!paginationContainer || pagination.totalPages <= 1) {
            if (paginationContainer) paginationContainer.innerHTML = '';
            return;
        }

        let paginationHtml = '<nav><ul class="pagination justify-content-center">';

        // زر السابق
        if (pagination.currentPage > 1) {
            paginationHtml += `
                <li class="page-item">
                    <button class="page-link pagination-btn" data-page="${pagination.currentPage - 1}">
                        السابق
                    </button>
                </li>
            `;
        }

        // أرقام الصفحات
        for (let i = 1; i <= pagination.totalPages; i++) {
            if (i === pagination.currentPage) {
                paginationHtml += `
                    <li class="page-item active">
                        <span class="page-link">${i}</span>
                    </li>
                `;
            } else {
                paginationHtml += `
                    <li class="page-item">
                        <button class="page-link pagination-btn" data-page="${i}">
                            ${i}
                        </button>
                    </li>
                `;
            }
        }

        // زر التالي
        if (pagination.currentPage < pagination.totalPages) {
            paginationHtml += `
                <li class="page-item">
                    <button class="page-link pagination-btn" data-page="${pagination.currentPage + 1}">
                        التالي
                    </button>
                </li>
            `;
        }

        paginationHtml += '</ul></nav>';
        paginationContainer.innerHTML = paginationHtml;
    }

    changePage(page) {
        this.searchState.page = page;
        this.performSearch();
    }

    showLoading() {
        if (this.resultsContainer) {
            this.resultsContainer.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري البحث...</span>
                    </div>
                    <p class="mt-3 text-muted">جاري البحث...</p>
                </div>
            `;
        }
    }

    hideLoading() {
        // يتم إخفاء التحميل عند عرض النتائج
    }

    showEmptyState() {
        if (this.resultsContainer) {
            this.resultsContainer.innerHTML = `
                <div class="empty-state text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4>لا توجد نتائج</h4>
                    <p class="text-muted">لم يتم العثور على كورسات تطابق معايير البحث</p>
                    <button class="btn btn-outline-primary clear-filters-btn">
                        مسح الفلاتر
                    </button>
                </div>
            `;
        }
    }

    showError(message) {
        if (this.resultsContainer) {
            this.resultsContainer.innerHTML = `
                <div class="alert alert-danger text-center">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${message}
                </div>
            `;
        }
    }

    clearFilters() {
        this.searchState.query = '';
        this.searchState.filters = {
            specialization: '',
            stage: '',
            instructor: '',
            status: '',
            sortBy: 'created_at',
            sortOrder: 'desc'
        };
        this.searchState.page = 1;

        // مسح عناصر الواجهة
        if (this.searchInput) this.searchInput.value = '';
        
        document.querySelectorAll('.search-filter').forEach(filter => {
            filter.value = '';
        });

        this.performSearch();
        this.saveFilters();
    }

    saveFilters() {
        if (!this.options.saveFilters) return;

        const filtersData = {
            query: this.searchState.query,
            filters: this.searchState.filters
        };

        localStorage.setItem('advancedSearchFilters', JSON.stringify(filtersData));
    }

    loadSavedFilters() {
        if (!this.options.saveFilters) return;

        try {
            const saved = localStorage.getItem('advancedSearchFilters');
            if (saved) {
                const filtersData = JSON.parse(saved);
                this.searchState.query = filtersData.query || '';
                this.searchState.filters = { ...this.searchState.filters, ...filtersData.filters };

                // تطبيق الفلاتر على الواجهة
                if (this.searchInput) this.searchInput.value = this.searchState.query;
                
                Object.keys(this.searchState.filters).forEach(key => {
                    const element = document.querySelector(`[name="${key}"]`);
                    if (element) element.value = this.searchState.filters[key];
                });
            }
        } catch (error) {
            console.error('خطأ في تحميل الفلاتر المحفوظة:', error);
        }
    }

    loadFromUrl() {
        if (!this.options.updateUrl) return;

        const params = new URLSearchParams(window.location.search);
        
        if (params.get('q')) {
            this.searchState.query = params.get('q');
            if (this.searchInput) this.searchInput.value = this.searchState.query;
        }

        Object.keys(this.searchState.filters).forEach(key => {
            if (params.get(key)) {
                this.searchState.filters[key] = params.get(key);
                const element = document.querySelector(`[name="${key}"]`);
                if (element) element.value = this.searchState.filters[key];
            }
        });
    }

    updateUrl() {
        if (!this.options.updateUrl) return;

        const params = new URLSearchParams();
        
        if (this.searchState.query) {
            params.set('q', this.searchState.query);
        }

        Object.keys(this.searchState.filters).forEach(key => {
            if (this.searchState.filters[key]) {
                params.set(key, this.searchState.filters[key]);
            }
        });

        const newUrl = `${window.location.pathname}?${params.toString()}`;
        window.history.replaceState({}, '', newUrl);
    }

    updateSortButtons() {
        document.querySelectorAll('.sort-btn').forEach(btn => {
            btn.classList.remove('active', 'asc', 'desc');
            
            if (btn.dataset.sortBy === this.searchState.filters.sortBy) {
                btn.classList.add('active', this.searchState.filters.sortOrder);
            }
        });
    }

    exportResults() {
        // تصدير النتائج كـ CSV أو JSON
        const params = new URLSearchParams({
            q: this.searchState.query,
            export: 'csv',
            ...this.searchState.filters
        });

        window.open(`${this.options.apiEndpoint}/export?${params}`, '_blank');
    }

    // دالة للحصول على حالة البحث الحالية
    getSearchState() {
        return { ...this.searchState };
    }

    // دالة لتحديث خيارات البحث
    updateOptions(newOptions) {
        this.options = { ...this.options, ...newOptions };
    }
}

    // دالة لإضافة فلاتر مخصصة
    addCustomFilter(name, value) {
        this.searchState.filters[name] = value;
        this.performSearch();
    }

    // دالة لإزالة فلتر مخصص
    removeCustomFilter(name) {
        delete this.searchState.filters[name];
        this.performSearch();
    }

    // دالة للحصول على رابط مشاركة البحث
    getShareableLink() {
        const params = new URLSearchParams({
            q: this.searchState.query,
            ...this.searchState.filters
        });

        return `${window.location.origin}${window.location.pathname}?${params.toString()}`;
    }

    // دالة لحفظ البحث كمفضل
    saveSearchAsFavorite(name) {
        const favorites = JSON.parse(localStorage.getItem('searchFavorites') || '[]');

        const searchData = {
            id: Date.now().toString(),
            name: name,
            query: this.searchState.query,
            filters: { ...this.searchState.filters },
            createdAt: new Date().toISOString()
        };

        favorites.push(searchData);
        localStorage.setItem('searchFavorites', JSON.stringify(favorites));

        return searchData.id;
    }

    // دالة لتحميل البحث المفضل
    loadFavoriteSearch(favoriteId) {
        const favorites = JSON.parse(localStorage.getItem('searchFavorites') || '[]');
        const favorite = favorites.find(f => f.id === favoriteId);

        if (favorite) {
            this.searchState.query = favorite.query;
            this.searchState.filters = { ...favorite.filters };

            // تطبيق على الواجهة
            if (this.searchInput) this.searchInput.value = this.searchState.query;

            Object.keys(this.searchState.filters).forEach(key => {
                const element = document.querySelector(`[name="${key}"]`);
                if (element) element.value = this.searchState.filters[key];
            });

            this.performSearch();
        }
    }

    // دالة للحصول على البحثات المفضلة
    getFavoriteSearches() {
        return JSON.parse(localStorage.getItem('searchFavorites') || '[]');
    }

    // دالة لحذف البحث المفضل
    deleteFavoriteSearch(favoriteId) {
        const favorites = JSON.parse(localStorage.getItem('searchFavorites') || '[]');
        const updatedFavorites = favorites.filter(f => f.id !== favoriteId);
        localStorage.setItem('searchFavorites', JSON.stringify(updatedFavorites));
    }

    // دالة لتصدير البحثات المفضلة
    exportFavorites() {
        const favorites = this.getFavoriteSearches();
        const dataStr = JSON.stringify(favorites, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = 'search_favorites.json';
        link.click();
    }

    // دالة لاستيراد البحثات المفضلة
    importFavorites(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();

            reader.onload = (e) => {
                try {
                    const favorites = JSON.parse(e.target.result);
                    localStorage.setItem('searchFavorites', JSON.stringify(favorites));
                    resolve(favorites);
                } catch (error) {
                    reject(error);
                }
            };

            reader.onerror = () => reject(new Error('فشل في قراءة الملف'));
            reader.readAsText(file);
        });
    }

    // دالة لإعادة تعيين جميع الإعدادات
    resetAllSettings() {
        localStorage.removeItem('advancedSearchFilters');
        localStorage.removeItem('searchFavorites');
        this.clearFilters();
    }

    // دالة للحصول على إحصائيات الاستخدام
    getUsageStats() {
        const stats = JSON.parse(localStorage.getItem('searchStats') || '{}');
        return {
            totalSearches: stats.totalSearches || 0,
            lastSearchDate: stats.lastSearchDate || null,
            mostUsedFilters: stats.mostUsedFilters || {},
            averageResultsPerSearch: stats.averageResultsPerSearch || 0
        };
    }

    // دالة لتحديث إحصائيات الاستخدام
    updateUsageStats(resultsCount) {
        const stats = this.getUsageStats();

        stats.totalSearches += 1;
        stats.lastSearchDate = new Date().toISOString();

        // تحديث الفلاتر الأكثر استخداماً
        Object.keys(this.searchState.filters).forEach(key => {
            if (this.searchState.filters[key]) {
                stats.mostUsedFilters[key] = (stats.mostUsedFilters[key] || 0) + 1;
            }
        });

        // حساب متوسط النتائج
        const totalResults = (stats.averageResultsPerSearch * (stats.totalSearches - 1)) + resultsCount;
        stats.averageResultsPerSearch = Math.round(totalResults / stats.totalSearches);

        localStorage.setItem('searchStats', JSON.stringify(stats));
    }
}

// تصدير الفئة للاستخدام العام
window.AdvancedSearchManager = AdvancedSearchManager;
