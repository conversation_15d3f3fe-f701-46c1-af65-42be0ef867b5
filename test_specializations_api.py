"""
اختبار API التخصصات
Test Specializations API
"""

import requests
import json
import sys
import os

# إضافة المجلد الرئيسي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_specializations_api():
    """اختبار API التخصصات"""
    
    base_url = "http://localhost:5000"
    
    print("🧪 بدء اختبار API التخصصات...")
    print("=" * 50)
    
    # 1. اختبار الحصول على التخصصات (بدون مصادقة)
    print("\n1. اختبار الحصول على التخصصات...")
    try:
        response = requests.get(f"{base_url}/api/specializations")
        print(f"   الحالة: {response.status_code}")
        
        if response.status_code == 200:
            specializations = response.json()
            print(f"   ✅ تم العثور على {len(specializations)} تخصص")
            
            if specializations:
                print("   📋 التخصصات الموجودة:")
                for spec in specializations[:3]:  # عرض أول 3 تخصصات فقط
                    print(f"      - {spec.get('name', 'غير محدد')} ({spec.get('name_en', 'N/A')})")
        else:
            print(f"   ❌ فشل في الحصول على التخصصات: {response.text}")
            
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
    
    # 2. اختبار الحصول على إحصائيات عامة
    print("\n2. اختبار الحصول على الإحصائيات...")
    try:
        response = requests.get(f"{base_url}/api/config")
        print(f"   الحالة: {response.status_code}")
        
        if response.status_code == 200:
            config = response.json()
            print(f"   ✅ اسم المنصة: {config.get('platform_name', 'غير محدد')}")
            print(f"   ✅ رابط المنصة: {config.get('platform_url', 'غير محدد')}")
        else:
            print(f"   ❌ فشل في الحصول على الإعدادات: {response.text}")
            
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
    
    # 3. اختبار فحص صحة التطبيق
    print("\n3. اختبار فحص صحة التطبيق...")
    try:
        response = requests.get(f"{base_url}/api/health")
        print(f"   الحالة: {response.status_code}")
        
        if response.status_code == 200:
            health = response.json()
            print(f"   ✅ حالة التطبيق: {health.get('status', 'غير محدد')}")
            print(f"   ✅ الوقت: {health.get('timestamp', 'غير محدد')}")
        else:
            print(f"   ❌ فشل في فحص صحة التطبيق: {response.text}")
            
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
    
    # 4. اختبار الحصول على تخصص محدد (إذا كان موجود)
    print("\n4. اختبار الحصول على تخصص محدد...")
    try:
        # أولاً نحصل على قائمة التخصصات
        response = requests.get(f"{base_url}/api/specializations")
        if response.status_code == 200:
            specializations = response.json()
            if specializations:
                first_spec_id = specializations[0].get('id')
                if first_spec_id:
                    # اختبار الحصول على التخصص المحدد
                    response = requests.get(f"{base_url}/api/specializations/{first_spec_id}")
                    print(f"   الحالة: {response.status_code}")
                    
                    if response.status_code == 200:
                        spec = response.json()
                        print(f"   ✅ تم الحصول على التخصص: {spec.get('name', 'غير محدد')}")
                    else:
                        print(f"   ❌ فشل في الحصول على التخصص: {response.text}")
                else:
                    print("   ⚠️ لا يوجد معرف للتخصص الأول")
            else:
                print("   ⚠️ لا توجد تخصصات للاختبار")
        else:
            print("   ❌ فشل في الحصول على قائمة التخصصات للاختبار")
            
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال: {e}")
    
    # 5. اختبار الوصول للصفحات (بدون مصادقة)
    print("\n5. اختبار الوصول للصفحات...")
    
    pages_to_test = [
        ("/", "الصفحة الرئيسية"),
        ("/login", "صفحة تسجيل الدخول"),
        ("/specializations", "صفحة التخصصات")
    ]
    
    for url, name in pages_to_test:
        try:
            response = requests.get(f"{base_url}{url}")
            print(f"   {name}: {response.status_code}")
            
            if response.status_code == 200:
                print(f"      ✅ تم تحميل الصفحة بنجاح")
            elif response.status_code == 302:
                print(f"      ↗️ إعادة توجيه (طبيعي)")
            else:
                print(f"      ❌ خطأ في تحميل الصفحة")
                
        except Exception as e:
            print(f"      ❌ خطأ في الاتصال: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 انتهى اختبار API التخصصات")
    print("\n📝 ملاحظات:")
    print("   - للاختبار الكامل، يجب تسجيل الدخول كأدمن")
    print("   - بعض الـ endpoints تتطلب مصادقة")
    print("   - تأكد من تشغيل التطبيق على المنفذ 5000")

def test_admin_endpoints_info():
    """عرض معلومات عن endpoints الأدمن"""
    
    print("\n" + "=" * 50)
    print("📋 معلومات عن API endpoints للأدمن:")
    print("=" * 50)
    
    admin_endpoints = [
        ("POST", "/api/admin/specializations", "إنشاء تخصص جديد"),
        ("PUT", "/api/admin/specializations/<id>", "تحديث تخصص موجود"),
        ("DELETE", "/api/admin/specializations/<id>", "حذف تخصص"),
        ("PATCH", "/api/admin/specializations/<id>/toggle", "تبديل حالة التخصص"),
        ("GET", "/api/admin/specializations/statistics", "إحصائيات التخصصات"),
        ("POST", "/api/admin/specializations/import", "استيراد التخصصات"),
        ("GET", "/api/admin/statistics", "الإحصائيات العامة")
    ]
    
    for method, endpoint, description in admin_endpoints:
        print(f"   {method:6} {endpoint:40} - {description}")
    
    print("\n📋 معلومات عن endpoints العامة:")
    print("=" * 30)
    
    public_endpoints = [
        ("GET", "/api/specializations", "الحصول على جميع التخصصات"),
        ("GET", "/api/specializations/<id>", "الحصول على تخصص محدد"),
        ("GET", "/api/health", "فحص صحة التطبيق"),
        ("GET", "/api/config", "الحصول على إعدادات التطبيق")
    ]
    
    for method, endpoint, description in public_endpoints:
        print(f"   {method:6} {endpoint:30} - {description}")

def create_sample_specialization_data():
    """إنشاء بيانات تخصص تجريبية"""
    
    sample_data = {
        "name": "تخصص تجريبي",
        "name_en": "Test Specialization",
        "description": "هذا تخصص تجريبي لاختبار النظام",
        "icon": "fas fa-flask",
        "color": "#28a745",
        "stages": [2, 3, 4],
        "active": True
    }
    
    print("\n📋 بيانات تخصص تجريبية:")
    print("=" * 30)
    print(json.dumps(sample_data, indent=2, ensure_ascii=False))
    
    return sample_data

if __name__ == "__main__":
    # تشغيل الاختبارات
    test_specializations_api()
    test_admin_endpoints_info()
    create_sample_specialization_data()
    
    print("\n🚀 لتشغيل اختبارات أكثر تفصيلاً:")
    print("   python test_specializations_api.py")
    print("\n💡 لاختبار endpoints الأدمن:")
    print("   1. سجل دخول كأدمن في المتصفح")
    print("   2. احصل على التوكين من Developer Tools")
    print("   3. استخدم التوكين في headers الطلبات")
