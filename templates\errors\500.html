{% extends "base.html" %}

{% block title %}خطأ في الخادم - {{ platform_name }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 text-center py-5">
            <div class="error-page">
                <div class="error-number mb-4">
                    <h1 class="display-1 fw-bold text-danger">500</h1>
                </div>
                
                <div class="error-icon mb-4">
                    <i class="fas fa-exclamation-triangle fa-5x text-warning"></i>
                </div>
                
                <h2 class="fw-bold mb-3">خطأ في الخادم</h2>
                <p class="text-muted mb-4">
                    عذراً، حدث خطأ غير متوقع في الخادم. نحن نعمل على حل هذه المشكلة.
                </p>
                
                <div class="error-actions">
                    <a href="{{ url_for('index') }}" class="btn btn-primary me-3">
                        <i class="fas fa-home me-2"></i>العودة للرئيسية
                    </a>
                    <button onclick="location.reload()" class="btn btn-outline-secondary">
                        <i class="fas fa-redo me-2"></i>إعادة المحاولة
                    </button>
                </div>
                
                <div class="mt-5">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        إذا استمرت المشكلة، يرجى التواصل مع الدعم الفني
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    animation: fadeInUp 0.6s ease-out;
}

.error-number h1 {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.error-icon i {
    animation: shake 1s infinite;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
{% endblock %}
