# إصلاح مشكلة تسجيل الدخول المتكرر في صفحة الكورسات

## المشكلة
عند الضغط على أزرار التعديل أو الحذف في صفحة الكورسات، كان يتم طلب تسجيل الدخول مرارًا وتكرارًا حتى بعد تسجيل الدخول بنجاح.

## سبب المشكلة
تم اكتشاف مشكلتين رئيسيتين:

### 1. تضارب في دوال `fetchWithAuth`
- كان هناك دالتان مختلفتان باسم `fetchWithAuth`:
  - في `static/js/main.js`: تعتمد على cookies فقط
  - في `static/js/content-editor.js`: تعتمد على localStorage/sessionStorage
- هذا التضارب كان يسبب مشاكل في إرسال التوكين

### 2. استخدام خاطئ لـ `session` في API routes
- بعض API routes كانت تستخدم `session.get('user_id')` بدلاً من `get_current_user()`
- API routes تستخدم JWT tokens وليس sessions

## الحلول المطبقة

### 1. توحيد دالة `fetchWithAuth`
تم تحديث دالة `fetchWithAuth` في `static/js/main.js` لتدعم:
- التوكين من localStorage/sessionStorage (Authorization header)
- Cookies (للأمان الإضافي)
- مسح التوكين المحفوظ عند انتهاء الصلاحية

```javascript
// الحصول على التوكين من مصادر متعددة
let token = localStorage.getItem('auth_token') || 
           localStorage.getItem('authToken') || 
           sessionStorage.getItem('auth_token') || 
           sessionStorage.getItem('authToken');

// إضافة Authorization header إذا كان التوكين متاحاً
if (token) {
    headers['Authorization'] = `Bearer ${token}`;
}

// تضمين cookies للأمان الإضافي
credentials: 'include'
```

### 2. إزالة دالة `fetchWithAuth` المكررة
تم إزالة الدالة المكررة من `static/js/content-editor.js` واستبدالها بتعليق.

### 3. إصلاح API routes
تم إصلاح الـ API routes التالية لاستخدام `get_current_user()` بدلاً من `session`:

#### في `app.py`:
1. **`api_delete_course`** (السطر 2906)
2. **`api_update_course_status`** (السطر 2959)
3. **`api_get_comprehensive_analytics`** (السطر 2107-2108)
4. **`api_get_activation_codes`** (السطر 3079-3080)
5. **`api_use_activation_code`** (السطر 3121)
6. **`api_get_student_courses`** (السطر 3199)
7. **`api_instructor_statistics`** (السطر 3759)

```python
# قبل الإصلاح
user_id = session.get('user_id')

# بعد الإصلاح
current_user = get_current_user()
user_id = current_user.get('user_id')
```

## الملفات المعدلة
1. `static/js/main.js` - توحيد دالة `fetchWithAuth`
2. `static/js/content-editor.js` - إزالة الدالة المكررة
3. `app.py` - إصلاح 7 API routes

## النتيجة
- تم حل مشكلة طلب تسجيل الدخول المتكرر
- أصبحت أزرار التعديل والحذف تعمل بشكل صحيح
- تحسن الأمان من خلال دعم كلاً من JWT tokens و cookies
- توحيد طريقة المصادقة عبر جميع أجزاء التطبيق

## اختبار الحل
1. سجل دخول كمدرس
2. انتقل لصفحة الكورسات
3. اضغط على أزرار التعديل أو الحذف
4. يجب أن تعمل الأزرار بدون طلب تسجيل دخول إضافي
