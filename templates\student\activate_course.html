{% extends "base.html" %}

{% block title %}تفعيل الكورس - {{ platform_name }}{% endblock %}

{% block extra_css %}
<style>
    .activation-hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 30px 30px;
        position: relative;
        overflow: hidden;
    }
    
    .activation-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="50" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="30" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }
    
    .activation-card {
        background: white;
        border-radius: 20px;
        padding: 2.5rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .activation-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    }
    
    .code-input-group {
        position: relative;
        margin-bottom: 1.5rem;
    }
    
    .code-input {
        font-family: 'Courier New', monospace;
        font-size: 1.2rem;
        font-weight: bold;
        text-align: center;
        text-transform: uppercase;
        letter-spacing: 2px;
        padding: 1rem 1.5rem;
        border: 3px solid #e3e6f0;
        border-radius: 15px;
        transition: all 0.3s ease;
        background: #f8f9fc;
    }
    
    .code-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
        transform: translateY(-2px);
    }
    
    .btn-activate {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
        padding: 1rem 2.5rem;
        border-radius: 25px;
        font-size: 1.1rem;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }
    
    .btn-activate:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        color: white;
    }
    
    .btn-activate:disabled {
        background: #6c757d;
        transform: none;
        box-shadow: none;
    }
    
    .enrollment-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        border-left: 5px solid #28a745;
        transition: all 0.3s ease;
    }
    
    .enrollment-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    
    .course-title {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }
    
    .course-meta {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .status-badge {
        padding: 0.4rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .status-active {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-pending {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .status-completed {
        background-color: #d1ecf1;
        color: #0c5460;
    }
    
    .help-section {
        background: #f8f9fc;
        border-radius: 15px;
        padding: 2rem;
        margin-top: 2rem;
        border: 1px solid #e3e6f0;
    }
    
    .help-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 1rem;
    }
    
    .help-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 1rem;
        flex-shrink: 0;
    }
    
    .help-content h6 {
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }
    
    .help-content p {
        color: #6c757d;
        margin-bottom: 0;
        font-size: 0.9rem;
    }
    
    .loading-spinner {
        display: none;
        margin-left: 0.5rem;
    }
    
    .success-animation {
        animation: successPulse 0.6s ease-in-out;
    }
    
    @keyframes successPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem 1rem;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="activation-hero">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <h1 class="display-4 mb-3">
                    <i class="fas fa-key me-3"></i>
                    تفعيل الكورس
                </h1>
                <p class="lead mb-0">
                    أدخل كود التفعيل الذي حصلت عليه من مدرسك للانضمام إلى الكورس
                </p>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- نموذج تفعيل الكورس -->
            <div class="activation-card">
                <div class="text-center mb-4">
                    <i class="fas fa-unlock-alt fa-3x text-primary mb-3"></i>
                    <h3 class="mb-2">أدخل كود التفعيل</h3>
                    <p class="text-muted">الكود مكون من 22 حرف وأرقام</p>
                </div>
                
                <form id="activationForm">
                    <div class="code-input-group">
                        <input type="text" 
                               class="form-control code-input" 
                               id="activationCode" 
                               name="code" 
                               placeholder="أدخل كود التفعيل هنا"
                               maxlength="22"
                               required>
                    </div>
                    
                    <div class="text-center">
                        <button type="submit" class="btn btn-activate" id="activateBtn">
                            <i class="fas fa-check me-2"></i>
                            تفعيل الكورس
                            <div class="loading-spinner spinner-border spinner-border-sm" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- الكورسات المسجل بها الطالب -->
            {% if enrollments %}
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-graduation-cap me-2"></i>
                        الكورسات المسجل بها
                    </h5>
                </div>
                <div class="card-body">
                    {% for enrollment in enrollments %}
                    <div class="enrollment-card">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h5 class="course-title">{{ enrollment.course_title or 'كورس غير محدد' }}</h5>
                                <div class="course-meta">
                                    <i class="fas fa-user me-1"></i>
                                    المدرس: {{ enrollment.instructor_name or 'غير محدد' }}
                                    <span class="mx-2">|</span>
                                    <i class="fas fa-calendar me-1"></i>
                                    تاريخ التسجيل: {{ enrollment.enrolled_at[:10] if enrollment.enrolled_at else 'غير محدد' }}
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                {% if enrollment.status == 'active' %}
                                    <span class="status-badge status-active">نشط</span>
                                {% elif enrollment.status == 'completed' %}
                                    <span class="status-badge status-completed">مكتمل</span>
                                {% else %}
                                    <span class="status-badge status-pending">في الانتظار</span>
                                {% endif %}
                                <div class="mt-2">
                                    <a href="/courses/{{ enrollment.course_id }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye me-1"></i>
                                        عرض الكورس
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% else %}
            <div class="card shadow">
                <div class="card-body">
                    <div class="empty-state">
                        <i class="fas fa-graduation-cap"></i>
                        <h5>لم تسجل في أي كورس بعد</h5>
                        <p>استخدم كود التفعيل أعلاه للانضمام إلى أول كورس لك</p>
                    </div>
                </div>
            </div>
            {% endif %}
            
            <!-- قسم المساعدة -->
            <div class="help-section">
                <h4 class="mb-4 text-center">
                    <i class="fas fa-question-circle me-2"></i>
                    كيفية استخدام كود التفعيل
                </h4>
                
                <div class="help-item">
                    <div class="help-icon">
                        <i class="fas fa-1"></i>
                    </div>
                    <div class="help-content">
                        <h6>احصل على الكود</h6>
                        <p>اطلب كود التفعيل من مدرسك أو من إدارة المنصة</p>
                    </div>
                </div>
                
                <div class="help-item">
                    <div class="help-icon">
                        <i class="fas fa-2"></i>
                    </div>
                    <div class="help-content">
                        <h6>أدخل الكود</h6>
                        <p>اكتب الكود في الحقل أعلاه بدقة (22 حرف وأرقام)</p>
                    </div>
                </div>
                
                <div class="help-item">
                    <div class="help-icon">
                        <i class="fas fa-3"></i>
                    </div>
                    <div class="help-content">
                        <h6>ابدأ التعلم</h6>
                        <p>بعد التفعيل الناجح، ستتمكن من الوصول إلى محتوى الكورس</p>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <p class="text-muted mb-0">
                        <i class="fas fa-info-circle me-1"></i>
                        في حالة وجود مشكلة، تواصل مع مدرسك أو الدعم الفني
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تنسيق إدخال الكود
document.getElementById('activationCode').addEventListener('input', function(e) {
    // تحويل إلى أحرف كبيرة وإزالة المسافات
    this.value = this.value.toUpperCase().replace(/\s/g, '');
    
    // تحديد طول الكود
    if (this.value.length > 22) {
        this.value = this.value.substring(0, 22);
    }
});

// معالجة نموذج التفعيل
document.getElementById('activationForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const code = document.getElementById('activationCode').value.trim();
    const activateBtn = document.getElementById('activateBtn');
    const loadingSpinner = activateBtn.querySelector('.loading-spinner');
    
    // التحقق من صحة الكود
    if (!code || code.length !== 22) {
        showAlert('يجب أن يكون كود التفعيل مكون من 22 حرف وأرقام', 'error');
        return;
    }
    
    // تعطيل الزر وإظهار التحميل
    activateBtn.disabled = true;
    loadingSpinner.style.display = 'inline-block';
    
    // إرسال طلب التفعيل
    fetch('/api/activation-codes/use', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code: code })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إظهار رسالة النجاح
            showAlert(data.message, 'success');
            
            // تأثير النجاح
            document.querySelector('.activation-card').classList.add('success-animation');
            
            // مسح الحقل
            document.getElementById('activationCode').value = '';
            
            // إعادة تحميل الصفحة بعد ثانيتين لإظهار الكورس الجديد
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showAlert(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في النظام، يرجى المحاولة مرة أخرى', 'error');
    })
    .finally(() => {
        // إعادة تفعيل الزر وإخفاء التحميل
        activateBtn.disabled = false;
        loadingSpinner.style.display = 'none';
    });
});

// عرض التنبيهات
function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="${icon} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // إضافة التنبيه في أعلى الصفحة
    const container = document.querySelector('.container');
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

// تحسين تجربة المستخدم - التركيز على حقل الإدخال
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('activationCode').focus();
});

// معالجة لصق الكود
document.getElementById('activationCode').addEventListener('paste', function(e) {
    setTimeout(() => {
        this.value = this.value.toUpperCase().replace(/\s/g, '');
        if (this.value.length > 22) {
            this.value = this.value.substring(0, 22);
        }
    }, 10);
});
</script>
{% endblock %}
