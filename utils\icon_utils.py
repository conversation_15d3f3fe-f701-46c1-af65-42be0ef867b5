"""
وظائف مساعدة لإدارة أيقونات التخصصات
Icon management utilities for specializations
"""

import os
import uuid
import logging
from typing import Dict, List, Optional, Tuple, Any
from PIL import Image, ImageOps
from werkzeug.utils import secure_filename
from werkzeug.datastructures import FileStorage
import hashlib
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

class IconManager:
    """مدير أيقونات التخصصات"""
    
    def __init__(self, upload_folder: str = 'static/uploads/specialization_icons'):
        self.upload_folder = upload_folder
        self.allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'svg', 'webp'}
        self.max_file_size = 2 * 1024 * 1024  # 2MB
        self.icon_sizes = {
            'thumbnail': (64, 64),
            'medium': (128, 128),
            'large': (256, 256)
        }
        
        # إنشاء المجلدات إذا لم تكن موجودة
        os.makedirs(self.upload_folder, exist_ok=True)
        for size_name in self.icon_sizes.keys():
            os.makedirs(os.path.join(self.upload_folder, size_name), exist_ok=True)
    
    def is_allowed_file(self, filename: str) -> bool:
        """التحقق من نوع الملف المسموح"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in self.allowed_extensions
    
    def generate_unique_filename(self, original_filename: str) -> str:
        """إنتاج اسم ملف فريد"""
        # الحصول على امتداد الملف
        file_ext = original_filename.rsplit('.', 1)[1].lower() if '.' in original_filename else 'png'
        
        # إنتاج معرف فريد
        unique_id = str(uuid.uuid4())
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        return f"icon_{timestamp}_{unique_id}.{file_ext}"
    
    def validate_image_file(self, file: FileStorage) -> Tuple[bool, str]:
        """التحقق من صحة ملف الصورة"""
        try:
            # التحقق من وجود الملف
            if not file or not file.filename:
                return False, "لم يتم اختيار ملف"
            
            # التحقق من نوع الملف
            if not self.is_allowed_file(file.filename):
                return False, f"نوع الملف غير مدعوم. الأنواع المدعومة: {', '.join(self.allowed_extensions)}"
            
            # التحقق من حجم الملف
            file.seek(0, 2)  # الانتقال لنهاية الملف
            file_size = file.tell()
            file.seek(0)  # العودة لبداية الملف
            
            if file_size > self.max_file_size:
                return False, f"حجم الملف كبير جداً. الحد الأقصى: {self.max_file_size // (1024*1024)}MB"
            
            # التحقق من صحة الصورة
            try:
                image = Image.open(file)
                image.verify()
                file.seek(0)  # إعادة تعيين مؤشر الملف
                return True, "الملف صالح"
            except Exception as e:
                return False, f"الملف ليس صورة صالحة: {str(e)}"
                
        except Exception as e:
            logger.error(f"خطأ في التحقق من الملف: {e}")
            return False, f"خطأ في التحقق من الملف: {str(e)}"
    
    def process_and_save_icon(self, file: FileStorage, filename: str) -> Dict[str, Any]:
        """معالجة وحفظ الأيقونة بأحجام مختلفة"""
        try:
            # فتح الصورة
            image = Image.open(file)
            
            # تحويل إلى RGB إذا كانت RGBA
            if image.mode in ('RGBA', 'LA'):
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'RGBA':
                    background.paste(image, mask=image.split()[-1])
                else:
                    background.paste(image, mask=image.split()[-1])
                image = background
            elif image.mode != 'RGB':
                image = image.convert('RGB')
            
            # حفظ الصورة الأصلية
            original_path = os.path.join(self.upload_folder, filename)
            image.save(original_path, 'JPEG', quality=90, optimize=True)
            
            # إنشاء أحجام مختلفة
            sizes_info = {}
            for size_name, (width, height) in self.icon_sizes.items():
                # تغيير حجم الصورة مع الحفاظ على النسبة
                resized_image = ImageOps.fit(image, (width, height), Image.Resampling.LANCZOS)
                
                # حفظ الحجم المحدد
                size_filename = f"{size_name}_{filename}"
                size_path = os.path.join(self.upload_folder, size_name, size_filename)
                resized_image.save(size_path, 'JPEG', quality=85, optimize=True)
                
                sizes_info[size_name] = {
                    'filename': size_filename,
                    'path': size_path,
                    'url': f'/static/uploads/specialization_icons/{size_name}/{size_filename}',
                    'size': (width, height)
                }
            
            # معلومات الصورة الأصلية
            original_info = {
                'filename': filename,
                'path': original_path,
                'url': f'/static/uploads/specialization_icons/{filename}',
                'size': image.size,
                'file_size': os.path.getsize(original_path)
            }
            
            return {
                'success': True,
                'original': original_info,
                'sizes': sizes_info,
                'message': 'تم رفع الأيقونة بنجاح'
            }
            
        except Exception as e:
            logger.error(f"خطأ في معالجة الصورة: {e}")
            return {
                'success': False,
                'message': f'خطأ في معالجة الصورة: {str(e)}'
            }
    
    def delete_icon_files(self, filename: str) -> bool:
        """حذف ملفات الأيقونة من جميع الأحجام"""
        try:
            # حذف الملف الأصلي
            original_path = os.path.join(self.upload_folder, filename)
            if os.path.exists(original_path):
                os.remove(original_path)
            
            # حذف الأحجام المختلفة
            for size_name in self.icon_sizes.keys():
                size_filename = f"{size_name}_{filename}"
                size_path = os.path.join(self.upload_folder, size_name, size_filename)
                if os.path.exists(size_path):
                    os.remove(size_path)
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حذف ملفات الأيقونة: {e}")
            return False
    
    def get_icon_info(self, filename: str) -> Optional[Dict[str, Any]]:
        """الحصول على معلومات الأيقونة"""
        try:
            original_path = os.path.join(self.upload_folder, filename)
            if not os.path.exists(original_path):
                return None
            
            # معلومات الملف الأصلي
            file_size = os.path.getsize(original_path)
            
            # معلومات الأحجام المختلفة
            sizes_info = {}
            for size_name in self.icon_sizes.keys():
                size_filename = f"{size_name}_{filename}"
                size_path = os.path.join(self.upload_folder, size_name, size_filename)
                if os.path.exists(size_path):
                    sizes_info[size_name] = {
                        'filename': size_filename,
                        'url': f'/static/uploads/specialization_icons/{size_name}/{size_filename}',
                        'exists': True
                    }
                else:
                    sizes_info[size_name] = {'exists': False}
            
            return {
                'filename': filename,
                'url': f'/static/uploads/specialization_icons/{filename}',
                'file_size': file_size,
                'sizes': sizes_info,
                'exists': True
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على معلومات الأيقونة: {e}")
            return None
    
    def list_available_icons(self) -> List[Dict[str, Any]]:
        """الحصول على قائمة بجميع الأيقونات المتاحة"""
        try:
            icons = []
            if not os.path.exists(self.upload_folder):
                return icons
            
            for filename in os.listdir(self.upload_folder):
                file_path = os.path.join(self.upload_folder, filename)
                if os.path.isfile(file_path) and self.is_allowed_file(filename):
                    icon_info = self.get_icon_info(filename)
                    if icon_info:
                        icons.append(icon_info)
            
            return icons
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على قائمة الأيقونات: {e}")
            return []

# إنشاء مثيل عام من مدير الأيقونات
icon_manager = IconManager()
