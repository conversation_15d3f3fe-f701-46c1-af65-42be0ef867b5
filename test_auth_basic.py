"""
اختبار أساسي لنظام المصادقة
Basic Authentication System Test
"""

import sys
import os
from flask import Flask

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار استيراد الوحدات"""
    print("🔍 اختبار استيراد الوحدات...")
    
    try:
        from utils.jwt_utils import JWTManager, get_jwt_manager
        print("✅ تم استيراد JWT Utils بنجاح")
    except Exception as e:
        print(f"❌ فشل في استيراد JWT Utils: {e}")
        return False
    
    try:
        from utils.auth_utils import AuthManager, get_auth_manager
        print("✅ تم استيراد Auth Utils بنجاح")
    except Exception as e:
        print(f"❌ فشل في استيراد Auth Utils: {e}")
        return False
    
    try:
        from utils.auth_middleware import AuthMiddleware, get_auth_middleware
        print("✅ تم استيراد Auth Middleware بنجاح")
    except Exception as e:
        print(f"❌ فشل في استيراد Auth Middleware: {e}")
        return False
    
    return True

def test_jwt_basic():
    """اختبار أساسي لـ JWT"""
    print("\n🔐 اختبار JWT Manager...")
    
    try:
        from utils.jwt_utils import JWTManager
        from config import Config
        
        # إنشاء تطبيق Flask للاختبار
        app = Flask(__name__)
        app.config.from_object(Config)
        
        with app.app_context():
            jwt_manager = JWTManager()
            
            # بيانات مستخدم للاختبار
            test_user = {
                'user_id': 'test_123',
                'email': '<EMAIL>',
                'role': 'student',
                'first_name': 'أحمد',
                'last_name': 'محمد'
            }
            
            # إنشاء توكين
            token = jwt_manager.generate_token(test_user)
            if not token:
                print("❌ فشل في إنشاء JWT token")
                return False
            
            print(f"✅ تم إنشاء JWT token: {token[:50]}...")
            
            # التحقق من التوكين
            payload = jwt_manager.verify_token(token)
            if not payload:
                print("❌ فشل في التحقق من JWT token")
                return False
            
            print("✅ تم التحقق من JWT token بنجاح")
            
            # استخراج بيانات المستخدم
            user_data = jwt_manager.get_user_from_token(token)
            if not user_data or user_data['email'] != test_user['email']:
                print("❌ فشل في استخراج بيانات المستخدم من التوكين")
                return False
            
            print("✅ تم استخراج بيانات المستخدم بنجاح")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار JWT: {e}")
        return False
    
    return True

def test_auth_basic():
    """اختبار أساسي للمصادقة"""
    print("\n🔒 اختبار Auth Manager...")
    
    try:
        from utils.auth_utils import AuthManager
        from config import Config
        
        # إنشاء تطبيق Flask للاختبار
        app = Flask(__name__)
        app.config.from_object(Config)
        
        with app.app_context():
            auth_manager = AuthManager()
            
            # اختبار تشفير كلمة المرور
            password = "test_password_123"
            hashed = auth_manager.hash_password(password)
            
            if not hashed or hashed == password:
                print("❌ فشل في تشفير كلمة المرور")
                return False
            
            print("✅ تم تشفير كلمة المرور بنجاح")
            
            # اختبار التحقق من كلمة المرور
            if not auth_manager.verify_password(password, hashed):
                print("❌ فشل في التحقق من كلمة المرور الصحيحة")
                return False
            
            if auth_manager.verify_password("wrong_password", hashed):
                print("❌ تم قبول كلمة مرور خاطئة")
                return False
            
            print("✅ تم التحقق من كلمة المرور بنجاح")
            
            # اختبار توليد كلمة مرور عشوائية
            random_password = auth_manager.generate_random_password(12)
            if not random_password or len(random_password) != 12:
                print("❌ فشل في توليد كلمة مرور عشوائية")
                return False
            
            print(f"✅ تم توليد كلمة مرور عشوائية: {random_password}")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار Auth Manager: {e}")
        return False
    
    return True

def test_middleware_basic():
    """اختبار أساسي للـ middleware"""
    print("\n🛡️ اختبار Auth Middleware...")
    
    try:
        from utils.auth_middleware import AuthMiddleware
        
        auth_middleware = AuthMiddleware()
        print("✅ تم إنشاء Auth Middleware بنجاح")
        
        # اختبار الـ decorators
        from utils.auth_middleware import login_required, admin_required, api_auth_required
        print("✅ تم استيراد decorators بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار Auth Middleware: {e}")
        return False
    
    return True

def test_app_routes():
    """اختبار routes التطبيق"""
    print("\n🌐 اختبار routes التطبيق...")
    
    try:
        from app import create_app
        
        app = create_app('testing')
        client = app.test_client()
        
        # اختبار الصفحة الرئيسية
        response = client.get('/')
        if response.status_code != 200:
            print(f"❌ فشل في الوصول للصفحة الرئيسية: {response.status_code}")
            return False
        
        print("✅ الصفحة الرئيسية تعمل بنجاح")
        
        # اختبار صفحة تسجيل الدخول
        response = client.get('/login')
        if response.status_code != 200:
            print(f"❌ فشل في الوصول لصفحة تسجيل الدخول: {response.status_code}")
            return False
        
        print("✅ صفحة تسجيل الدخول تعمل بنجاح")
        
        # اختبار API health check
        response = client.get('/api/health')
        if response.status_code != 200:
            print(f"❌ فشل في API health check: {response.status_code}")
            return False
        
        print("✅ API health check يعمل بنجاح")
        
        # اختبار API login مع بيانات ناقصة
        response = client.post('/api/auth/login',
                             json={},
                             content_type='application/json')
        if response.status_code != 400:
            print(f"❌ API login لم يرفض البيانات الناقصة: {response.status_code}")
            return False
        
        print("✅ API login يرفض البيانات الناقصة بشكل صحيح")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار routes التطبيق: {e}")
        return False
    
    return True

def main():
    """تشغيل جميع الاختبارات الأساسية"""
    print("🚀 بدء الاختبارات الأساسية لنظام المصادقة")
    print("=" * 60)
    
    tests = [
        ("استيراد الوحدات", test_imports),
        ("JWT Manager", test_jwt_basic),
        ("Auth Manager", test_auth_basic),
        ("Auth Middleware", test_middleware_basic),
        ("App Routes", test_app_routes)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✅ {test_name}: نجح")
            else:
                print(f"\n❌ {test_name}: فشل")
        except Exception as e:
            print(f"\n💥 {test_name}: خطأ - {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 النتائج النهائية:")
    print(f"   - الاختبارات الناجحة: {passed}/{total}")
    print(f"   - معدل النجاح: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات الأساسية نجحت!")
        print("✅ نظام المصادقة جاهز للاستخدام")
        return True
    else:
        print(f"\n⚠️ {total - passed} اختبار(ات) فشل")
        print("🔧 يرجى مراجعة الأخطاء وإصلاحها")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
