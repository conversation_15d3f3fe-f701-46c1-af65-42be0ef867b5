{% extends "base.html" %}

{% block title %}البحث المتقدم في الكورسات{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/advanced-search.css') }}">
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">البحث المتقدم في الكورسات</h1>
                    <p class="text-muted">ابحث عن الكورسات باستخدام فلاتر متقدمة</p>
                </div>
                <div>
                    <a href="{{ url_for('courses') }}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للكورسات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- حاوي البحث المتقدم -->
    <div class="advanced-search-container">
        <div class="search-header">
            <h2>ابحث عن الكورس المناسب لك</h2>
            <p>استخدم الفلاتر أدناه للعثور على الكورسات التي تناسب احتياجاتك</p>
        </div>

        <!-- شريط البحث الرئيسي -->
        <div class="main-search-bar">
            <div class="search-input-group">
                <i class="fas fa-search search-icon"></i>
                <input type="text" id="searchInput" placeholder="ابحث في عنوان الكورس، الوصف، أو اسم المدرس..." autocomplete="off">
                <button type="button" class="search-btn" id="searchBtn">
                    <i class="fas fa-search me-2"></i>
                    بحث
                </button>
            </div>
        </div>

        <!-- فلاتر متقدمة -->
        <div class="advanced-filters">
            <button class="filters-toggle" type="button" data-bs-toggle="collapse" data-bs-target="#advancedFiltersCollapse" aria-expanded="false">
                <i class="fas fa-filter"></i>
                فلاتر متقدمة
                <i class="fas fa-chevron-down"></i>
            </button>

            <div class="collapse" id="advancedFiltersCollapse">
                <div class="row g-3 mt-2">
                    <!-- فلتر التخصص -->
                    <div class="col-md-3">
                        <div class="filter-group">
                            <label for="specializationFilter">التخصص</label>
                            <select class="form-select search-filter" name="specialization" id="specializationFilter">
                                <option value="">جميع التخصصات</option>
                            </select>
                        </div>
                    </div>

                    <!-- فلتر المرحلة -->
                    <div class="col-md-3">
                        <div class="filter-group">
                            <label for="stageFilter">المرحلة</label>
                            <select class="form-select search-filter" name="stage" id="stageFilter">
                                <option value="">جميع المراحل</option>
                                <option value="2">المرحلة الثانية</option>
                                <option value="3">المرحلة الثالثة</option>
                                <option value="4">المرحلة الرابعة</option>
                                <option value="general">عام</option>
                            </select>
                        </div>
                    </div>

                    <!-- فلتر المدرس -->
                    <div class="col-md-3">
                        <div class="filter-group">
                            <label for="instructorFilter">المدرس</label>
                            <select class="form-select search-filter" name="instructor" id="instructorFilter">
                                <option value="">جميع المدرسين</option>
                            </select>
                        </div>
                    </div>

                    <!-- فلتر الحالة -->
                    {% if session.role == 'student' %}
                    <div class="col-md-3">
                        <div class="filter-group">
                            <label for="statusFilter">الحالة</label>
                            <select class="form-select search-filter" name="status" id="statusFilter">
                                <option value="">الكل</option>
                                <option value="enrolled">مسجل فيها</option>
                                <option value="available">متاحة للتسجيل</option>
                            </select>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- أزرار الترتيب -->
                <div class="sort-controls">
                    <span class="me-3 fw-bold">ترتيب حسب:</span>
                    <button type="button" class="sort-btn active desc" data-sort-by="created_at">
                        تاريخ الإنشاء
                    </button>
                    <button type="button" class="sort-btn" data-sort-by="title">
                        العنوان
                    </button>
                    <button type="button" class="sort-btn" data-sort-by="instructor">
                        المدرس
                    </button>
                    <button type="button" class="sort-btn" data-sort-by="specialization">
                        التخصص
                    </button>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="search-actions">
                    <button type="button" class="action-btn clear-filters-btn">
                        <i class="fas fa-times me-2"></i>
                        مسح الفلاتر
                    </button>
                    <button type="button" class="action-btn export-results-btn">
                        <i class="fas fa-download me-2"></i>
                        تصدير النتائج
                    </button>
                    <button type="button" class="action-btn" data-bs-toggle="modal" data-bs-target="#favoritesModal">
                        <i class="fas fa-star me-2"></i>
                        البحثات المفضلة
                    </button>
                    <button type="button" class="action-btn" id="saveSearchBtn">
                        <i class="fas fa-bookmark me-2"></i>
                        حفظ البحث
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات البحث -->
    <div id="searchStats"></div>

    <!-- نتائج البحث -->
    <div class="search-results">
        <div id="searchResults">
            <!-- سيتم تحميل النتائج هنا -->
        </div>
    </div>

    <!-- التصفح -->
    <div id="searchPagination"></div>
</div>

<!-- Modal البحثات المفضلة -->
<div class="modal fade" id="favoritesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">البحثات المفضلة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="favoritesList">
                    <!-- سيتم تحميل البحثات المفضلة هنا -->
                </div>

                <div class="text-center mt-3" id="noFavoritesMessage" style="display: none;">
                    <i class="fas fa-star fa-3x text-muted mb-3"></i>
                    <h5>لا توجد بحثات مفضلة</h5>
                    <p class="text-muted">احفظ بحثاتك المفضلة للوصول السريع إليها</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-primary" id="exportFavoritesBtn">
                    <i class="fas fa-download me-2"></i>
                    تصدير
                </button>
                <button type="button" class="btn btn-outline-secondary" id="importFavoritesBtn">
                    <i class="fas fa-upload me-2"></i>
                    استيراد
                </button>
                <input type="file" id="importFavoritesFile" accept=".json" style="display: none;">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal حفظ البحث -->
<div class="modal fade" id="saveSearchModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">حفظ البحث</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="searchName" class="form-label">اسم البحث</label>
                    <input type="text" class="form-control" id="searchName" placeholder="أدخل اسماً للبحث">
                </div>
                <div class="mb-3">
                    <label class="form-label">معاينة البحث</label>
                    <div class="bg-light p-3 rounded">
                        <div id="searchPreview">
                            <!-- سيتم عرض معاينة البحث هنا -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmSaveSearch">حفظ</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/advanced-search.js') }}"></script>
<script>
$(document).ready(function() {
    // تحميل التخصصات
    loadSpecializations();
    
    // تحميل المدرسين
    loadInstructors();

    // تهيئة مدير البحث المتقدم
    const searchManager = new AdvancedSearchManager({
        apiEndpoint: '/api/courses/search',
        searchInputId: 'searchInput',
        resultsContainerId: 'searchResults',
        filtersContainerId: 'advancedFiltersCollapse',
        statsContainerId: 'searchStats',
        debounceDelay: 300,
        saveFilters: true,
        updateUrl: true
    });

    // تحديث الفلاتر عند تغيير التخصصات
    $('#specializationFilter').on('change', function() {
        const selectedSpecialization = $(this).val();
        updateStageFilter(selectedSpecialization);
    });

    // إدارة البحثات المفضلة
    setupFavoritesManagement();
});

async function loadSpecializations() {
    try {
        const response = await fetch('/api/specializations');
        const data = await response.json();
        
        if (data.success) {
            const select = $('#specializationFilter');
            data.specializations.forEach(spec => {
                if (spec.active) {
                    select.append(`<option value="${spec.id}">${spec.name}</option>`);
                }
            });
        }
    } catch (error) {
        console.error('خطأ في تحميل التخصصات:', error);
    }
}

async function loadInstructors() {
    try {
        const response = await fetch('/api/instructors');
        const data = await response.json();
        
        if (data.success) {
            const select = $('#instructorFilter');
            data.instructors.forEach(instructor => {
                select.append(`<option value="${instructor.id}">${instructor.name}</option>`);
            });
        }
    } catch (error) {
        console.error('خطأ في تحميل المدرسين:', error);
    }
}

function updateStageFilter(specializationId) {
    const stageFilter = $('#stageFilter');
    
    if (!specializationId) {
        // إظهار جميع المراحل
        stageFilter.find('option').show();
        return;
    }

    // هنا يمكن تحديد المراحل المتاحة حسب التخصص
    // حالياً سنعرض جميع المراحل
    stageFilter.find('option').show();
}

function setupFavoritesManagement() {
    // حفظ البحث
    $('#saveSearchBtn').on('click', function() {
        const searchState = searchManager.getSearchState();

        // إنشاء معاينة البحث
        let preview = '';
        if (searchState.query) {
            preview += `<strong>البحث:</strong> ${searchState.query}<br>`;
        }

        Object.keys(searchState.filters).forEach(key => {
            if (searchState.filters[key]) {
                const filterName = getFilterDisplayName(key);
                const filterValue = getFilterDisplayValue(key, searchState.filters[key]);
                preview += `<strong>${filterName}:</strong> ${filterValue}<br>`;
            }
        });

        if (!preview) {
            preview = 'بحث عام (بدون فلاتر)';
        }

        $('#searchPreview').html(preview);
        $('#saveSearchModal').modal('show');
    });

    // تأكيد حفظ البحث
    $('#confirmSaveSearch').on('click', function() {
        const searchName = $('#searchName').val().trim();

        if (!searchName) {
            showAlert('يرجى إدخال اسم للبحث', 'warning');
            return;
        }

        const favoriteId = searchManager.saveSearchAsFavorite(searchName);
        showAlert('تم حفظ البحث بنجاح', 'success');

        $('#saveSearchModal').modal('hide');
        $('#searchName').val('');

        // تحديث قائمة المفضلة إذا كانت مفتوحة
        if ($('#favoritesModal').hasClass('show')) {
            loadFavoritesList();
        }
    });

    // عرض البحثات المفضلة
    $('#favoritesModal').on('show.bs.modal', function() {
        loadFavoritesList();
    });

    // تصدير المفضلة
    $('#exportFavoritesBtn').on('click', function() {
        searchManager.exportFavorites();
    });

    // استيراد المفضلة
    $('#importFavoritesBtn').on('click', function() {
        $('#importFavoritesFile').click();
    });

    $('#importFavoritesFile').on('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            searchManager.importFavorites(file)
                .then(() => {
                    showAlert('تم استيراد البحثات المفضلة بنجاح', 'success');
                    loadFavoritesList();
                })
                .catch(error => {
                    showAlert('فشل في استيراد البحثات المفضلة', 'danger');
                    console.error(error);
                });
        }
    });
}

function loadFavoritesList() {
    const favorites = searchManager.getFavoriteSearches();
    const container = $('#favoritesList');

    if (favorites.length === 0) {
        container.html('');
        $('#noFavoritesMessage').show();
        return;
    }

    $('#noFavoritesMessage').hide();

    let html = '';
    favorites.forEach(favorite => {
        const createdDate = new Date(favorite.createdAt).toLocaleDateString('ar-SA');

        html += `
            <div class="card mb-3">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="card-title">${favorite.name}</h6>
                            <small class="text-muted">تم الحفظ في: ${createdDate}</small>
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-primary load-favorite-btn" data-id="${favorite.id}">
                                <i class="fas fa-search"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger delete-favorite-btn" data-id="${favorite.id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>

                    <div class="mt-2">
                        ${createFavoritePreview(favorite)}
                    </div>
                </div>
            </div>
        `;
    });

    container.html(html);

    // ربط الأحداث
    $('.load-favorite-btn').on('click', function() {
        const favoriteId = $(this).data('id');
        searchManager.loadFavoriteSearch(favoriteId);
        $('#favoritesModal').modal('hide');
        showAlert('تم تحميل البحث المفضل', 'success');
    });

    $('.delete-favorite-btn').on('click', function() {
        const favoriteId = $(this).data('id');
        if (confirm('هل أنت متأكد من حذف هذا البحث المفضل؟')) {
            searchManager.deleteFavoriteSearch(favoriteId);
            loadFavoritesList();
            showAlert('تم حذف البحث المفضل', 'info');
        }
    });
}

function createFavoritePreview(favorite) {
    let preview = '';

    if (favorite.query) {
        preview += `<span class="badge bg-primary me-1">البحث: ${favorite.query}</span>`;
    }

    Object.keys(favorite.filters).forEach(key => {
        if (favorite.filters[key]) {
            const filterName = getFilterDisplayName(key);
            const filterValue = getFilterDisplayValue(key, favorite.filters[key]);
            preview += `<span class="badge bg-secondary me-1">${filterName}: ${filterValue}</span>`;
        }
    });

    return preview || '<span class="text-muted">بحث عام</span>';
}

function getFilterDisplayName(key) {
    const names = {
        'specialization': 'التخصص',
        'stage': 'المرحلة',
        'instructor': 'المدرس',
        'status': 'الحالة',
        'sortBy': 'الترتيب'
    };
    return names[key] || key;
}

function getFilterDisplayValue(key, value) {
    if (key === 'stage') {
        const stages = {
            '2': 'المرحلة الثانية',
            '3': 'المرحلة الثالثة',
            '4': 'المرحلة الرابعة',
            'general': 'عام'
        };
        return stages[value] || value;
    }

    if (key === 'status') {
        const statuses = {
            'enrolled': 'مسجل فيها',
            'available': 'متاحة للتسجيل'
        };
        return statuses[value] || value;
    }

    return value;
}

// دالة لعرض رسائل التنبيه
function showAlert(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // إضافة التنبيه في أعلى الصفحة
    $('.container-fluid').prepend(alertHtml);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
{% endblock %}
