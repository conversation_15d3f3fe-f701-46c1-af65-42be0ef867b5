"""
نماذج قاعدة البيانات لمنصة الكورسات
Database models for the courses platform
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from enum import Enum

class UserRole(Enum):
    """أدوار المستخدمين"""
    ADMIN = "admin"
    INSTRUCTOR = "instructor"
    STUDENT = "student"

class CourseStatus(Enum):
    """حالات الكورس"""
    DRAFT = "draft"
    PUBLISHED = "published"
    ARCHIVED = "archived"

class EnrollmentStatus(Enum):
    """حالات التسجيل"""
    ACTIVE = "active"
    COMPLETED = "completed"
    SUSPENDED = "suspended"

class DatabaseModels:
    """فئة لتعريف نماذج قاعدة البيانات"""
    
    @staticmethod
    def create_user_model(
        email: str,
        telegram_id: str,
        role: UserRole,
        first_name: str,
        last_name: str = "",
        specialization_id: str = None,
        permissions: Dict[str, Any] = None,
        active: bool = True
    ) -> Dict[str, Any]:
        """إنشاء نموذج مستخدم"""
        
        user_data = {
            'email': email,
            'telegram_id': str(telegram_id),
            'role': role.value,
            'first_name': first_name,
            'last_name': last_name,
            'full_name': f"{first_name} {last_name}".strip(),
            'active': active,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat(),
            'last_login': None,
            'profile': {
                'avatar_url': None,
                'bio': "",
                'phone': None
            }
        }
        
        # إضافة التخصص للمدرسين والطلاب
        if role in [UserRole.INSTRUCTOR, UserRole.STUDENT] and specialization_id:
            user_data['specialization_id'] = specialization_id
        
        # إضافة الصلاحيات للمدرسين
        if role == UserRole.INSTRUCTOR:
            user_data['permissions'] = permissions or {
                'can_create_courses': True,
                'can_manage_students': True,
                'allowed_stages': [2, 3, 4],  # جميع المراحل افتراضياً
                'can_create_general_courses': False
            }
        
        return user_data
    
    @staticmethod
    def create_specialization_model(
        name: str,
        name_en: str,
        icon: str,
        description: str = "",
        stages: List[int] = None,
        active: bool = True,
        icon_type: str = "font_awesome",
        icon_url: str = None,
        color: str = "#667eea"
    ) -> Dict[str, Any]:
        """إنشاء نموذج تخصص"""

        return {
            'name': name,
            'name_en': name_en,
            'icon': icon,
            'icon_type': icon_type,  # font_awesome أو uploaded
            'icon_url': icon_url,    # رابط الصورة المرفوعة
            'color': color,          # لون التخصص
            'description': description,
            'stages': stages or [2, 3, 4],
            'active': active,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat(),
            'courses_count': 0,
            'students_count': 0
        }

    @staticmethod
    def create_specialization_icon_model(
        filename: str,
        original_name: str,
        file_size: int,
        uploaded_by: str,
        description: str = ""
    ) -> Dict[str, Any]:
        """إنشاء نموذج أيقونة تخصص مرفوعة"""

        return {
            'filename': filename,
            'original_name': original_name,
            'file_size': file_size,
            'file_url': f'/static/uploads/specialization_icons/{filename}',
            'uploaded_by': uploaded_by,
            'description': description,
            'active': True,
            'usage_count': 0,  # عدد التخصصات التي تستخدم هذه الأيقونة
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
    
    @staticmethod
    def create_course_model(
        title: str,
        instructor_id: str,
        specialization_id: str,
        stage: int,
        description: str = "",
        thumbnail_url: str = None,
        status: CourseStatus = CourseStatus.DRAFT,
        is_general: bool = False
    ) -> Dict[str, Any]:
        """إنشاء نموذج كورس"""
        
        return {
            'title': title,
            'description': description,
            'instructor_id': instructor_id,
            'specialization_id': specialization_id if not is_general else None,
            'stage': stage,
            'is_general': is_general,
            'status': status.value,
            'thumbnail_url': thumbnail_url,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat(),
            'published_at': None,
            'settings': {
                'allow_comments': True,
                'allow_downloads': False,
                'require_completion_order': True
            },
            'stats': {
                'total_lessons': 0,
                'total_duration': 0,
                'enrolled_students': 0,
                'completion_rate': 0
            }
        }
    
    @staticmethod
    def create_lesson_model(
        course_id: str,
        title: str,
        content_type: str,  # video, text, quiz, assignment
        content_data: Dict[str, Any],
        order: int = 1,
        duration: int = 0
    ) -> Dict[str, Any]:
        """إنشاء نموذج درس"""
        
        return {
            'course_id': course_id,
            'title': title,
            'content_type': content_type,
            'content_data': content_data,
            'order': order,
            'duration': duration,  # بالثواني
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat(),
            'settings': {
                'is_preview': False,
                'require_previous_completion': True
            }
        }
    
    @staticmethod
    def create_enrollment_model(
        student_id: str,
        course_id: str,
        status: EnrollmentStatus = EnrollmentStatus.ACTIVE
    ) -> Dict[str, Any]:
        """إنشاء نموذج تسجيل"""
        
        return {
            'student_id': student_id,
            'course_id': course_id,
            'status': status.value,
            'enrolled_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat(),
            'completed_at': None,
            'progress': {
                'completed_lessons': [],
                'current_lesson': None,
                'completion_percentage': 0,
                'total_watch_time': 0
            }
        }
    
    @staticmethod
    def create_activation_code_model(
        code: str,
        course_id: str,
        created_by: str,
        max_uses: int = 1,
        expires_at: datetime = None
    ) -> Dict[str, Any]:
        """إنشاء نموذج كود تفعيل"""
        
        return {
            'code': code,
            'course_id': course_id,
            'created_by': created_by,
            'max_uses': max_uses,
            'current_uses': 0,
            'active': True,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'expires_at': expires_at.isoformat() if expires_at else None,
            'used_by': []  # قائمة معرفات المستخدمين الذين استخدموا الكود
        }
    
    @staticmethod
    def create_invitation_link_model(
        link_id: str,
        created_by: str,
        link_type: str,  # instructor_signup, student_invite
        target_data: Dict[str, Any],
        max_uses: int = 1,
        expires_at: datetime = None
    ) -> Dict[str, Any]:
        """إنشاء نموذج رابط دعوة"""
        
        return {
            'link_id': link_id,
            'created_by': created_by,
            'link_type': link_type,
            'target_data': target_data,  # بيانات إضافية حسب نوع الرابط
            'max_uses': max_uses,
            'current_uses': 0,
            'active': True,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'expires_at': expires_at.isoformat() if expires_at else None,
            'used_by': []
        }
    
    @staticmethod
    def get_database_schema() -> Dict[str, Any]:
        """الحصول على مخطط قاعدة البيانات الكامل"""
        
        return {
            'users': {
                'description': 'بيانات المستخدمين (أدمن، مدرسين، طلاب)',
                'fields': {
                    'email': 'string - البريد الإلكتروني',
                    'telegram_id': 'string - معرف التليجرام',
                    'role': 'string - الدور (admin/instructor/student)',
                    'first_name': 'string - الاسم الأول',
                    'last_name': 'string - الاسم الأخير',
                    'full_name': 'string - الاسم الكامل',
                    'specialization_id': 'string - معرف التخصص',
                    'permissions': 'object - الصلاحيات',
                    'active': 'boolean - حالة النشاط',
                    'created_at': 'string - تاريخ الإنشاء',
                    'updated_at': 'string - تاريخ التحديث',
                    'last_login': 'string - آخر تسجيل دخول',
                    'profile': 'object - بيانات الملف الشخصي'
                }
            },
            'specializations': {
                'description': 'التخصصات المتاحة',
                'fields': {
                    'name': 'string - اسم التخصص بالعربية',
                    'name_en': 'string - اسم التخصص بالإنجليزية',
                    'icon': 'string - أيقونة التخصص',
                    'description': 'string - وصف التخصص',
                    'stages': 'array - المراحل المتاحة',
                    'active': 'boolean - حالة النشاط',
                    'created_at': 'string - تاريخ الإنشاء',
                    'courses_count': 'number - عدد الكورسات',
                    'students_count': 'number - عدد الطلاب'
                }
            },
            'courses': {
                'description': 'الكورسات التعليمية',
                'fields': {
                    'title': 'string - عنوان الكورس',
                    'description': 'string - وصف الكورس',
                    'instructor_id': 'string - معرف المدرس',
                    'specialization_id': 'string - معرف التخصص',
                    'stage': 'number - المرحلة الدراسية',
                    'is_general': 'boolean - كورس عام',
                    'status': 'string - حالة الكورس',
                    'thumbnail_url': 'string - رابط الصورة المصغرة',
                    'settings': 'object - إعدادات الكورس',
                    'stats': 'object - إحصائيات الكورس'
                }
            },
            'lessons': {
                'description': 'دروس الكورسات',
                'fields': {
                    'course_id': 'string - معرف الكورس',
                    'title': 'string - عنوان الدرس',
                    'content_type': 'string - نوع المحتوى',
                    'content_data': 'object - بيانات المحتوى',
                    'order': 'number - ترتيب الدرس',
                    'duration': 'number - مدة الدرس بالثواني'
                }
            },
            'enrollments': {
                'description': 'تسجيلات الطلاب في الكورسات',
                'fields': {
                    'student_id': 'string - معرف الطالب',
                    'course_id': 'string - معرف الكورس',
                    'status': 'string - حالة التسجيل',
                    'progress': 'object - تقدم الطالب'
                }
            },
            'activation_codes': {
                'description': 'أكواد تفعيل الكورسات',
                'fields': {
                    'code': 'string - الكود',
                    'course_id': 'string - معرف الكورس',
                    'created_by': 'string - منشئ الكود',
                    'max_uses': 'number - الحد الأقصى للاستخدام',
                    'current_uses': 'number - الاستخدام الحالي',
                    'expires_at': 'string - تاريخ الانتهاء'
                }
            },
            'invitation_links': {
                'description': 'روابط الدعوة',
                'fields': {
                    'link_id': 'string - معرف الرابط',
                    'created_by': 'string - منشئ الرابط',
                    'link_type': 'string - نوع الرابط',
                    'target_data': 'object - بيانات الهدف',
                    'max_uses': 'number - الحد الأقصى للاستخدام'
                }
            }
        }
