"""
نظام الإشعارات لأكواد التفعيل
Notification system for activation codes
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Tuple
import requests
import json

# إعداد التسجيل
logger = logging.getLogger(__name__)

class NotificationManager:
    """مدير الإشعارات"""
    
    def __init__(self, firebase_manager=None, bot_token: str = None):
        """
        تهيئة مدير الإشعارات
        
        Args:
            firebase_manager: مدير Firebase
            bot_token: رمز بوت التليجرام
        """
        self.firebase_manager = firebase_manager
        self.bot_token = bot_token
        
        # إعدادات الإشعارات
        self.config = {
            'telegram_enabled': bool(bot_token),
            'email_enabled': False,  # يمكن تفعيلها لاحقاً
            'notification_days_before': [7, 3, 1],  # إشعارات قبل 7، 3، 1 أيام
            'daily_summary_enabled': True,
            'admin_notifications_enabled': True
        }
    
    def send_expiration_notifications(self) -> Dict[str, int]:
        """إرسال إشعارات انتهاء الصلاحية"""
        try:
            if not self.firebase_manager:
                logger.warning("مدير Firebase غير متاح")
                return {'sent': 0, 'failed': 0}
            
            results = {'sent': 0, 'failed': 0}
            
            # إرسال إشعارات للمدرسين
            instructor_results = self._send_instructor_notifications()
            results['sent'] += instructor_results['sent']
            results['failed'] += instructor_results['failed']
            
            # إرسال إشعارات للأدمن
            if self.config['admin_notifications_enabled']:
                admin_results = self._send_admin_notifications()
                results['sent'] += admin_results['sent']
                results['failed'] += admin_results['failed']
            
            return results
            
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعارات انتهاء الصلاحية: {e}")
            return {'sent': 0, 'failed': 0}
    
    def _send_instructor_notifications(self) -> Dict[str, int]:
        """إرسال إشعارات للمدرسين"""
        try:
            results = {'sent': 0, 'failed': 0}
            
            # الحصول على جميع المدرسين
            instructors = self._get_all_instructors()
            
            for instructor in instructors:
                instructor_id = instructor.get('id')
                if not instructor_id:
                    continue
                
                # الحصول على الأكواد المنتهية قريباً للمدرس
                expiring_codes = self._get_instructor_expiring_codes(instructor_id)
                
                if expiring_codes:
                    # إرسال الإشعار
                    success = self._send_instructor_notification(instructor, expiring_codes)
                    if success:
                        results['sent'] += 1
                    else:
                        results['failed'] += 1
            
            return results
            
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعارات المدرسين: {e}")
            return {'sent': 0, 'failed': 0}
    
    def _send_admin_notifications(self) -> Dict[str, int]:
        """إرسال إشعارات للأدمن"""
        try:
            results = {'sent': 0, 'failed': 0}
            
            # الحصول على جميع الأدمن
            admins = self._get_all_admins()
            
            # إحصائيات عامة
            stats = self._get_expiration_stats()
            
            if stats['total_expiring'] > 0:
                for admin in admins:
                    success = self._send_admin_notification(admin, stats)
                    if success:
                        results['sent'] += 1
                    else:
                        results['failed'] += 1
            
            return results
            
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعارات الأدمن: {e}")
            return {'sent': 0, 'failed': 0}
    
    def _get_all_instructors(self) -> List[Dict[str, Any]]:
        """الحصول على جميع المدرسين"""
        try:
            users_data = self.firebase_manager.database.child('users').get()
            if not users_data:
                return []
            
            instructors = []
            for user_id, user_data in users_data.items():
                if isinstance(user_data, dict) and user_data.get('role') == 'instructor':
                    user_data['id'] = user_id
                    instructors.append(user_data)
            
            return instructors
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على المدرسين: {e}")
            return []
    
    def _get_all_admins(self) -> List[Dict[str, Any]]:
        """الحصول على جميع الأدمن"""
        try:
            users_data = self.firebase_manager.database.child('users').get()
            if not users_data:
                return []
            
            admins = []
            for user_id, user_data in users_data.items():
                if isinstance(user_data, dict) and user_data.get('role') == 'admin':
                    user_data['id'] = user_id
                    admins.append(user_data)
            
            return admins
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على الأدمن: {e}")
            return []
    
    def _get_instructor_expiring_codes(self, instructor_id: str) -> List[Dict[str, Any]]:
        """الحصول على أكواد المدرس المنتهية قريباً"""
        try:
            expiring_codes = []
            
            for days in self.config['notification_days_before']:
                codes = self.firebase_manager.get_expiring_codes(days_ahead=days, user_id=instructor_id)
                
                # تصفية الأكواد التي لم يتم إرسال إشعار لها
                for code in codes:
                    notification_key = f"notification_sent_{days}d"
                    if not code.get(notification_key, False):
                        code['notification_type'] = f"{days}_days_before"
                        expiring_codes.append(code)
            
            return expiring_codes
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على أكواد المدرس المنتهية: {e}")
            return []
    
    def _get_expiration_stats(self) -> Dict[str, Any]:
        """الحصول على إحصائيات انتهاء الصلاحية"""
        try:
            stats = {
                'total_expiring': 0,
                'expiring_1_day': 0,
                'expiring_3_days': 0,
                'expiring_7_days': 0,
                'total_expired_today': 0
            }
            
            # الأكواد المنتهية خلال فترات مختلفة
            for days in [1, 3, 7]:
                expiring_codes = self.firebase_manager.get_expiring_codes(days_ahead=days)
                stats[f'expiring_{days}_day{"s" if days > 1 else ""}'] = len(expiring_codes)
                stats['total_expiring'] += len(expiring_codes)
            
            # الأكواد المنتهية اليوم
            current_time = datetime.now(timezone.utc)
            today_start = current_time.replace(hour=0, minute=0, second=0, microsecond=0)
            
            all_codes = self.firebase_manager.get_activation_codes(active_only=False)
            for code in all_codes:
                expires_at = code.get('expires_at')
                if expires_at:
                    try:
                        expiry_date = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                        if today_start <= expiry_date < current_time:
                            stats['total_expired_today'] += 1
                    except:
                        pass
            
            return stats
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات انتهاء الصلاحية: {e}")
            return {}
    
    def _send_instructor_notification(self, instructor: Dict[str, Any], expiring_codes: List[Dict[str, Any]]) -> bool:
        """إرسال إشعار للمدرس"""
        try:
            telegram_id = instructor.get('telegram_id')
            if not telegram_id or not self.config['telegram_enabled']:
                return False
            
            # إنشاء رسالة الإشعار
            message = self._create_instructor_message(instructor, expiring_codes)
            
            # إرسال الرسالة عبر التليجرام
            success = self._send_telegram_message(telegram_id, message)
            
            if success:
                # تحديث حالة الإشعار للأكواد
                self._mark_codes_notified(expiring_codes)
                logger.info(f"تم إرسال إشعار للمدرس {instructor.get('full_name', instructor.get('id'))}")
            
            return success
            
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار المدرس: {e}")
            return False
    
    def _send_admin_notification(self, admin: Dict[str, Any], stats: Dict[str, Any]) -> bool:
        """إرسال إشعار للأدمن"""
        try:
            telegram_id = admin.get('telegram_id')
            if not telegram_id or not self.config['telegram_enabled']:
                return False
            
            # إنشاء رسالة الإشعار
            message = self._create_admin_message(admin, stats)
            
            # إرسال الرسالة عبر التليجرام
            success = self._send_telegram_message(telegram_id, message)
            
            if success:
                logger.info(f"تم إرسال إشعار للأدمن {admin.get('full_name', admin.get('id'))}")
            
            return success
            
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار الأدمن: {e}")
            return False
    
    def _create_instructor_message(self, instructor: Dict[str, Any], expiring_codes: List[Dict[str, Any]]) -> str:
        """إنشاء رسالة إشعار للمدرس"""
        try:
            instructor_name = instructor.get('full_name', 'المدرس')
            
            message = f"🔔 مرحباً {instructor_name}\n\n"
            message += f"لديك {len(expiring_codes)} كود تفعيل سينتهي قريباً:\n\n"
            
            # تجميع الأكواد حسب نوع الإشعار
            codes_by_type = {}
            for code in expiring_codes:
                notification_type = code.get('notification_type', 'unknown')
                if notification_type not in codes_by_type:
                    codes_by_type[notification_type] = []
                codes_by_type[notification_type].append(code)
            
            # عرض الأكواد مجمعة
            for notification_type, codes in codes_by_type.items():
                if notification_type == "1_days_before":
                    message += "⚠️ ينتهي غداً:\n"
                elif notification_type == "3_days_before":
                    message += "⏰ ينتهي خلال 3 أيام:\n"
                elif notification_type == "7_days_before":
                    message += "📅 ينتهي خلال أسبوع:\n"
                
                for code in codes:
                    course = self.firebase_manager.get_course(code.get('course_id'))
                    course_title = course.get('title', 'غير محدد') if course else 'غير محدد'
                    days_left = code.get('days_until_expiry', 0)
                    
                    message += f"• {course_title} ({days_left} يوم)\n"
                
                message += "\n"
            
            message += "💡 يرجى تجديد الأكواد أو إنشاء أكواد جديدة من لوحة التحكم."
            
            return message
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء رسالة المدرس: {e}")
            return "خطأ في إنشاء الرسالة"
    
    def _create_admin_message(self, admin: Dict[str, Any], stats: Dict[str, Any]) -> str:
        """إنشاء رسالة إشعار للأدمن"""
        try:
            admin_name = admin.get('full_name', 'الأدمن')
            
            message = f"📊 تقرير يومي - {admin_name}\n\n"
            message += "📈 إحصائيات أكواد التفعيل:\n\n"
            
            if stats.get('total_expired_today', 0) > 0:
                message += f"❌ انتهت اليوم: {stats['total_expired_today']} كود\n"
            
            if stats.get('expiring_1_day', 0) > 0:
                message += f"⚠️ تنتهي غداً: {stats['expiring_1_day']} كود\n"
            
            if stats.get('expiring_3_days', 0) > 0:
                message += f"⏰ تنتهي خلال 3 أيام: {stats['expiring_3_days']} كود\n"
            
            if stats.get('expiring_7_days', 0) > 0:
                message += f"📅 تنتهي خلال أسبوع: {stats['expiring_7_days']} كود\n"
            
            message += f"\n📊 إجمالي الأكواد المنتهية قريباً: {stats.get('total_expiring', 0)}"
            
            return message
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء رسالة الأدمن: {e}")
            return "خطأ في إنشاء الرسالة"
    
    def _send_telegram_message(self, chat_id: str, message: str) -> bool:
        """إرسال رسالة عبر التليجرام"""
        try:
            if not self.bot_token:
                return False
            
            url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
            
            data = {
                'chat_id': chat_id,
                'text': message,
                'parse_mode': 'HTML'
            }
            
            response = requests.post(url, data=data, timeout=10)
            
            if response.status_code == 200:
                return True
            else:
                logger.error(f"فشل إرسال رسالة التليجرام: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"خطأ في إرسال رسالة التليجرام: {e}")
            return False
    
    def _mark_codes_notified(self, codes: List[Dict[str, Any]]):
        """تحديد الأكواد كمرسل لها إشعار"""
        try:
            for code in codes:
                code_id = code.get('id')
                notification_type = code.get('notification_type')
                
                if code_id and notification_type:
                    # تحديد نوع الإشعار المرسل
                    notification_key = f"notification_sent_{notification_type}"
                    
                    update_data = {
                        notification_key: True,
                        f"{notification_key}_at": datetime.now(timezone.utc).isoformat()
                    }
                    
                    self.firebase_manager.database.child('activation_codes').child(code_id).update(update_data)
                    
        except Exception as e:
            logger.error(f"خطأ في تحديد الأكواد كمرسل لها إشعار: {e}")
    
    def send_immediate_notification(self, user_id: str, message: str) -> bool:
        """إرسال إشعار فوري لمستخدم محدد"""
        try:
            user = self.firebase_manager.get_user(user_id)
            if not user:
                return False
            
            telegram_id = user.get('telegram_id')
            if not telegram_id:
                return False
            
            return self._send_telegram_message(telegram_id, message)
            
        except Exception as e:
            logger.error(f"خطأ في إرسال الإشعار الفوري: {e}")
            return False
    
    def update_config(self, new_config: Dict[str, Any]):
        """تحديث إعدادات الإشعارات"""
        try:
            self.config.update(new_config)
            logger.info("تم تحديث إعدادات الإشعارات")
        except Exception as e:
            logger.error(f"خطأ في تحديث إعدادات الإشعارات: {e}")


# مثيل عام لمدير الإشعارات
notification_manager = None

def get_notification_manager(firebase_manager=None, bot_token: str = None) -> NotificationManager:
    """الحصول على مثيل مدير الإشعارات"""
    global notification_manager
    
    if notification_manager is None:
        notification_manager = NotificationManager(firebase_manager, bot_token)
    
    if firebase_manager and not notification_manager.firebase_manager:
        notification_manager.firebase_manager = firebase_manager
    
    if bot_token and not notification_manager.bot_token:
        notification_manager.bot_token = bot_token
        notification_manager.config['telegram_enabled'] = True
    
    return notification_manager
