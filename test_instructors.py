#!/usr/bin/env python3
"""
اختبار وجود المدرسين في قاعدة البيانات
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.firebase_utils import get_firebase_manager

def test_instructors():
    """اختبار جلب المدرسين"""
    
    try:
        print("🔍 اختبار جلب المدرسين...")
        
        # الحصول على مدير Firebase
        firebase_manager = get_firebase_manager()
        
        if not firebase_manager:
            print("❌ فشل في الحصول على مدير Firebase")
            return
        
        print("✅ تم الحصول على مدير Firebase")
        
        # جلب جميع المستخدمين
        all_users = firebase_manager.get_all_users()
        print(f"📊 إجمالي المستخدمين: {len(all_users)}")
        
        # فلترة المدرسين
        instructors = [user for user in all_users if user.get('role') == 'instructor']
        print(f"👨‍🏫 عدد المدرسين: {len(instructors)}")
        
        if instructors:
            print("\n📋 قائمة المدرسين:")
            for i, instructor in enumerate(instructors, 1):
                print(f"  {i}. {instructor.get('full_name', 'بدون اسم')} ({instructor.get('email', 'بدون إيميل')})")
                print(f"     ID: {instructor.get('id', 'بدون معرف')}")
                print(f"     التخصص: {instructor.get('specialization_id', 'غير محدد')}")
                print(f"     الصلاحيات: {instructor.get('permissions', 'غير محددة')}")
                print()
        else:
            print("❌ لا يوجد مدرسين في قاعدة البيانات")
        
        # اختبار وظيفة get_all_instructors الجديدة
        print("🔍 اختبار وظيفة get_all_instructors...")
        instructors_detailed = firebase_manager.get_all_instructors()
        print(f"👨‍🏫 عدد المدرسين (مفصل): {len(instructors_detailed)}")
        
        if instructors_detailed:
            print("\n📋 قائمة المدرسين المفصلة:")
            for i, instructor in enumerate(instructors_detailed, 1):
                print(f"  {i}. {instructor.get('full_name', 'بدون اسم')}")
                print(f"     البريد: {instructor.get('email', 'بدون إيميل')}")
                print(f"     التخصص: {instructor.get('specialization_name', 'غير محدد')}")
                print(f"     عدد الكورسات: {instructor.get('courses_count', 0)}")
                print(f"     عدد الطلاب: {instructor.get('students_count', 0)}")
                print(f"     الصلاحيات: {instructor.get('permissions', {})}")
                print()
        
        # اختبار إحصائيات المدرسين
        print("📊 اختبار إحصائيات المدرسين...")
        stats = firebase_manager.get_instructors_statistics()
        print(f"الإحصائيات: {stats}")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المدرسين: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_instructors()
