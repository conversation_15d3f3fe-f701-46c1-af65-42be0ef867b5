{% extends "base.html" %}

{% block title %}تسجيل الدخول - {{ platform_name }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-lg border-0 rounded-3 mt-5">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-graduation-cap fa-3x text-primary mb-3"></i>
                        <h2 class="fw-bold">تسجيل الدخول</h2>
                        <p class="text-muted">ادخل إلى حسابك للوصول للكورسات</p>
                    </div>
                    
                    <form id="loginForm" method="POST">
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-envelope"></i>
                                </span>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock"></i>
                                </span>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe" name="remember">
                            <label class="form-check-label" for="rememberMe">
                                تذكرني
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-100 py-2 fw-bold">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </button>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="text-muted mb-3">ليس لديك حساب؟</p>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            للحصول على حساب، تواصل مع مدرسك أو استخدم رابط الدعوة المرسل إليك
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تبديل إظهار/إخفاء كلمة المرور
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    
    togglePassword.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        
        const icon = this.querySelector('i');
        icon.classList.toggle('fa-eye');
        icon.classList.toggle('fa-eye-slash');
    });
    
    // معالجة نموذج تسجيل الدخول
    const loginForm = document.getElementById('loginForm');
    loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        const email = document.getElementById('email').value.trim();
        const password = document.getElementById('password').value;
        const remember = document.getElementById('rememberMe').checked;

        // التحقق من البيانات
        if (!email || !password) {
            showError('يرجى إدخال البريد الإلكتروني وكلمة المرور');
            return;
        }

        showLoading(submitBtn);

        try {
            // استخدام AuthManager لتسجيل الدخول
            const result = await authManager.login(email, password, remember);

            if (result.success) {
                showSuccess('تم تسجيل الدخول بنجاح');

                // إعادة التوجيه حسب دور المستخدم
                setTimeout(() => {
                    const user = authManager.getCurrentUser();
                    const userRole = user.role;
                    let redirectUrl = '/dashboard';

                    if (userRole === 'admin') {
                        redirectUrl = '/admin/dashboard';
                    } else if (userRole === 'instructor') {
                        redirectUrl = '/instructor/dashboard';
                    } else if (userRole === 'student') {
                        redirectUrl = '/student/dashboard';
                    }

                    // التحقق من وجود صفحة مطلوبة للعودة إليها
                    const urlParams = new URLSearchParams(window.location.search);
                    const nextPage = urlParams.get('next');
                    if (nextPage) {
                        redirectUrl = decodeURIComponent(nextPage);
                    }

                    window.location.href = redirectUrl;
                }, 1500);

            } else {
                showError(result.message || 'فشل في تسجيل الدخول');
            }

        } catch (error) {
            console.error('خطأ في تسجيل الدخول:', error);
            showError('حدث خطأ في الاتصال، يرجى المحاولة لاحقاً');
        } finally {
            hideLoading(submitBtn, originalText);
        }
    });
});
</script>
{% endblock %}
