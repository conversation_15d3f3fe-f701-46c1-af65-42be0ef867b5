/**
 * إدارة المدرسين - صفحة مخصصة
 * Instructors Management - Dedicated Page
 */

class InstructorsManager {
    constructor() {
        this.instructors = [];
        this.filteredInstructors = [];
        this.specializations = [];
        this.currentEditId = null;
        this.init();
    }

    init() {
        this.loadStatistics();
        this.loadSpecializations();
        this.loadInstructors();
        this.setupEventListeners();
    }

    /**
     * تحميل إحصائيات المدرسين
     */
    async loadStatistics() {
        try {
            const response = await fetch('/api/admin/instructors/statistics');
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.updateStatistics(data.statistics);
                }
            }
        } catch (error) {
            console.error('خطأ في تحميل الإحصائيات:', error);
        }
    }

    /**
     * تحديث عرض الإحصائيات
     */
    updateStatistics(stats) {
        document.getElementById('totalInstructors').textContent = stats.total_instructors || 0;
        document.getElementById('activeInstructors').textContent = stats.active_instructors || 0;
        document.getElementById('canCreateCourses').textContent = stats.permissions_stats?.can_create_courses || 0;
        document.getElementById('canCreateGeneral').textContent = stats.permissions_stats?.can_create_general_courses || 0;
    }

    /**
     * تحميل قائمة التخصصات
     */
    async loadSpecializations() {
        try {
            const response = await fetch('/api/admin/specializations');
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.specializations = data.specializations;
                    this.populateSpecializationFilter();
                }
            }
        } catch (error) {
            console.error('خطأ في تحميل التخصصات:', error);
        }
    }

    /**
     * ملء قائمة فلتر التخصصات
     */
    populateSpecializationFilter() {
        const select = document.getElementById('specializationFilter');
        select.innerHTML = '<option value="">جميع التخصصات</option>';
        
        this.specializations.forEach(spec => {
            const option = document.createElement('option');
            option.value = spec.id;
            option.textContent = spec.name;
            select.appendChild(option);
        });
    }

    /**
     * تحميل قائمة المدرسين
     */
    async loadInstructors() {
        try {
            this.showLoading();
            
            const params = new URLSearchParams();
            const search = document.getElementById('searchInput').value.trim();
            const specialization = document.getElementById('specializationFilter').value;
            const status = document.getElementById('statusFilter').value;
            
            if (search) params.append('search', search);
            if (specialization) params.append('specialization', specialization);
            if (status) params.append('status', status);
            
            const response = await fetch(`/api/admin/instructors?${params}`);
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.instructors = data.instructors;
                    this.filteredInstructors = [...this.instructors];
                    this.renderInstructors();
                } else {
                    this.showError(data.message || 'فشل في تحميل المدرسين');
                }
            } else {
                this.showError('خطأ في الاتصال بالخادم');
            }
        } catch (error) {
            console.error('خطأ في تحميل المدرسين:', error);
            this.showError('حدث خطأ في تحميل البيانات');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * عرض قائمة المدرسين
     */
    renderInstructors() {
        const container = document.getElementById('instructorsContainer');
        const noDataMessage = document.getElementById('noDataMessage');
        
        if (this.filteredInstructors.length === 0) {
            container.innerHTML = '';
            noDataMessage.classList.remove('d-none');
            return;
        }
        
        noDataMessage.classList.add('d-none');
        
        container.innerHTML = this.filteredInstructors.map(instructor => `
            <div class="col-lg-6 col-xl-4 mb-4">
                <div class="instructor-card p-4 h-100">
                    <div class="d-flex align-items-start mb-3">
                        <div class="instructor-avatar me-3">
                            ${this.getInitials(instructor.full_name || instructor.first_name)}
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${instructor.full_name || `${instructor.first_name} ${instructor.last_name || ''}`}</h6>
                            <p class="text-muted mb-1 small">${instructor.email}</p>
                            <span class="badge ${instructor.active ? 'bg-success' : 'bg-secondary'} small">
                                ${instructor.active ? 'نشط' : 'غير نشط'}
                            </span>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="#" onclick="instructorsManager.editPermissions('${instructor.id}')">
                                        <i class="fas fa-key me-2"></i>
                                        تعديل الصلاحيات
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="#" onclick="instructorsManager.viewDetails('${instructor.id}')">
                                        <i class="fas fa-eye me-2"></i>
                                        عرض التفاصيل
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <small class="text-muted d-block mb-1">التخصص:</small>
                        <span class="badge bg-primary">${instructor.specialization_name}</span>
                    </div>
                    
                    <div class="row text-center mb-3">
                        <div class="col-4">
                            <div class="fw-bold text-primary">${instructor.courses_count || 0}</div>
                            <small class="text-muted">كورس</small>
                        </div>
                        <div class="col-4">
                            <div class="fw-bold text-success">${instructor.students_count || 0}</div>
                            <small class="text-muted">طالب</small>
                        </div>
                        <div class="col-4">
                            <div class="fw-bold text-info">${this.getActivePermissionsCount(instructor.permissions)}</div>
                            <small class="text-muted">صلاحية</small>
                        </div>
                    </div>
                    
                    <div class="permissions-summary">
                        <small class="text-muted d-block mb-2">الصلاحيات:</small>
                        <div>
                            ${this.renderPermissionBadges(instructor.permissions)}
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * الحصول على الأحرف الأولى من الاسم
     */
    getInitials(name) {
        if (!name) return 'M';
        const words = name.trim().split(' ');
        if (words.length >= 2) {
            return words[0].charAt(0) + words[1].charAt(0);
        }
        return words[0].charAt(0);
    }

    /**
     * عد الصلاحيات النشطة
     */
    getActivePermissionsCount(permissions) {
        if (!permissions) return 0;
        let count = 0;
        if (permissions.can_create_courses) count++;
        if (permissions.can_manage_students) count++;
        if (permissions.can_create_general_courses) count++;
        return count;
    }

    /**
     * عرض شارات الصلاحيات
     */
    renderPermissionBadges(permissions) {
        if (!permissions) return '<span class="permission-badge permission-disabled">لا توجد صلاحيات</span>';
        
        const badges = [];
        
        if (permissions.can_create_courses) {
            badges.push('<span class="permission-badge permission-enabled">إنشاء كورسات</span>');
        }
        
        if (permissions.can_manage_students) {
            badges.push('<span class="permission-badge permission-enabled">إدارة طلاب</span>');
        }
        
        if (permissions.can_create_general_courses) {
            badges.push('<span class="permission-badge permission-enabled">كورسات عامة</span>');
        }
        
        if (permissions.allowed_stages && permissions.allowed_stages.length > 0) {
            badges.push(`<span class="permission-badge permission-enabled">المراحل: ${permissions.allowed_stages.join(', ')}</span>`);
        }
        
        return badges.length > 0 ? badges.join(' ') : '<span class="permission-badge permission-disabled">لا توجد صلاحيات</span>';
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // البحث
        document.getElementById('searchInput').addEventListener('input', () => {
            this.loadInstructors();
        });
        
        // فلاتر
        document.getElementById('specializationFilter').addEventListener('change', () => {
            this.loadInstructors();
        });
        
        document.getElementById('statusFilter').addEventListener('change', () => {
            this.loadInstructors();
        });
    }

    /**
     * تعديل صلاحيات مدرس
     */
    editPermissions(instructorId) {
        const instructor = this.instructors.find(i => i.id === instructorId);
        if (!instructor) return;
        
        this.currentEditId = instructorId;
        
        // ملء بيانات المدرس في النموذج
        document.getElementById('instructorId').value = instructorId;
        document.getElementById('modalInstructorName').textContent = instructor.full_name || `${instructor.first_name} ${instructor.last_name || ''}`;
        document.getElementById('modalInstructorEmail').textContent = instructor.email;
        document.getElementById('modalInstructorSpecialization').textContent = instructor.specialization_name;
        document.getElementById('modalInstructorAvatar').textContent = this.getInitials(instructor.full_name || instructor.first_name);
        
        // ملء الصلاحيات
        const permissions = instructor.permissions || {};
        document.getElementById('canCreateCourses').checked = permissions.can_create_courses || false;
        document.getElementById('canManageStudents').checked = permissions.can_manage_students || false;
        document.getElementById('canCreateGeneral').checked = permissions.can_create_general_courses || false;
        
        // ملء المراحل
        this.renderStagesCheckboxes(permissions.allowed_stages || []);
        
        // عرض النموذج
        const modal = new bootstrap.Modal(document.getElementById('permissionsModal'));
        modal.show();
    }

    /**
     * عرض خانات اختيار المراحل
     */
    renderStagesCheckboxes(allowedStages) {
        const container = document.getElementById('stagesContainer');
        const stages = [
            { value: 1, label: 'المرحلة الأولى' },
            { value: 2, label: 'المرحلة الثانية' },
            { value: 3, label: 'المرحلة الثالثة' },
            { value: 4, label: 'المرحلة الرابعة' },
            { value: 5, label: 'المرحلة الخامسة' },
            { value: 6, label: 'المرحلة السادسة' }
        ];
        
        container.innerHTML = stages.map(stage => `
            <div class="form-check stage-checkbox">
                <input class="form-check-input" type="checkbox" id="stage${stage.value}" value="${stage.value}" 
                       ${allowedStages.includes(stage.value) ? 'checked' : ''}>
                <label class="form-check-label" for="stage${stage.value}">
                    ${stage.label}
                </label>
            </div>
        `).join('');
    }

    /**
     * عرض التفاصيل
     */
    viewDetails(instructorId) {
        const instructor = this.instructors.find(i => i.id === instructorId);
        if (!instructor) return;
        
        // يمكن إضافة نموذج منفصل لعرض التفاصيل
        console.log('عرض تفاصيل المدرس:', instructor);
    }

    /**
     * عرض حالة التحميل
     */
    showLoading() {
        document.getElementById('loadingSpinner').classList.remove('d-none');
        document.getElementById('instructorsContainer').style.opacity = '0.5';
    }

    /**
     * إخفاء حالة التحميل
     */
    hideLoading() {
        document.getElementById('loadingSpinner').classList.add('d-none');
        document.getElementById('instructorsContainer').style.opacity = '1';
    }

    /**
     * عرض رسالة خطأ
     */
    showError(message) {
        // يمكن استخدام toast أو alert
        alert(message);
    }
}

// متغير عام للوصول من HTML
let instructorsManager;

// تهيئة المدير عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    instructorsManager = new InstructorsManager();
});

/**
 * وظائف عامة يمكن استدعاؤها من HTML
 */

function refreshData() {
    if (instructorsManager) {
        instructorsManager.loadStatistics();
        instructorsManager.loadInstructors();
    }
}

function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('specializationFilter').value = '';
    document.getElementById('statusFilter').value = '';
    if (instructorsManager) {
        instructorsManager.loadInstructors();
    }
}

async function savePermissions() {
    if (!instructorsManager || !instructorsManager.currentEditId) return;
    
    try {
        // جمع بيانات الصلاحيات
        const permissions = {
            can_create_courses: document.getElementById('canCreateCourses').checked,
            can_manage_students: document.getElementById('canManageStudents').checked,
            can_create_general_courses: document.getElementById('canCreateGeneral').checked,
            allowed_stages: []
        };
        
        // جمع المراحل المختارة
        for (let i = 1; i <= 6; i++) {
            const checkbox = document.getElementById(`stage${i}`);
            if (checkbox && checkbox.checked) {
                permissions.allowed_stages.push(i);
            }
        }
        
        // إرسال البيانات
        const response = await fetch(`/api/admin/instructors/${instructorsManager.currentEditId}/permissions`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ permissions })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // إغلاق النموذج
            const modal = bootstrap.Modal.getInstance(document.getElementById('permissionsModal'));
            modal.hide();
            
            // تحديث البيانات
            instructorsManager.loadInstructors();
            instructorsManager.loadStatistics();
            
            // عرض رسالة نجاح
            alert('تم تحديث الصلاحيات بنجاح');
        } else {
            alert(data.message || 'فشل في تحديث الصلاحيات');
        }
    } catch (error) {
        console.error('خطأ في حفظ الصلاحيات:', error);
        alert('حدث خطأ في حفظ الصلاحيات');
    }
}
