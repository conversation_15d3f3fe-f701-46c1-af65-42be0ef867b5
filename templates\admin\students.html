{% extends "base.html" %}

{% block title %}إدارة الطلاب - {{ platform_name }}{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/admin.css') }}" rel="stylesheet">
<style>
    .student-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
    }
    
    .student-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    
    .student-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
        font-weight: bold;
    }
    
    .instructor-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 20px;
        margin: 0.125rem;
        display: inline-block;
    }
    
    .instructor-assigned {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .instructor-unassigned {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
        transition: transform 0.3s ease;
    }
    
    .stats-card:hover {
        transform: translateY(-3px);
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .filter-section {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .student-info {
        flex: 1;
    }
    
    .student-meta {
        font-size: 0.875rem;
        color: #6c757d;
    }
    
    .course-count {
        background: #e3f2fd;
        color: #1976d2;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-user-graduate text-primary me-2"></i>
                        إدارة الطلاب
                    </h1>
                    <p class="text-muted mb-0">إدارة الطلاب وانتمائهم للمدرسين في المنصة</p>
                </div>
                <button class="btn btn-primary" onclick="refreshData()">
                    <i class="fas fa-sync-alt me-2"></i>
                    تحديث البيانات
                </button>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4" id="statisticsSection">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number" id="totalStudents">0</div>
                <div>إجمالي الطلاب</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                <div class="stats-number" id="assignedStudents">0</div>
                <div>طلاب مرتبطين بمدرسين</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);">
                <div class="stats-number" id="unassignedStudents">0</div>
                <div>طلاب غير مرتبطين</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                <div class="stats-number" id="activeStudents">0</div>
                <div>طلاب نشطين</div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="filter-section">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">البحث</label>
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="searchInput" placeholder="البحث بالاسم أو البريد أو معرف التليجرام">
                </div>
            </div>
            <div class="col-md-3">
                <label class="form-label">المدرس المسؤول</label>
                <select class="form-select" id="instructorFilter">
                    <option value="">جميع المدرسين</option>
                    <option value="unassigned">غير مرتبطين</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">التخصص</label>
                <select class="form-select" id="specializationFilter">
                    <option value="">جميع التخصصات</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select class="form-select" id="statusFilter">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                    <i class="fas fa-times me-2"></i>
                    مسح الفلاتر
                </button>
            </div>
        </div>
    </div>

    <!-- قائمة الطلاب -->
    <div class="row" id="studentsContainer">
        <!-- سيتم تحميل الطلاب هنا -->
    </div>

    <!-- Loading Spinner -->
    <div class="text-center py-5" id="loadingSpinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
        <p class="mt-2 text-muted">جاري تحميل بيانات الطلاب...</p>
    </div>

    <!-- رسالة عدم وجود بيانات -->
    <div class="text-center py-5 d-none" id="noDataMessage">
        <i class="fas fa-user-graduate fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">لا توجد بيانات طلاب</h5>
        <p class="text-muted">لم يتم العثور على أي طلاب في النظام</p>
    </div>
</div>

<!-- Modal تعديل المدرس المسؤول -->
<div class="modal fade" id="instructorModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-edit me-2"></i>
                    تعديل المدرس المسؤول
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="instructorForm">
                    <input type="hidden" id="studentId">
                    
                    <div class="mb-3">
                        <h6 class="fw-bold mb-3">معلومات الطالب</h6>
                        <div class="d-flex align-items-center mb-3">
                            <div class="student-avatar me-3" id="modalStudentAvatar">
                                ط
                            </div>
                            <div>
                                <h6 class="mb-1" id="modalStudentName">اسم الطالب</h6>
                                <p class="text-muted mb-0" id="modalStudentEmail">البريد الإلكتروني</p>
                                <small class="text-muted" id="modalStudentSpecialization">التخصص</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="newInstructor" class="form-label">المدرس المسؤول الجديد</label>
                        <select class="form-select" id="newInstructor" required>
                            <option value="">اختر المدرس</option>
                            <option value="">إزالة الارتباط</option>
                        </select>
                        <div class="form-text">اختر المدرس الذي سيكون مسؤولاً عن هذا الطالب</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-primary" onclick="saveInstructorAssignment()">
                    <i class="fas fa-save me-2"></i>
                    حفظ التغييرات
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/admin-students.js') }}"></script>
{% endblock %}
