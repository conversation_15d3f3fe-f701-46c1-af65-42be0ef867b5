/**
 * تصميم مشغل الفيديو المتقدم
 * Advanced Video Player Styles
 */

.advanced-video-player {
    position: relative;
    width: 100%;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.video-wrapper {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    background: #000;
}

.youtube-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* طبقة التراكب */
.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

/* زر التشغيل الكبير */
.play-button-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    pointer-events: auto;
    transition: opacity 0.3s ease;
}

.play-btn-large {
    width: 80px;
    height: 80px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.play-btn-large:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
}

/* مؤشر التحميل */
.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: none;
    pointer-events: none;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* عناصر التحكم */
.video-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 20px 15px 15px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 20;
}

.video-controls.visible {
    opacity: 1;
    visibility: visible;
}

.controls-row {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* شريط التقدم */
.progress-container {
    position: relative;
    height: 6px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
    cursor: pointer;
    margin-bottom: 5px;
}

.progress-bar {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 3px;
    overflow: hidden;
}

.progress-buffer {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 3px;
    width: 0%;
    transition: width 0.3s ease;
}

.progress-played {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: #ff4444;
    border-radius: 3px;
    width: 0%;
    transition: width 0.1s ease;
}

.progress-handle {
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 14px;
    height: 14px;
    background: #ff4444;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease;
    left: 0%;
}

.progress-container:hover .progress-handle {
    opacity: 1;
}

/* أزرار التحكم */
.controls-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.controls-left,
.controls-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.control-btn {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    min-width: 36px;
    height: 36px;
    justify-content: center;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.control-btn.play-pause-btn {
    font-size: 18px;
}

.btn-text {
    font-size: 10px;
    font-weight: bold;
}

/* التحكم في الصوت */
.volume-control {
    position: relative;
    display: flex;
    align-items: center;
}

.volume-slider {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    padding: 10px;
    border-radius: 4px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    margin-bottom: 10px;
}

.volume-control:hover .volume-slider {
    opacity: 1;
    visibility: visible;
}

.volume-slider input[type="range"] {
    writing-mode: bt-lr; /* IE */
    -webkit-appearance: slider-vertical; /* WebKit */
    width: 30px;
    height: 80px;
    background: transparent;
    outline: none;
}

/* عرض الوقت */
.time-display {
    color: white;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
    min-width: 80px;
}

.time-separator {
    opacity: 0.7;
}

/* قائمة الإعدادات */
.settings-menu {
    position: absolute;
    bottom: 60px;
    right: 15px;
    background: rgba(0, 0, 0, 0.9);
    border-radius: 8px;
    padding: 15px;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    z-index: 30;
}

.settings-menu.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.settings-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    color: white;
    font-size: 14px;
}

.settings-item:last-child {
    margin-bottom: 0;
}

.settings-item select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
}

.settings-item select option {
    background: #333;
    color: white;
}

/* رسائل الخطأ */
.video-error {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.8);
    z-index: 40;
}

.error-content {
    text-align: center;
    color: white;
    padding: 20px;
}

.error-content i {
    font-size: 48px;
    color: #ff4444;
    margin-bottom: 15px;
}

.error-content p {
    font-size: 16px;
    margin: 0;
}

/* الشاشة الكاملة */
.advanced-video-player:-webkit-full-screen {
    width: 100vw;
    height: 100vh;
}

.advanced-video-player:-moz-full-screen {
    width: 100vw;
    height: 100vh;
}

.advanced-video-player:fullscreen {
    width: 100vw;
    height: 100vh;
}

.advanced-video-player:fullscreen .video-wrapper {
    padding-bottom: 0;
    height: 100vh;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .controls-buttons {
        flex-direction: column;
        gap: 10px;
    }
    
    .controls-left,
    .controls-right {
        justify-content: center;
        width: 100%;
    }
    
    .play-btn-large {
        width: 60px;
        height: 60px;
        font-size: 20px;
    }
    
    .control-btn {
        font-size: 14px;
        padding: 6px;
        min-width: 32px;
        height: 32px;
    }
    
    .time-display {
        font-size: 12px;
        min-width: 70px;
    }
    
    .volume-slider {
        position: static;
        transform: none;
        margin: 0;
        opacity: 1;
        visibility: visible;
        background: transparent;
        padding: 0;
    }
    
    .volume-slider input[type="range"] {
        writing-mode: initial;
        -webkit-appearance: none;
        width: 80px;
        height: 4px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
    }
    
    .volume-slider input[type="range"]::-webkit-slider-thumb {
        -webkit-appearance: none;
        width: 14px;
        height: 14px;
        background: #ff4444;
        border-radius: 50%;
        cursor: pointer;
    }
    
    .settings-menu {
        right: 50%;
        transform: translateX(50%) translateY(10px);
        min-width: 180px;
    }
    
    .settings-menu.visible {
        transform: translateX(50%) translateY(0);
    }
}

@media (max-width: 480px) {
    .video-controls {
        padding: 15px 10px 10px;
    }

    .controls-left,
    .controls-right {
        gap: 8px;
    }

    .control-btn {
        font-size: 12px;
        padding: 4px;
        min-width: 28px;
        height: 28px;
    }

    .play-btn-large {
        width: 50px;
        height: 50px;
        font-size: 18px;
    }

    .progress-container {
        height: 8px; /* شريط تقدم أكبر للمس */
        margin-bottom: 8px;
    }

    .progress-handle {
        width: 16px;
        height: 16px;
    }

    .error-content i {
        font-size: 36px;
    }

    .error-content p {
        font-size: 14px;
    }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1200px) {
    .control-btn {
        min-width: 40px;
        height: 40px;
        font-size: 18px;
    }

    .control-btn.play-pause-btn {
        font-size: 20px;
    }

    .time-display {
        font-size: 16px;
        min-width: 90px;
    }

    .play-button-overlay .play-btn-large {
        width: 80px;
        height: 80px;
        font-size: 32px;
    }
}

/* تحسينات إضافية للتفاعل */
.control-btn:active {
    transform: scale(0.95);
}

.progress-container:hover {
    height: 8px;
}

.progress-container:hover .progress-handle {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
}

/* تحسين الانتقالات */
.advanced-video-player * {
    transition: all 0.3s ease;
}

.progress-played,
.progress-buffer {
    transition: width 0.1s ease;
}

/* تحسين التركيز للوصولية */
.control-btn:focus {
    outline: 2px solid #ff4444;
    outline-offset: 2px;
}

.progress-container:focus-within .progress-handle {
    opacity: 1;
    box-shadow: 0 0 0 3px rgba(255, 68, 68, 0.3);
}

/* تحسينات إضافية للموبايل */
@media (hover: none) and (pointer: coarse) {
    .video-controls {
        opacity: 1;
        visibility: visible;
    }

    .progress-handle {
        opacity: 1;
    }

    .control-btn:hover {
        background: rgba(255, 255, 255, 0.1);
    }
}

/* تحسين الأداء */
.youtube-player {
    will-change: transform;
}

.progress-played,
.progress-buffer,
.progress-handle {
    will-change: width, left;
}

/* إجراءات الأمان المتقدمة */
.security-protection-layer {
    pointer-events: none !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
}

.youtube-protection-overlay {
    pointer-events: none !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
}

/* تحذيرات الأمان */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.security-warning {
    animation: slideIn 0.3s ease-out;
}

/* منع التحديد والنسخ */
.advanced-video-player * {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
    -webkit-touch-callout: none !important;
    -webkit-tap-highlight-color: transparent !important;
}

/* منع السحب والإفلات */
.advanced-video-player img,
.advanced-video-player video,
.advanced-video-player iframe {
    -webkit-user-drag: none !important;
    -khtml-user-drag: none !important;
    -moz-user-drag: none !important;
    -o-user-drag: none !important;
    user-drag: none !important;
    pointer-events: none !important;
}

/* حماية إضافية للفيديو */
.video-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
    background: transparent;
}

/* منع الطباعة */
@media print {
    .advanced-video-player {
        display: none !important;
    }
}

/* حماية من أدوات المطور */
.advanced-video-player {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: transform;
}

/* تحسين الأمان للموبايل */
@media (max-width: 768px) {
    .advanced-video-player {
        -webkit-touch-callout: none !important;
        -webkit-user-select: none !important;
        -webkit-tap-highlight-color: transparent !important;
        touch-action: manipulation !important;
    }
}

/* مؤشرات الإيماءات للموبايل */
.gesture-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 15px;
    border-radius: 20px;
    font-size: 14px;
    z-index: 1000;
    display: none;
    pointer-events: none;
}

.gesture-indicator.ready {
    background: rgba(0, 150, 255, 0.8);
}

/* معاينة التقديم/التأخير */
.seek-preview {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 15px 20px;
    border-radius: 25px;
    display: none;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    z-index: 1000;
    pointer-events: none;
    min-width: 80px;
}

.seek-preview.forward {
    background: rgba(0, 150, 255, 0.9);
}

.seek-preview.backward {
    background: rgba(255, 100, 0, 0.9);
}

.seek-icon {
    font-size: 24px;
    line-height: 1;
}

.seek-text {
    font-size: 14px;
    font-weight: bold;
}

/* مؤشر الصوت */
.volume-indicator {
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 15px 10px;
    border-radius: 20px;
    display: none;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    z-index: 1000;
    pointer-events: none;
    min-height: 120px;
}

.volume-icon {
    font-size: 20px;
}

.volume-bar {
    width: 4px;
    height: 60px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    position: relative;
    overflow: hidden;
}

.volume-fill {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: #ff4444;
    border-radius: 2px;
    transition: height 0.1s ease;
}

.volume-text {
    font-size: 12px;
    font-weight: bold;
}

/* مؤشر السطوع */
.brightness-indicator {
    position: absolute;
    top: 50%;
    left: 20px;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 15px 10px;
    border-radius: 20px;
    display: none;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    z-index: 1000;
    pointer-events: none;
    min-height: 120px;
}

.brightness-icon {
    font-size: 20px;
}

.brightness-bar {
    width: 4px;
    height: 60px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    position: relative;
    overflow: hidden;
}

.brightness-fill {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: #ffaa00;
    border-radius: 2px;
    transition: height 0.1s ease;
}

.brightness-text {
    font-size: 12px;
    font-weight: bold;
}

/* ملاحظات التقديم/التأخير */
.seek-feedback {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 10px 15px;
    border-radius: 20px;
    font-size: 14px;
    z-index: 1000;
    pointer-events: none;
    animation: seekFeedbackSlide 2s ease-out forwards;
}

.seek-feedback.forward {
    background: rgba(0, 150, 255, 0.9);
}

.seek-feedback.backward {
    background: rgba(255, 100, 0, 0.9);
}

@keyframes seekFeedbackSlide {
    0% {
        opacity: 0;
        transform: translateX(-50%) translateY(-10px);
    }
    20% {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
    80% {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateX(-50%) translateY(-10px);
    }
}

/* تحسينات الوضع الأفقي */
.advanced-video-player.landscape-mode {
    height: 100vh;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    border-radius: 0;
}

.advanced-video-player.landscape-mode .video-wrapper {
    height: 100%;
    padding-bottom: 0;
}

.advanced-video-player.landscape-mode .video-controls.landscape-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 20px 40px 20px;
}

.landscape-controls .controls-buttons {
    flex-direction: row !important;
    justify-content: space-between;
    align-items: center;
}

.landscape-controls .controls-left,
.landscape-controls .controls-right {
    width: auto !important;
    flex: none;
}

.landscape-controls .progress-container {
    position: absolute;
    top: 0;
    left: 40px;
    right: 40px;
    margin-bottom: 0;
}

/* وضع توفير الطاقة */
.advanced-video-player.power-save-mode {
    filter: brightness(0.8);
}

.advanced-video-player.power-save-mode .video-controls {
    background: rgba(0, 0, 0, 0.6);
}

.advanced-video-player.power-save-mode .control-btn {
    opacity: 0.8;
}

.advanced-video-player.power-save-mode .decorative-element {
    display: none !important;
}

/* وضع البطارية المنخفضة */
.advanced-video-player.low-power-mode {
    filter: brightness(0.7) contrast(0.9);
}

.advanced-video-player.low-power-mode .video-controls {
    background: rgba(0, 0, 0, 0.5);
    transition: none !important;
}

.advanced-video-player.low-power-mode * {
    transition: none !important;
    animation: none !important;
}

/* تحسينات Picture-in-Picture */
.pip-btn {
    position: relative;
}

.pip-btn::after {
    content: '';
    position: absolute;
    top: 2px;
    right: 2px;
    width: 6px;
    height: 6px;
    background: currentColor;
    border-radius: 1px;
    opacity: 0.7;
}

/* تحسينات إضافية للموبايل الصغير */
@media (max-width: 360px) {
    .volume-indicator,
    .brightness-indicator {
        padding: 10px 8px;
        min-height: 100px;
    }

    .volume-bar,
    .brightness-bar {
        height: 50px;
    }

    .seek-preview {
        padding: 12px 16px;
        min-width: 70px;
    }

    .seek-icon {
        font-size: 20px;
    }

    .seek-text {
        font-size: 12px;
    }
}

/* تحسينات للشاشات الكبيرة في الوضع الأفقي */
@media (orientation: landscape) and (max-height: 500px) {
    .advanced-video-player {
        height: 100vh;
    }

    .video-wrapper {
        height: 100%;
        padding-bottom: 0;
    }

    .video-controls {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    }

    .progress-container {
        position: absolute;
        top: 0;
        left: 20px;
        right: 20px;
        margin-bottom: 0;
    }

    .controls-buttons {
        padding-top: 25px;
    }
}

/* تحسين الاستجابة للمس */
@media (hover: none) and (pointer: coarse) {
    .control-btn {
        min-width: 44px;
        height: 44px;
        font-size: 16px;
    }

    .progress-container {
        height: 12px;
        margin-bottom: 10px;
    }

    .progress-handle {
        width: 20px;
        height: 20px;
        opacity: 1;
    }

    .volume-slider input[type="range"] {
        height: 8px;
    }

    /* تحسين المساحة بين الأزرار */
    .controls-left,
    .controls-right {
        gap: 12px;
    }
}

/* تحسينات الوصولية للموبايل */
@media (max-width: 768px) {
    .control-btn:focus {
        outline: 3px solid #ff4444;
        outline-offset: 3px;
    }

    .progress-container:focus-within .progress-handle {
        box-shadow: 0 0 0 4px rgba(255, 68, 68, 0.4);
    }
}

/* تحسين الأداء للموبايل */
.advanced-video-player {
    contain: layout style paint;
}

.video-wrapper,
.youtube-player {
    contain: layout style;
}

.video-controls {
    contain: layout style paint;
    will-change: opacity, transform;
}

.gesture-indicator,
.seek-preview,
.volume-indicator,
.brightness-indicator,
.seek-feedback {
    contain: layout style paint;
    will-change: opacity, transform;
}

/* ملاحظات اختصارات لوحة المفاتيح */
.keyboard-feedback {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 12px 16px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 1000;
    pointer-events: none;
    animation: keyboardFeedbackSlide 2s ease-out forwards;
    font-size: 14px;
    font-weight: 500;
    min-width: 120px;
}

.feedback-icon {
    font-size: 18px;
    line-height: 1;
}

.feedback-text {
    font-size: 13px;
}

@keyframes keyboardFeedbackSlide {
    0% {
        opacity: 0;
        transform: translateX(20px) scale(0.9);
    }
    15% {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
    85% {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateX(20px) scale(0.9);
    }
}

/* قائمة اختصارات لوحة المفاتيح */
.shortcuts-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.shortcuts-modal {
    background: #1a1a1a;
    border-radius: 15px;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.shortcuts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #333;
    background: linear-gradient(135deg, #ff4444, #cc3333);
    border-radius: 15px 15px 0 0;
}

.shortcuts-header h3 {
    margin: 0;
    color: white;
    font-size: 18px;
    font-weight: 600;
}

.close-shortcuts {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.2s ease;
}

.close-shortcuts:hover {
    background: rgba(255, 255, 255, 0.2);
}

.shortcuts-content {
    padding: 25px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    color: white;
}

.shortcuts-section h4 {
    margin: 0 0 15px 0;
    color: #ff4444;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid #ff4444;
    padding-bottom: 8px;
}

.shortcut-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 1.4;
}

.shortcut-item kbd {
    background: #333;
    color: #fff;
    padding: 4px 8px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    font-weight: bold;
    margin-left: 10px;
    min-width: 60px;
    text-align: center;
    border: 1px solid #555;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* وضع المسرح */
.advanced-video-player.theater-mode {
    width: 100vw;
    height: 70vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9998;
    border-radius: 0;
}

.advanced-video-player.theater-mode .video-wrapper {
    height: 100%;
    padding-bottom: 0;
}

/* المشغل المصغر */
.advanced-video-player.miniplayer-mode {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 300px;
    height: 169px;
    z-index: 9999;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
}

.advanced-video-player.miniplayer-mode:hover {
    transform: scale(1.05);
}

.advanced-video-player.miniplayer-mode .video-wrapper {
    height: 100%;
    padding-bottom: 0;
}

.advanced-video-player.miniplayer-mode .video-controls {
    padding: 8px;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
}

.advanced-video-player.miniplayer-mode .controls-buttons {
    gap: 8px;
}

.advanced-video-player.miniplayer-mode .control-btn {
    width: 28px;
    height: 28px;
    font-size: 12px;
}

.advanced-video-player.miniplayer-mode .progress-container {
    height: 6px;
    margin-bottom: 8px;
}

.advanced-video-player.miniplayer-mode .progress-handle {
    width: 12px;
    height: 12px;
}

/* تحسينات للموبايل في قائمة الاختصارات */
@media (max-width: 768px) {
    .shortcuts-modal {
        max-width: 95vw;
        max-height: 90vh;
        margin: 20px;
    }

    .shortcuts-content {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 20px;
    }

    .shortcut-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .shortcut-item kbd {
        margin-left: 0;
        margin-bottom: 5px;
    }

    .keyboard-feedback {
        top: 10px;
        right: 10px;
        left: 10px;
        text-align: center;
        justify-content: center;
    }
}

/* تحسينات الوصولية للاختصارات */
.shortcuts-modal:focus-within {
    outline: 3px solid #ff4444;
    outline-offset: 3px;
}

.close-shortcuts:focus {
    outline: 2px solid white;
    outline-offset: 2px;
}

/* تحسين الأداء للأوضاع المختلفة */
.advanced-video-player.theater-mode,
.advanced-video-player.miniplayer-mode {
    contain: layout style paint;
    will-change: transform, width, height;
}

/* تأثيرات انتقالية سلسة */
.advanced-video-player {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.advanced-video-player.theater-mode,
.advanced-video-player.miniplayer-mode {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}
