/**
 * مشغل الفيديو المتقدم باستخدام YouTube IFrame API
 * Advanced Video Player using YouTube IFrame API
 */

class AdvancedVideoPlayer {
    constructor(containerId, videoId, options = {}) {
        this.containerId = containerId;
        this.videoId = videoId;
        this.player = null;
        this.isReady = false;
        this.isPlaying = false;
        this.currentTime = 0;
        this.duration = 0;
        this.volume = 100;
        this.isMuted = false;
        this.isFullscreen = false;
        
        // خيارات المشغل
        this.options = {
            width: '100%',
            height: '100%',
            autoplay: false,
            showControls: false,
            enableKeyboard: true,
            ...options
        };
        
        // معرفات العناصر
        this.playerId = `youtube-player-${Date.now()}`;
        this.controlsId = `video-controls-${Date.now()}`;
        
        // متغيرات التتبع
        this.progressUpdateInterval = null;
        this.hideControlsTimeout = null;
        this.lastInteraction = Date.now();
        this.lastPercentage = 0;
        
        // أحداث مخصصة
        this.events = {
            onReady: options.onReady || (() => {}),
            onStateChange: options.onStateChange || (() => {}),
            onProgress: options.onProgress || (() => {}),
            onTimeUpdate: options.onTimeUpdate || (() => {})
        };
        
        this.init();
    }
    
    init() {
        // إنشاء هيكل المشغل
        this.createPlayerStructure();

        // تحميل YouTube IFrame API إذا لم يكن محملاً
        this.loadYouTubeAPI();

        // إعداد أحداث الشاشة الكاملة
        this.setupFullscreenEvents();

        // إعداد أحداث اللمس للموبايل
        this.setupTouchEvents();

        // تحميل إعدادات المستخدم المحفوظة
        this.loadUserPreferences();

        // إعداد إجراءات الأمان المتقدمة
        this.setupAdvancedSecurity();

        // بدء مراقبة الأنشطة المشبوهة
        this.monitorSuspiciousActivity();

        // تحسينات الأداء للموبايل
        this.optimizePerformanceForMobile();
    }
    
    createPlayerStructure() {
        const container = document.getElementById(this.containerId);
        if (!container) {
            console.error(`Container with ID ${this.containerId} not found`);
            return;
        }
        
        container.innerHTML = `
            <div class="advanced-video-player" data-video-id="${this.videoId}">
                <div class="video-wrapper">
                    <div id="${this.playerId}" class="youtube-player"></div>
                    <div class="video-overlay" id="videoOverlay">
                        <div class="play-button-overlay" id="playButtonOverlay">
                            <button class="play-btn-large" id="playBtnLarge">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                        <div class="loading-spinner" id="loadingSpinner">
                            <div class="spinner"></div>
                        </div>
                    </div>
                </div>
                
                <div class="video-controls" id="${this.controlsId}">
                    <div class="controls-row">
                        <!-- شريط التقدم -->
                        <div class="progress-container">
                            <div class="progress-bar" id="progressBar">
                                <div class="progress-buffer" id="progressBuffer"></div>
                                <div class="progress-played" id="progressPlayed"></div>
                                <div class="progress-handle" id="progressHandle"></div>
                            </div>
                        </div>
                        
                        <!-- أزرار التحكم -->
                        <div class="controls-buttons">
                            <div class="controls-left">
                                <button class="control-btn play-pause-btn" id="playPauseBtn">
                                    <i class="fas fa-play"></i>
                                </button>
                                <button class="control-btn" id="rewindBtn" title="الرجوع 10 ثوان">
                                    <i class="fas fa-undo"></i>
                                    <span class="btn-text">10</span>
                                </button>
                                <button class="control-btn" id="forwardBtn" title="التقدم 10 ثوان">
                                    <i class="fas fa-redo"></i>
                                    <span class="btn-text">10</span>
                                </button>
                                <div class="volume-control">
                                    <button class="control-btn volume-btn" id="volumeBtn">
                                        <i class="fas fa-volume-up"></i>
                                    </button>
                                    <div class="volume-slider" id="volumeSlider">
                                        <input type="range" min="0" max="100" value="100" id="volumeRange">
                                    </div>
                                </div>
                                <div class="time-display" id="timeDisplay">
                                    <span id="currentTimeDisplay">0:00</span>
                                    <span class="time-separator">/</span>
                                    <span id="durationDisplay">0:00</span>
                                </div>
                            </div>
                            
                            <div class="controls-right">
                                <button class="control-btn" id="settingsBtn" title="الإعدادات">
                                    <i class="fas fa-cog"></i>
                                </button>
                                <button class="control-btn" id="fullscreenBtn" title="شاشة كاملة">
                                    <i class="fas fa-expand"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- قائمة الإعدادات -->
                <div class="settings-menu" id="settingsMenu">
                    <div class="settings-item">
                        <span>جودة الفيديو</span>
                        <select id="qualitySelect">
                            <option value="auto">تلقائي</option>
                        </select>
                    </div>
                    <div class="settings-item">
                        <span>سرعة التشغيل</span>
                        <select id="speedSelect">
                            <option value="0.25">0.25x</option>
                            <option value="0.5">0.5x</option>
                            <option value="0.75">0.75x</option>
                            <option value="1" selected>1x</option>
                            <option value="1.25">1.25x</option>
                            <option value="1.5">1.5x</option>
                            <option value="2">2x</option>
                        </select>
                    </div>
                </div>
            </div>
        `;
        
        // إعداد أحداث التحكم
        this.setupControlEvents();

        // منع القائمة السياقية
        this.disableContextMenu();
    }
    
    loadYouTubeAPI() {
        // التحقق من وجود YouTube API
        if (window.YT && window.YT.Player) {
            this.initializePlayer();
            return;
        }
        
        // تحميل YouTube IFrame API
        if (!window.onYouTubeIframeAPIReady) {
            const tag = document.createElement('script');
            tag.src = 'https://www.youtube.com/iframe_api';
            const firstScriptTag = document.getElementsByTagName('script')[0];
            firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
            
            // إعداد callback عندما يكون API جاهزاً
            window.onYouTubeIframeAPIReady = () => {
                this.initializePlayer();
            };
        } else {
            // API قيد التحميل، انتظار
            const checkAPI = setInterval(() => {
                if (window.YT && window.YT.Player) {
                    clearInterval(checkAPI);
                    this.initializePlayer();
                }
            }, 100);
        }
    }
    
    initializePlayer() {
        this.player = new YT.Player(this.playerId, {
            height: '100%',
            width: '100%',
            videoId: this.videoId,
            playerVars: {
                'autoplay': this.options.autoplay ? 1 : 0,
                'controls': 0,           // إخفاء عناصر التحكم
                'modestbranding': 1,     // إخفاء شعار YouTube
                'rel': 0,                // عدم عرض فيديوهات مقترحة
                'showinfo': 0,           // إخفاء معلومات الفيديو
                'iv_load_policy': 3,     // إخفاء التعليقات التوضيحية
                'disablekb': 1,          // تعطيل لوحة المفاتيح الافتراضية
                'fs': 0,                 // تعطيل زر الشاشة الكاملة
                'playsinline': 1,        // تشغيل مضمن في الموبايل
                'cc_load_policy': 0,     // إخفاء الترجمة
                'enablejsapi': 1,        // تفعيل JavaScript API
                'origin': window.location.origin
            },
            events: {
                'onReady': (event) => this.onPlayerReady(event),
                'onStateChange': (event) => this.onPlayerStateChange(event),
                'onError': (event) => this.onPlayerError(event)
            }
        });
    }
    
    onPlayerReady(event) {
        this.isReady = true;
        this.duration = this.player.getDuration();
        this.updateDurationDisplay();
        this.updateQualityOptions();
        
        // إخفاء spinner التحميل
        document.getElementById('loadingSpinner').style.display = 'none';
        
        // إظهار زر التشغيل الكبير
        document.getElementById('playButtonOverlay').style.display = 'flex';
        
        // بدء تحديث التقدم
        this.startProgressUpdate();
        
        // استدعاء callback المخصص
        this.events.onReady(event);
        
        console.log('YouTube Player Ready');
    }
    
    onPlayerStateChange(event) {
        const state = event.data;
        
        switch (state) {
            case YT.PlayerState.PLAYING:
                this.isPlaying = true;
                this.updatePlayPauseButton();
                document.getElementById('playButtonOverlay').style.display = 'none';
                break;
                
            case YT.PlayerState.PAUSED:
                this.isPlaying = false;
                this.updatePlayPauseButton();
                break;
                
            case YT.PlayerState.ENDED:
                this.isPlaying = false;
                this.updatePlayPauseButton();
                document.getElementById('playButtonOverlay').style.display = 'flex';
                this.events.onProgress(100); // إشارة إكمال الفيديو
                break;
                
            case YT.PlayerState.BUFFERING:
                document.getElementById('loadingSpinner').style.display = 'block';
                break;
                
            default:
                document.getElementById('loadingSpinner').style.display = 'none';
                break;
        }
        
        // استدعاء callback المخصص
        this.events.onStateChange(event);
    }
    
    onPlayerError(event) {
        console.error('YouTube Player Error:', event.data);
        
        // إظهار رسالة خطأ للمستخدم
        const errorMessages = {
            2: 'معرف الفيديو غير صحيح',
            5: 'خطأ في تشغيل الفيديو',
            100: 'الفيديو غير موجود أو محذوف',
            101: 'الفيديو غير متاح في منطقتك',
            150: 'الفيديو غير متاح في منطقتك'
        };
        
        const errorMessage = errorMessages[event.data] || 'حدث خطأ في تشغيل الفيديو';
        this.showError(errorMessage);
    }
    
    setupControlEvents() {
        // زر التشغيل/الإيقاف الكبير
        document.getElementById('playBtnLarge').addEventListener('click', () => {
            this.togglePlayPause();
        });

        // زر التشغيل/الإيقاف في شريط التحكم
        document.getElementById('playPauseBtn').addEventListener('click', () => {
            this.togglePlayPause();
        });

        // أزرار الترجيع والتقدم
        document.getElementById('rewindBtn').addEventListener('click', () => {
            this.seekBy(-10);
        });

        document.getElementById('forwardBtn').addEventListener('click', () => {
            this.seekBy(10);
        });

        // التحكم في الصوت
        document.getElementById('volumeBtn').addEventListener('click', () => {
            this.toggleMute();
        });

        document.getElementById('volumeRange').addEventListener('input', (e) => {
            this.setVolume(parseInt(e.target.value));
        });

        // شريط التقدم
        this.setupProgressBar();

        // الشاشة الكاملة
        document.getElementById('fullscreenBtn').addEventListener('click', () => {
            this.toggleFullscreen();
        });

        // الإعدادات
        document.getElementById('settingsBtn').addEventListener('click', () => {
            this.toggleSettings();
        });

        // تغيير الجودة والسرعة
        document.getElementById('qualitySelect').addEventListener('change', (e) => {
            this.setQuality(e.target.value);
        });

        document.getElementById('speedSelect').addEventListener('change', (e) => {
            this.setPlaybackRate(parseFloat(e.target.value));
        });

        // أحداث لوحة المفاتيح
        if (this.options.enableKeyboard) {
            this.setupKeyboardEvents();
        }

        // إخفاء/إظهار عناصر التحكم
        this.setupControlsVisibility();
    }

    setupProgressBar() {
        const progressBar = document.getElementById('progressBar');
        const progressHandle = document.getElementById('progressHandle');
        let isDragging = false;

        const startDrag = (e) => {
            isDragging = true;
            document.addEventListener('mousemove', onDrag);
            document.addEventListener('mouseup', endDrag);
            document.addEventListener('touchmove', onDrag);
            document.addEventListener('touchend', endDrag);
        };

        const onDrag = (e) => {
            if (!isDragging || !this.isReady) return;

            const rect = progressBar.getBoundingClientRect();
            const clientX = e.clientX || (e.touches && e.touches[0].clientX);
            const percentage = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width));
            const newTime = percentage * this.duration;

            this.updateProgressDisplay(percentage);
            this.currentTime = newTime;
        };

        const endDrag = () => {
            if (isDragging) {
                isDragging = false;
                this.seekTo(this.currentTime);
                document.removeEventListener('mousemove', onDrag);
                document.removeEventListener('mouseup', endDrag);
                document.removeEventListener('touchmove', onDrag);
                document.removeEventListener('touchend', endDrag);
            }
        };

        progressBar.addEventListener('mousedown', startDrag);
        progressBar.addEventListener('touchstart', startDrag);
        progressHandle.addEventListener('mousedown', startDrag);
        progressHandle.addEventListener('touchstart', startDrag);

        // النقر على شريط التقدم للانتقال
        progressBar.addEventListener('click', (e) => {
            if (!this.isReady) return;

            const rect = progressBar.getBoundingClientRect();
            const percentage = (e.clientX - rect.left) / rect.width;
            const newTime = percentage * this.duration;
            this.seekTo(newTime);
        });
    }

    setupKeyboardEvents() {
        // إعداد اختصارات لوحة المفاتيح المتقدمة
        document.addEventListener('keydown', (e) => {
            // التأكد من أن المشغل نشط أو في منطقة التركيز
            if (!this.isPlayerFocused() && !this.isPlayerInViewport()) return;

            // منع التداخل مع حقول الإدخال
            if (this.isInputFieldFocused()) return;

            const isCtrl = e.ctrlKey || e.metaKey;
            const isShift = e.shiftKey;
            const isAlt = e.altKey;

            // اختصارات التشغيل الأساسية
            switch (e.code) {
                case 'Space':
                    e.preventDefault();
                    this.togglePlayPause();
                    this.showKeyboardFeedback('⏯️', 'تشغيل/إيقاف');
                    break;

                case 'KeyK':
                    e.preventDefault();
                    this.togglePlayPause();
                    this.showKeyboardFeedback('⏯️', 'تشغيل/إيقاف');
                    break;

                // التنقل في الفيديو
                case 'ArrowLeft':
                    e.preventDefault();
                    if (isShift) {
                        this.seekBy(-10); // Shift + ← = 10 ثوان
                        this.showKeyboardFeedback('⏪', '-10 ثوان');
                    } else if (isCtrl) {
                        this.seekBy(-30); // Ctrl + ← = 30 ثانية
                        this.showKeyboardFeedback('⏪', '-30 ثانية');
                    } else {
                        this.seekBy(-5); // ← = 5 ثوان
                        this.showKeyboardFeedback('⏪', '-5 ثوان');
                    }
                    break;

                case 'ArrowRight':
                    e.preventDefault();
                    if (isShift) {
                        this.seekBy(10); // Shift + → = 10 ثوان
                        this.showKeyboardFeedback('⏩', '+10 ثوان');
                    } else if (isCtrl) {
                        this.seekBy(30); // Ctrl + → = 30 ثانية
                        this.showKeyboardFeedback('⏩', '+30 ثانية');
                    } else {
                        this.seekBy(5); // → = 5 ثوان
                        this.showKeyboardFeedback('⏩', '+5 ثوان');
                    }
                    break;

                case 'KeyJ':
                    e.preventDefault();
                    this.seekBy(-10);
                    this.showKeyboardFeedback('⏪', '-10 ثوان');
                    break;

                case 'KeyL':
                    e.preventDefault();
                    this.seekBy(10);
                    this.showKeyboardFeedback('⏩', '+10 ثانية');
                    break;

                // التحكم في الصوت
                case 'ArrowUp':
                    e.preventDefault();
                    if (isShift) {
                        this.setVolume(Math.min(100, this.volume + 20));
                        this.showKeyboardFeedback('🔊', `الصوت: ${Math.round(this.volume)}%`);
                    } else {
                        this.setVolume(Math.min(100, this.volume + 10));
                        this.showKeyboardFeedback('🔊', `الصوت: ${Math.round(this.volume)}%`);
                    }
                    break;

                case 'ArrowDown':
                    e.preventDefault();
                    if (isShift) {
                        this.setVolume(Math.max(0, this.volume - 20));
                        this.showKeyboardFeedback('🔉', `الصوت: ${Math.round(this.volume)}%`);
                    } else {
                        this.setVolume(Math.max(0, this.volume - 10));
                        this.showKeyboardFeedback('🔉', `الصوت: ${Math.round(this.volume)}%`);
                    }
                    break;

                case 'KeyM':
                    e.preventDefault();
                    this.toggleMute();
                    this.showKeyboardFeedback(this.isMuted ? '🔇' : '🔊', this.isMuted ? 'كتم الصوت' : 'تشغيل الصوت');
                    break;

                // الشاشة الكاملة
                case 'KeyF':
                    e.preventDefault();
                    this.toggleFullscreen();
                    this.showKeyboardFeedback('⛶', this.isFullscreen ? 'شاشة كاملة' : 'خروج من الشاشة الكاملة');
                    break;

                case 'Escape':
                    if (this.isFullscreen) {
                        e.preventDefault();
                        this.exitFullscreen();
                        this.showKeyboardFeedback('⛶', 'خروج من الشاشة الكاملة');
                    }
                    break;

                // التنقل السريع
                case 'Home':
                    e.preventDefault();
                    this.seekTo(0);
                    this.showKeyboardFeedback('⏮️', 'بداية الفيديو');
                    break;

                case 'End':
                    e.preventDefault();
                    if (this.duration > 0) {
                        this.seekTo(this.duration - 5);
                        this.showKeyboardFeedback('⏭️', 'نهاية الفيديو');
                    }
                    break;

                // التنقل بالنسب المئوية
                case 'Digit0':
                case 'Numpad0':
                    e.preventDefault();
                    this.seekToPercentage(0);
                    this.showKeyboardFeedback('0️⃣', '0%');
                    break;

                case 'Digit1':
                case 'Numpad1':
                    e.preventDefault();
                    this.seekToPercentage(10);
                    this.showKeyboardFeedback('1️⃣', '10%');
                    break;

                case 'Digit2':
                case 'Numpad2':
                    e.preventDefault();
                    this.seekToPercentage(20);
                    this.showKeyboardFeedback('2️⃣', '20%');
                    break;

                case 'Digit3':
                case 'Numpad3':
                    e.preventDefault();
                    this.seekToPercentage(30);
                    this.showKeyboardFeedback('3️⃣', '30%');
                    break;

                case 'Digit4':
                case 'Numpad4':
                    e.preventDefault();
                    this.seekToPercentage(40);
                    this.showKeyboardFeedback('4️⃣', '40%');
                    break;

                case 'Digit5':
                case 'Numpad5':
                    e.preventDefault();
                    this.seekToPercentage(50);
                    this.showKeyboardFeedback('5️⃣', '50%');
                    break;

                case 'Digit6':
                case 'Numpad6':
                    e.preventDefault();
                    this.seekToPercentage(60);
                    this.showKeyboardFeedback('6️⃣', '60%');
                    break;

                case 'Digit7':
                case 'Numpad7':
                    e.preventDefault();
                    this.seekToPercentage(70);
                    this.showKeyboardFeedback('7️⃣', '70%');
                    break;

                case 'Digit8':
                case 'Numpad8':
                    e.preventDefault();
                    this.seekToPercentage(80);
                    this.showKeyboardFeedback('8️⃣', '80%');
                    break;

                case 'Digit9':
                case 'Numpad9':
                    e.preventDefault();
                    this.seekToPercentage(90);
                    this.showKeyboardFeedback('9️⃣', '90%');
                    break;

                // سرعة التشغيل
                case 'Comma':
                    e.preventDefault();
                    this.decreasePlaybackRate();
                    break;

                case 'Period':
                    e.preventDefault();
                    this.increasePlaybackRate();
                    break;

                case 'Slash':
                    e.preventDefault();
                    this.resetPlaybackRate();
                    this.showKeyboardFeedback('⚡', 'سرعة عادية');
                    break;

                // إظهار/إخفاء عناصر التحكم
                case 'KeyC':
                    e.preventDefault();
                    this.toggleControlsVisibility();
                    break;

                // إظهار معلومات الفيديو
                case 'KeyI':
                    e.preventDefault();
                    this.showVideoInfo();
                    break;

                // إظهار قائمة الاختصارات
                case 'Slash':
                    if (isShift) { // Shift + / = ?
                        e.preventDefault();
                        this.showKeyboardShortcuts();
                    }
                    break;
            }
        });

        // إعداد اختصارات إضافية للتحكم المتقدم
        this.setupAdvancedKeyboardShortcuts();
    }

    setupControlsVisibility() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        const controls = document.getElementById(this.controlsId);

        const showControls = () => {
            controls.classList.add('visible');
            this.lastInteraction = Date.now();

            // إخفاء عناصر التحكم بعد 3 ثوان من عدم التفاعل
            clearTimeout(this.hideControlsTimeout);
            this.hideControlsTimeout = setTimeout(() => {
                if (this.isPlaying && Date.now() - this.lastInteraction > 3000) {
                    controls.classList.remove('visible');
                }
            }, 3000);
        };

        const hideControls = () => {
            if (!this.isPlaying) return;
            controls.classList.remove('visible');
        };

        // إظهار عناصر التحكم عند الحركة أو اللمس
        playerContainer.addEventListener('mousemove', showControls);
        playerContainer.addEventListener('touchstart', showControls);
        playerContainer.addEventListener('click', showControls);

        // إخفاء عناصر التحكم عند مغادرة المشغل
        playerContainer.addEventListener('mouseleave', hideControls);

        // إظهار عناصر التحكم افتراضياً
        showControls();
    }

    // وظائف التحكم الأساسية
    togglePlayPause() {
        if (!this.isReady) return;

        if (this.isPlaying) {
            this.player.pauseVideo();
        } else {
            this.player.playVideo();
        }
    }

    seekTo(seconds) {
        if (!this.isReady) return;
        this.player.seekTo(seconds, true);
    }

    seekBy(seconds) {
        if (!this.isReady) return;
        const newTime = Math.max(0, Math.min(this.duration, this.currentTime + seconds));
        this.seekTo(newTime);
    }

    setVolume(volume) {
        if (!this.isReady) return;

        this.volume = Math.max(0, Math.min(100, volume));
        this.player.setVolume(this.volume);

        // تحديث واجهة التحكم في الصوت
        document.getElementById('volumeRange').value = this.volume;
        this.updateVolumeButton();

        // إلغاء كتم الصوت إذا كان مكتوماً
        if (this.isMuted && this.volume > 0) {
            this.isMuted = false;
            this.updateVolumeButton();
        }
    }

    toggleMute() {
        if (!this.isReady) return;

        if (this.isMuted) {
            this.player.unMute();
            this.isMuted = false;
        } else {
            this.player.mute();
            this.isMuted = true;
        }

        this.updateVolumeButton();
    }

    setPlaybackRate(rate) {
        if (!this.isReady) return;
        this.player.setPlaybackRate(rate);
    }

    setQuality(quality) {
        if (!this.isReady) return;

        if (quality === 'auto') {
            // YouTube سيختار الجودة تلقائياً
            return;
        }

        this.player.setPlaybackQuality(quality);
    }

    toggleFullscreen() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);

        if (!this.isFullscreen) {
            if (playerContainer.requestFullscreen) {
                playerContainer.requestFullscreen();
            } else if (playerContainer.webkitRequestFullscreen) {
                playerContainer.webkitRequestFullscreen();
            } else if (playerContainer.msRequestFullscreen) {
                playerContainer.msRequestFullscreen();
            }
        } else {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            }
        }
    }

    toggleSettings() {
        const settingsMenu = document.getElementById('settingsMenu');
        settingsMenu.classList.toggle('visible');
    }

    // وظائف التحديث
    startProgressUpdate() {
        // استخدام الوظيفة المحسنة للأداء
        this.optimizePerformance();
    }

    updateProgressDisplay(percentage) {
        const progressPlayed = document.getElementById('progressPlayed');
        const progressHandle = document.getElementById('progressHandle');

        progressPlayed.style.width = (percentage * 100) + '%';
        progressHandle.style.left = (percentage * 100) + '%';
    }

    updateTimeDisplay() {
        document.getElementById('currentTimeDisplay').textContent = this.formatTime(this.currentTime);
        document.getElementById('durationDisplay').textContent = this.formatTime(this.duration);
    }

    updateDurationDisplay() {
        document.getElementById('durationDisplay').textContent = this.formatTime(this.duration);
    }

    updatePlayPauseButton() {
        const playPauseBtn = document.getElementById('playPauseBtn');
        const playBtnLarge = document.getElementById('playBtnLarge');

        if (this.isPlaying) {
            playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
            playBtnLarge.innerHTML = '<i class="fas fa-pause"></i>';
        } else {
            playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
            playBtnLarge.innerHTML = '<i class="fas fa-play"></i>';
        }
    }

    updateVolumeButton() {
        const volumeBtn = document.getElementById('volumeBtn');

        if (this.isMuted || this.volume === 0) {
            volumeBtn.innerHTML = '<i class="fas fa-volume-mute"></i>';
        } else if (this.volume < 50) {
            volumeBtn.innerHTML = '<i class="fas fa-volume-down"></i>';
        } else {
            volumeBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
        }
    }

    updateQualityOptions() {
        const qualitySelect = document.getElementById('qualitySelect');
        const availableQualities = this.player.getAvailableQualityLevels();

        // مسح الخيارات الموجودة
        qualitySelect.innerHTML = '<option value="auto">تلقائي</option>';

        // إضافة الجودات المتاحة
        const qualityLabels = {
            'hd2160': '4K (2160p)',
            'hd1440': '1440p',
            'hd1080': '1080p',
            'hd720': '720p',
            'large': '480p',
            'medium': '360p',
            'small': '240p',
            'tiny': '144p'
        };

        availableQualities.forEach(quality => {
            const option = document.createElement('option');
            option.value = quality;
            option.textContent = qualityLabels[quality] || quality;
            qualitySelect.appendChild(option);
        });
    }

    // وظائف مساعدة
    formatTime(seconds) {
        if (!seconds || isNaN(seconds)) return '0:00';

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }

    isPlayerFocused() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        return playerContainer && playerContainer.contains(document.activeElement);
    }

    // التحقق من وجود المشغل في منطقة العرض
    isPlayerInViewport() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        if (!playerContainer) return false;

        const rect = playerContainer.getBoundingClientRect();
        const windowHeight = window.innerHeight || document.documentElement.clientHeight;
        const windowWidth = window.innerWidth || document.documentElement.clientWidth;

        return (
            rect.top < windowHeight &&
            rect.bottom > 0 &&
            rect.left < windowWidth &&
            rect.right > 0
        );
    }

    // التحقق من تركيز حقول الإدخال
    isInputFieldFocused() {
        const activeElement = document.activeElement;
        if (!activeElement) return false;

        const inputTypes = ['input', 'textarea', 'select'];
        const tagName = activeElement.tagName.toLowerCase();

        return inputTypes.includes(tagName) ||
               activeElement.contentEditable === 'true' ||
               activeElement.hasAttribute('contenteditable');
    }

    // عرض ملاحظات اختصارات لوحة المفاتيح
    showKeyboardFeedback(icon, text) {
        // إزالة الملاحظات السابقة
        const existingFeedback = document.querySelector('.keyboard-feedback');
        if (existingFeedback) {
            existingFeedback.remove();
        }

        const feedback = document.createElement('div');
        feedback.className = 'keyboard-feedback';
        feedback.innerHTML = `
            <div class="feedback-icon">${icon}</div>
            <div class="feedback-text">${text}</div>
        `;

        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        playerContainer.appendChild(feedback);

        // إزالة الملاحظة بعد 2 ثانية
        setTimeout(() => {
            if (feedback.parentNode) {
                feedback.parentNode.removeChild(feedback);
            }
        }, 2000);
    }

    // التنقل إلى نسبة مئوية محددة
    seekToPercentage(percentage) {
        if (!this.player || !this.duration) return;

        const targetTime = (this.duration * percentage) / 100;
        this.seekTo(targetTime);
    }

    // زيادة سرعة التشغيل
    increasePlaybackRate() {
        const currentRate = this.player ? this.player.getPlaybackRate() : 1;
        const rates = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2];
        const currentIndex = rates.indexOf(currentRate);

        if (currentIndex < rates.length - 1) {
            const newRate = rates[currentIndex + 1];
            this.setPlaybackRate(newRate);
            this.showKeyboardFeedback('⚡', `السرعة: ${newRate}x`);
        }
    }

    // تقليل سرعة التشغيل
    decreasePlaybackRate() {
        const currentRate = this.player ? this.player.getPlaybackRate() : 1;
        const rates = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2];
        const currentIndex = rates.indexOf(currentRate);

        if (currentIndex > 0) {
            const newRate = rates[currentIndex - 1];
            this.setPlaybackRate(newRate);
            this.showKeyboardFeedback('⚡', `السرعة: ${newRate}x`);
        }
    }

    // إعادة تعيين سرعة التشغيل
    resetPlaybackRate() {
        this.setPlaybackRate(1);
    }

    // تبديل رؤية عناصر التحكم
    toggleControlsVisibility() {
        const controls = document.querySelector(`#${this.containerId} .video-controls`);
        if (!controls) return;

        const isVisible = controls.style.opacity !== '0';
        if (isVisible) {
            controls.style.opacity = '0';
            controls.style.visibility = 'hidden';
            this.showKeyboardFeedback('👁️', 'إخفاء عناصر التحكم');
        } else {
            controls.style.opacity = '1';
            controls.style.visibility = 'visible';
            this.showKeyboardFeedback('👁️', 'إظهار عناصر التحكم');
        }
    }

    // عرض معلومات الفيديو
    showVideoInfo() {
        if (!this.player) return;

        const currentTime = this.formatTime(this.currentTime);
        const duration = this.formatTime(this.duration);
        const quality = this.player.getPlaybackQuality ? this.player.getPlaybackQuality() : 'غير محدد';
        const rate = this.player.getPlaybackRate ? this.player.getPlaybackRate() : 1;
        const volume = Math.round(this.volume);

        const info = `
            الوقت: ${currentTime} / ${duration}
            الجودة: ${quality}
            السرعة: ${rate}x
            الصوت: ${volume}%
        `;

        this.showMessage(info, 4000);
    }

    // عرض قائمة اختصارات لوحة المفاتيح
    showKeyboardShortcuts() {
        const shortcuts = `
            <div class="shortcuts-modal">
                <div class="shortcuts-header">
                    <h3>⌨️ اختصارات لوحة المفاتيح</h3>
                    <button class="close-shortcuts" onclick="this.parentElement.parentElement.parentElement.remove()">×</button>
                </div>
                <div class="shortcuts-content">
                    <div class="shortcuts-section">
                        <h4>التشغيل</h4>
                        <div class="shortcut-item"><kbd>Space</kbd> أو <kbd>K</kbd> - تشغيل/إيقاف</div>
                        <div class="shortcut-item"><kbd>M</kbd> - كتم/إلغاء كتم الصوت</div>
                        <div class="shortcut-item"><kbd>F</kbd> - شاشة كاملة</div>
                        <div class="shortcut-item"><kbd>Esc</kbd> - خروج من الشاشة الكاملة</div>
                    </div>
                    <div class="shortcuts-section">
                        <h4>التنقل</h4>
                        <div class="shortcut-item"><kbd>←</kbd> - تأخير 5 ثوان</div>
                        <div class="shortcut-item"><kbd>→</kbd> - تقديم 5 ثوان</div>
                        <div class="shortcut-item"><kbd>Shift + ←</kbd> - تأخير 10 ثوان</div>
                        <div class="shortcut-item"><kbd>Shift + →</kbd> - تقديم 10 ثوان</div>
                        <div class="shortcut-item"><kbd>Ctrl + ←</kbd> - تأخير 30 ثانية</div>
                        <div class="shortcut-item"><kbd>Ctrl + →</kbd> - تقديم 30 ثانية</div>
                        <div class="shortcut-item"><kbd>J</kbd> - تأخير 10 ثوان</div>
                        <div class="shortcut-item"><kbd>L</kbd> - تقديم 10 ثوان</div>
                        <div class="shortcut-item"><kbd>Home</kbd> - بداية الفيديو</div>
                        <div class="shortcut-item"><kbd>End</kbd> - نهاية الفيديو</div>
                    </div>
                    <div class="shortcuts-section">
                        <h4>الصوت</h4>
                        <div class="shortcut-item"><kbd>↑</kbd> - زيادة الصوت 10%</div>
                        <div class="shortcut-item"><kbd>↓</kbd> - تقليل الصوت 10%</div>
                        <div class="shortcut-item"><kbd>Shift + ↑</kbd> - زيادة الصوت 20%</div>
                        <div class="shortcut-item"><kbd>Shift + ↓</kbd> - تقليل الصوت 20%</div>
                    </div>
                    <div class="shortcuts-section">
                        <h4>السرعة</h4>
                        <div class="shortcut-item"><kbd>,</kbd> - تقليل السرعة</div>
                        <div class="shortcut-item"><kbd>.</kbd> - زيادة السرعة</div>
                        <div class="shortcut-item"><kbd>/</kbd> - سرعة عادية</div>
                    </div>
                    <div class="shortcuts-section">
                        <h4>التنقل السريع</h4>
                        <div class="shortcut-item"><kbd>0-9</kbd> - الانتقال إلى نسبة مئوية</div>
                        <div class="shortcut-item"><kbd>C</kbd> - إظهار/إخفاء عناصر التحكم</div>
                        <div class="shortcut-item"><kbd>I</kbd> - معلومات الفيديو</div>
                        <div class="shortcut-item"><kbd>?</kbd> - هذه القائمة</div>
                    </div>
                </div>
            </div>
        `;

        // إزالة القائمة السابقة إن وجدت
        const existingModal = document.querySelector('.shortcuts-modal');
        if (existingModal) {
            existingModal.remove();
            return;
        }

        const modal = document.createElement('div');
        modal.className = 'shortcuts-overlay';
        modal.innerHTML = shortcuts;

        // إغلاق عند النقر خارج القائمة
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        document.body.appendChild(modal);
    }

    // إعداد اختصارات متقدمة إضافية
    setupAdvancedKeyboardShortcuts() {
        // اختصارات للتحكم في الجودة
        document.addEventListener('keydown', (e) => {
            if (!this.isPlayerFocused() && !this.isPlayerInViewport()) return;
            if (this.isInputFieldFocused()) return;

            const isCtrl = e.ctrlKey || e.metaKey;
            const isShift = e.shiftKey;

            // اختصارات الجودة (Ctrl + Shift + Q)
            if (isCtrl && isShift && e.code === 'KeyQ') {
                e.preventDefault();
                this.cycleVideoQuality();
                return;
            }

            // اختصارات السرعة السريعة
            if (isCtrl && !isShift) {
                switch (e.code) {
                    case 'Minus':
                    case 'NumpadSubtract':
                        e.preventDefault();
                        this.decreasePlaybackRate();
                        break;
                    case 'Equal':
                    case 'NumpadAdd':
                        e.preventDefault();
                        this.increasePlaybackRate();
                        break;
                    case 'Digit0':
                    case 'Numpad0':
                        e.preventDefault();
                        this.resetPlaybackRate();
                        break;
                }
            }

            // اختصارات التنقل المتقدم
            if (isShift && !isCtrl) {
                switch (e.code) {
                    case 'KeyN':
                        e.preventDefault();
                        this.seekToNextChapter();
                        break;
                    case 'KeyP':
                        e.preventDefault();
                        this.seekToPreviousChapter();
                        break;
                }
            }

            // اختصارات الوضع المتقدم
            if (isCtrl && isShift) {
                switch (e.code) {
                    case 'KeyF':
                        e.preventDefault();
                        this.toggleTheaterMode();
                        break;
                    case 'KeyM':
                        e.preventDefault();
                        this.toggleMiniplayer();
                        break;
                }
            }
        });

        // اختصارات الماوس المتقدمة
        this.setupAdvancedMouseShortcuts();
    }

    // تدوير جودة الفيديو
    cycleVideoQuality() {
        if (!this.player || !this.player.getAvailableQualityLevels) return;

        const availableQualities = this.player.getAvailableQualityLevels();
        const currentQuality = this.player.getPlaybackQuality();

        if (availableQualities.length === 0) return;

        const currentIndex = availableQualities.indexOf(currentQuality);
        const nextIndex = (currentIndex + 1) % availableQualities.length;
        const nextQuality = availableQualities[nextIndex];

        this.player.setPlaybackQuality(nextQuality);
        this.showKeyboardFeedback('🎬', `الجودة: ${nextQuality}`);
    }

    // الانتقال للفصل التالي (محاكاة)
    seekToNextChapter() {
        const chapterDuration = 60; // دقيقة واحدة لكل فصل
        const currentChapter = Math.floor(this.currentTime / chapterDuration);
        const nextChapterStart = (currentChapter + 1) * chapterDuration;

        if (nextChapterStart < this.duration) {
            this.seekTo(nextChapterStart);
            this.showKeyboardFeedback('⏭️', `الفصل ${currentChapter + 2}`);
        }
    }

    // الانتقال للفصل السابق (محاكاة)
    seekToPreviousChapter() {
        const chapterDuration = 60; // دقيقة واحدة لكل فصل
        const currentChapter = Math.floor(this.currentTime / chapterDuration);
        const prevChapterStart = Math.max(0, (currentChapter - 1) * chapterDuration);

        this.seekTo(prevChapterStart);
        this.showKeyboardFeedback('⏮️', `الفصل ${Math.max(1, currentChapter)}`);
    }

    // تبديل وضع المسرح
    toggleTheaterMode() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        playerContainer.classList.toggle('theater-mode');

        const isTheaterMode = playerContainer.classList.contains('theater-mode');
        this.showKeyboardFeedback('🎭', isTheaterMode ? 'وضع المسرح' : 'الوضع العادي');
    }

    // تبديل المشغل المصغر
    toggleMiniplayer() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        playerContainer.classList.toggle('miniplayer-mode');

        const isMiniMode = playerContainer.classList.contains('miniplayer-mode');
        this.showKeyboardFeedback('📱', isMiniMode ? 'مشغل مصغر' : 'مشغل عادي');
    }

    // إعداد اختصارات الماوس المتقدمة
    setupAdvancedMouseShortcuts() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);

        // النقر المزدوج للشاشة الكاملة
        playerContainer.addEventListener('dblclick', (e) => {
            e.preventDefault();
            this.toggleFullscreen();
        });

        // عجلة الماوس للتحكم في الصوت
        playerContainer.addEventListener('wheel', (e) => {
            if (e.ctrlKey) {
                e.preventDefault();

                if (e.deltaY < 0) {
                    // التمرير للأعلى - زيادة الصوت
                    this.setVolume(Math.min(100, this.volume + 5));
                } else {
                    // التمرير للأسفل - تقليل الصوت
                    this.setVolume(Math.max(0, this.volume - 5));
                }

                this.showKeyboardFeedback('🔊', `الصوت: ${Math.round(this.volume)}%`);
            }
        }, { passive: false });

        // النقر الأوسط للتشغيل/الإيقاف
        playerContainer.addEventListener('mousedown', (e) => {
            if (e.button === 1) { // النقر الأوسط
                e.preventDefault();
                this.togglePlayPause();
            }
        });
    }

    showError(message) {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        const errorDiv = document.createElement('div');
        errorDiv.className = 'video-error';
        errorDiv.innerHTML = `
            <div class="error-content">
                <i class="fas fa-exclamation-triangle"></i>
                <p>${message}</p>
            </div>
        `;
        playerContainer.appendChild(errorDiv);
    }

    // وظائف التحكم العامة
    play() {
        if (this.isReady) this.player.playVideo();
    }

    pause() {
        if (this.isReady) this.player.pauseVideo();
    }

    stop() {
        if (this.isReady) this.player.stopVideo();
    }

    getCurrentTime() {
        return this.isReady ? this.player.getCurrentTime() : 0;
    }

    getDuration() {
        return this.isReady ? this.player.getDuration() : 0;
    }

    getVolume() {
        return this.volume;
    }

    disableContextMenu() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);

        // منع القائمة السياقية
        playerContainer.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.logSecurityEvent('context_menu_attempt');
            return false;
        });

        // منع السحب والإفلات
        playerContainer.addEventListener('dragstart', (e) => {
            e.preventDefault();
            this.logSecurityEvent('drag_attempt');
            return false;
        });

        // منع التحديد
        playerContainer.addEventListener('selectstart', (e) => {
            e.preventDefault();
            this.logSecurityEvent('text_selection_attempt');
            return false;
        });

        // منع النسخ
        playerContainer.addEventListener('copy', (e) => {
            e.preventDefault();
            this.logSecurityEvent('copy_attempt');
            return false;
        });

        // منع القص
        playerContainer.addEventListener('cut', (e) => {
            e.preventDefault();
            this.logSecurityEvent('cut_attempt');
            return false;
        });

        // منع اللصق
        playerContainer.addEventListener('paste', (e) => {
            e.preventDefault();
            this.logSecurityEvent('paste_attempt');
            return false;
        });

        // إضافة طبقة حماية شفافة
        this.addProtectionOverlay();
    }

    setupAdvancedSecurity() {
        // منع Developer Tools
        this.preventDevTools();

        // منع Print Screen
        this.preventScreenCapture();

        // منع View Source
        this.preventViewSource();

        // تعطيل اختصارات المتصفح الخطيرة
        this.disableDangerousShortcuts();

        // منع الوصول المباشر لـ YouTube
        this.preventDirectYouTubeAccess();

        // إضافة حماية من البوتات
        this.addBotProtection();
    }

    preventDevTools() {
        // منع F12
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F12') {
                e.preventDefault();
                this.logSecurityEvent('f12_attempt');
                this.showSecurityWarning('محاولة فتح أدوات المطور محظورة');
                return false;
            }

            // منع Ctrl+Shift+I
            if (e.ctrlKey && e.shiftKey && e.key === 'I') {
                e.preventDefault();
                this.logSecurityEvent('devtools_shortcut_attempt');
                this.showSecurityWarning('محاولة فتح أدوات المطور محظورة');
                return false;
            }

            // منع Ctrl+Shift+J
            if (e.ctrlKey && e.shiftKey && e.key === 'J') {
                e.preventDefault();
                this.logSecurityEvent('console_shortcut_attempt');
                this.showSecurityWarning('محاولة فتح وحدة التحكم محظورة');
                return false;
            }

            // منع Ctrl+U
            if (e.ctrlKey && e.key === 'u') {
                e.preventDefault();
                this.logSecurityEvent('view_source_attempt');
                this.showSecurityWarning('عرض مصدر الصفحة محظور');
                return false;
            }
        });

        // مراقبة تغيير حجم النافذة (قد يشير لفتح DevTools)
        let devtools = {
            open: false,
            orientation: null
        };

        const threshold = 160;

        setInterval(() => {
            if (window.outerHeight - window.innerHeight > threshold ||
                window.outerWidth - window.innerWidth > threshold) {
                if (!devtools.open) {
                    devtools.open = true;
                    this.logSecurityEvent('devtools_detected');
                    this.showSecurityWarning('تم اكتشاف فتح أدوات المطور');
                }
            } else {
                devtools.open = false;
            }
        }, 500);
    }

    preventScreenCapture() {
        // منع Print Screen
        document.addEventListener('keydown', (e) => {
            if (e.key === 'PrintScreen') {
                e.preventDefault();
                this.logSecurityEvent('print_screen_attempt');
                this.showSecurityWarning('أخذ لقطات الشاشة محظور');
                return false;
            }

            // منع Alt+Print Screen
            if (e.altKey && e.key === 'PrintScreen') {
                e.preventDefault();
                this.logSecurityEvent('alt_print_screen_attempt');
                this.showSecurityWarning('أخذ لقطات الشاشة محظور');
                return false;
            }
        });

        // محاولة منع لقطات الشاشة عبر CSS (محدود الفعالية)
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        if (playerContainer) {
            playerContainer.style.webkitUserSelect = 'none';
            playerContainer.style.mozUserSelect = 'none';
            playerContainer.style.msUserSelect = 'none';
            playerContainer.style.userSelect = 'none';
        }
    }

    preventViewSource() {
        // منع Ctrl+U (View Source)
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'u') {
                e.preventDefault();
                this.logSecurityEvent('view_source_attempt');
                this.showSecurityWarning('عرض مصدر الصفحة محظور');
                return false;
            }
        });
    }

    disableDangerousShortcuts() {
        document.addEventListener('keydown', (e) => {
            // منع Ctrl+S (Save)
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.logSecurityEvent('save_attempt');
                return false;
            }

            // منع Ctrl+A (Select All)
            if (e.ctrlKey && e.key === 'a') {
                e.preventDefault();
                this.logSecurityEvent('select_all_attempt');
                return false;
            }

            // منع Ctrl+P (Print)
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                this.logSecurityEvent('print_attempt');
                this.showSecurityWarning('الطباعة محظورة');
                return false;
            }

            // منع Ctrl+Shift+C (Inspect Element)
            if (e.ctrlKey && e.shiftKey && e.key === 'C') {
                e.preventDefault();
                this.logSecurityEvent('inspect_element_attempt');
                this.showSecurityWarning('فحص العنصر محظور');
                return false;
            }
        });
    }

    preventDirectYouTubeAccess() {
        // منع النقر على iframe YouTube مباشرة
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);

        // إضافة طبقة شفافة فوق الفيديو لمنع النقر المباشر
        const overlay = document.createElement('div');
        overlay.className = 'youtube-protection-overlay';
        overlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 5;
            pointer-events: none;
            background: transparent;
        `;

        const videoWrapper = playerContainer.querySelector('.video-wrapper');
        if (videoWrapper) {
            videoWrapper.appendChild(overlay);
        }

        // منع محاولات الوصول لـ YouTube API مباشرة
        if (window.YT && window.YT.Player) {
            const originalGetVideoUrl = window.YT.Player.prototype.getVideoUrl;
            if (originalGetVideoUrl) {
                window.YT.Player.prototype.getVideoUrl = function() {
                    this.logSecurityEvent?.('youtube_url_access_attempt');
                    return null;
                };
            }
        }
    }

    addBotProtection() {
        // التحقق من التفاعل البشري
        let humanInteraction = false;
        let interactionTimeout;

        const markHumanInteraction = () => {
            humanInteraction = true;
            clearTimeout(interactionTimeout);
            interactionTimeout = setTimeout(() => {
                humanInteraction = false;
            }, 30000); // 30 ثانية
        };

        // تتبع التفاعلات البشرية
        ['mousedown', 'mousemove', 'keydown', 'scroll', 'touchstart'].forEach(event => {
            document.addEventListener(event, markHumanInteraction, { passive: true });
        });

        // التحقق من البوتات عند محاولة التشغيل
        const originalPlay = this.play.bind(this);
        this.play = function() {
            if (!humanInteraction) {
                this.logSecurityEvent('bot_play_attempt');
                this.showSecurityWarning('يجب التفاعل مع الصفحة أولاً');
                return;
            }
            return originalPlay();
        };
    }

    addProtectionOverlay() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);

        // إضافة طبقة حماية إضافية
        const protectionLayer = document.createElement('div');
        protectionLayer.className = 'security-protection-layer';
        protectionLayer.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            pointer-events: none;
            background: linear-gradient(45deg, transparent 49%, rgba(0,0,0,0.01) 50%, transparent 51%);
            background-size: 20px 20px;
        `;

        playerContainer.appendChild(protectionLayer);
    }

    monitorSuspiciousActivity() {
        // مراقبة محاولات التلاعب المتكررة
        this.securityEvents = [];
        this.suspiciousActivityThreshold = 5; // عدد المحاولات المشبوهة قبل التحذير

        // مراقبة تغييرات DOM المشبوهة
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || mutation.type === 'attributes') {
                    // التحقق من محاولات تعديل المشغل
                    const target = mutation.target;
                    if (target.closest && target.closest(`#${this.containerId}`)) {
                        this.logSecurityEvent('dom_manipulation_attempt');
                    }
                }
            });
        });

        const playerContainer = document.querySelector(`#${this.containerId}`);
        if (playerContainer) {
            observer.observe(playerContainer, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['style', 'class', 'src']
            });
        }

        // مراقبة محاولات الوصول للكونسول
        let consoleWarningShown = false;
        const originalConsole = window.console;

        ['log', 'warn', 'error', 'info', 'debug'].forEach(method => {
            const original = originalConsole[method];
            window.console[method] = function(...args) {
                if (!consoleWarningShown && args.some(arg =>
                    typeof arg === 'string' && arg.includes('youtube'))) {
                    consoleWarningShown = true;
                    this.logSecurityEvent?.('console_youtube_access');
                }
                return original.apply(this, args);
            };
        });
    }

    logSecurityEvent(eventType) {
        const timestamp = new Date().toISOString();
        const event = {
            type: eventType,
            timestamp: timestamp,
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        // إضافة الحدث لقائمة الأحداث الأمنية
        this.securityEvents = this.securityEvents || [];
        this.securityEvents.push(event);

        // الاحتفاظ بآخر 50 حدث فقط
        if (this.securityEvents.length > 50) {
            this.securityEvents = this.securityEvents.slice(-50);
        }

        // تسجيل في الكونسول للمطورين
        console.warn(`🔒 Security Event: ${eventType} at ${timestamp}`);

        // إرسال للخادم إذا كان متاحاً
        this.reportSecurityEvent(event);

        // التحقق من تجاوز العتبة
        this.checkSuspiciousActivityThreshold();
    }

    reportSecurityEvent(event) {
        // إرسال الحدث الأمني للخادم (اختياري)
        if (typeof fetch !== 'undefined') {
            fetch('/api/security/log', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(event)
            }).catch(err => {
                // تجاهل أخطاء الشبكة
                console.debug('Security event reporting failed:', err);
            });
        }
    }

    checkSuspiciousActivityThreshold() {
        const recentEvents = this.securityEvents.filter(event => {
            const eventTime = new Date(event.timestamp);
            const now = new Date();
            return (now - eventTime) < 60000; // آخر دقيقة
        });

        if (recentEvents.length >= this.suspiciousActivityThreshold) {
            this.handleSuspiciousActivity();
        }
    }

    handleSuspiciousActivity() {
        this.logSecurityEvent('suspicious_activity_threshold_exceeded');
        this.showSecurityWarning('تم اكتشاف نشاط مشبوه. سيتم إيقاف المشغل مؤقتاً.');

        // إيقاف المشغل مؤقتاً
        if (this.player && this.player.pauseVideo) {
            this.player.pauseVideo();
        }

        // تعطيل التحكم لفترة قصيرة
        this.temporarilyDisableControls();
    }

    temporarilyDisableControls() {
        const controls = document.querySelectorAll(`#${this.containerId} .control-btn`);
        controls.forEach(btn => {
            btn.disabled = true;
            btn.style.opacity = '0.5';
        });

        // إعادة تفعيل التحكم بعد 10 ثوان
        setTimeout(() => {
            controls.forEach(btn => {
                btn.disabled = false;
                btn.style.opacity = '1';
            });
        }, 10000);
    }

    showSecurityWarning(message) {
        // إنشاء تحذير أمني
        const warning = document.createElement('div');
        warning.className = 'security-warning';
        warning.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff4444;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 10000;
            font-family: Arial, sans-serif;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            max-width: 300px;
            animation: slideIn 0.3s ease-out;
        `;

        warning.innerHTML = `
            <div style="display: flex; align-items: center; gap: 10px;">
                <span style="font-size: 18px;">⚠️</span>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(warning);

        // إزالة التحذير بعد 5 ثوان
        setTimeout(() => {
            if (warning.parentNode) {
                warning.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    warning.remove();
                }, 300);
            }
        }, 5000);
    }

    // أحداث الشاشة الكاملة
    setupFullscreenEvents() {
        const updateFullscreenButton = () => {
            const fullscreenBtn = document.getElementById('fullscreenBtn');

            if (document.fullscreenElement || document.webkitFullscreenElement || document.msFullscreenElement) {
                this.isFullscreen = true;
                fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
                fullscreenBtn.title = 'إنهاء الشاشة الكاملة';
            } else {
                this.isFullscreen = false;
                fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
                fullscreenBtn.title = 'شاشة كاملة';
            }
        };

        document.addEventListener('fullscreenchange', updateFullscreenButton);
        document.addEventListener('webkitfullscreenchange', updateFullscreenButton);
        document.addEventListener('msfullscreenchange', updateFullscreenButton);
    }

    // تحديث buffer التحميل
    updateBufferProgress() {
        if (!this.isReady) return;

        try {
            const buffered = this.player.getVideoLoadedFraction();
            const progressBuffer = document.getElementById('progressBuffer');
            progressBuffer.style.width = (buffered * 100) + '%';
        } catch (error) {
            // تجاهل الأخطاء في حالة عدم توفر البيانات
        }
    }

    // إعداد أحداث اللمس المتقدمة للموبايل
    setupTouchEvents() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        const videoWrapper = document.querySelector(`#${this.containerId} .video-wrapper`);

        let touchStartTime = 0;
        let touchStartX = 0;
        let touchStartY = 0;
        let touchMoveX = 0;
        let touchMoveY = 0;
        let isDoubleTap = false;
        let lastTapTime = 0;
        let gestureInProgress = false;
        let initialVolume = 0;
        let initialBrightness = 0;

        // منع التمرير الافتراضي على منطقة الفيديو
        playerContainer.addEventListener('touchmove', (e) => {
            if (gestureInProgress) {
                e.preventDefault();
            }
        }, { passive: false });

        playerContainer.addEventListener('touchstart', (e) => {
            touchStartTime = Date.now();
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
            touchMoveX = touchStartX;
            touchMoveY = touchStartY;
            gestureInProgress = false;

            // التحقق من النقر المزدوج
            const timeSinceLastTap = touchStartTime - lastTapTime;
            if (timeSinceLastTap < 300) {
                isDoubleTap = true;
            } else {
                isDoubleTap = false;
            }
            lastTapTime = touchStartTime;

            // حفظ القيم الحالية للصوت والسطوع
            initialVolume = this.volume;

            // إضافة مؤشر بصري للإيماءة
            this.showGestureIndicator('ready');
        });

        playerContainer.addEventListener('touchmove', (e) => {
            if (e.touches.length !== 1) return;

            touchMoveX = e.touches[0].clientX;
            touchMoveY = e.touches[0].clientY;

            const deltaX = touchMoveX - touchStartX;
            const deltaY = touchMoveY - touchStartY;
            const absDeltaX = Math.abs(deltaX);
            const absDeltaY = Math.abs(deltaY);

            // تحديد نوع الإيماءة
            if (absDeltaX > 20 || absDeltaY > 20) {
                gestureInProgress = true;

                // إيماءة أفقية - التقديم والتأخير
                if (absDeltaX > absDeltaY && absDeltaX > 30) {
                    const seekAmount = Math.floor(deltaX / 10); // كل 10 بكسل = ثانية واحدة
                    this.showSeekPreview(seekAmount);
                }

                // إيماءة عمودية - التحكم في الصوت أو السطوع
                else if (absDeltaY > absDeltaX && absDeltaY > 30) {
                    const containerWidth = playerContainer.offsetWidth;
                    const touchPosition = touchStartX / containerWidth;

                    // الجانب الأيمن - التحكم في الصوت
                    if (touchPosition > 0.5) {
                        const volumeChange = -deltaY / 3; // كل 3 بكسل = 1% صوت
                        const newVolume = Math.max(0, Math.min(100, initialVolume + volumeChange));
                        this.setVolume(newVolume);
                        this.showVolumeIndicator(newVolume);
                    }
                    // الجانب الأيسر - التحكم في السطوع (محاكاة)
                    else {
                        const brightnessChange = -deltaY / 3;
                        this.showBrightnessIndicator(brightnessChange);
                    }
                }
            }
        });

        playerContainer.addEventListener('touchend', (e) => {
            const touchEndTime = Date.now();
            const touchDuration = touchEndTime - touchStartTime;
            const touchEndX = e.changedTouches[0].clientX;
            const touchEndY = e.changedTouches[0].clientY;

            const deltaX = touchEndX - touchStartX;
            const deltaY = touchEndY - touchStartY;
            const absDeltaX = Math.abs(deltaX);
            const absDeltaY = Math.abs(deltaY);

            // إخفاء مؤشرات الإيماءات
            this.hideGestureIndicators();

            // النقر المزدوج - تبديل الشاشة الكاملة
            if (isDoubleTap && touchDuration < 300 && absDeltaX < 20 && absDeltaY < 20) {
                this.toggleFullscreen();
                return;
            }

            // إيماءة التقديم/التأخير
            if (gestureInProgress && absDeltaX > absDeltaY && absDeltaX > 30) {
                const seekAmount = Math.floor(deltaX / 10);
                this.seekBy(seekAmount);
                this.showSeekFeedback(seekAmount);
                return;
            }

            // لمسة عادية - تبديل عرض عناصر التحكم
            if (!gestureInProgress && touchDuration < 300 && absDeltaX < 10 && absDeltaY < 10) {
                const controls = document.getElementById(this.controlsId);
                if (controls.classList.contains('visible')) {
                    controls.classList.remove('visible');
                } else {
                    controls.classList.add('visible');
                    this.lastInteraction = Date.now();
                }
            }

            gestureInProgress = false;
        });

        // إعداد أحداث الاتجاه
        this.setupOrientationEvents();

        // إعداد Picture-in-Picture للموبايل
        this.setupPictureInPicture();
    }

    // عرض مؤشر الإيماءة
    showGestureIndicator(type) {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        let indicator = playerContainer.querySelector('.gesture-indicator');

        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'gesture-indicator';
            playerContainer.appendChild(indicator);
        }

        indicator.className = `gesture-indicator ${type}`;
        indicator.style.display = 'block';
    }

    // إخفاء مؤشرات الإيماءات
    hideGestureIndicators() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        const indicators = playerContainer.querySelectorAll('.gesture-indicator, .seek-preview, .volume-indicator, .brightness-indicator');

        indicators.forEach(indicator => {
            indicator.style.display = 'none';
        });
    }

    // عرض معاينة التقديم/التأخير
    showSeekPreview(seekAmount) {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        let preview = playerContainer.querySelector('.seek-preview');

        if (!preview) {
            preview = document.createElement('div');
            preview.className = 'seek-preview';
            preview.innerHTML = `
                <div class="seek-icon"></div>
                <div class="seek-text"></div>
            `;
            playerContainer.appendChild(preview);
        }

        const icon = preview.querySelector('.seek-icon');
        const text = preview.querySelector('.seek-text');

        if (seekAmount > 0) {
            icon.innerHTML = '⏩';
            text.textContent = `+${seekAmount}s`;
            preview.className = 'seek-preview forward';
        } else {
            icon.innerHTML = '⏪';
            text.textContent = `${seekAmount}s`;
            preview.className = 'seek-preview backward';
        }

        preview.style.display = 'flex';
    }

    // عرض مؤشر الصوت
    showVolumeIndicator(volume) {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        let indicator = playerContainer.querySelector('.volume-indicator');

        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'volume-indicator';
            indicator.innerHTML = `
                <div class="volume-icon">🔊</div>
                <div class="volume-bar">
                    <div class="volume-fill"></div>
                </div>
                <div class="volume-text"></div>
            `;
            playerContainer.appendChild(indicator);
        }

        const fill = indicator.querySelector('.volume-fill');
        const text = indicator.querySelector('.volume-text');
        const icon = indicator.querySelector('.volume-icon');

        fill.style.height = `${volume}%`;
        text.textContent = `${Math.round(volume)}%`;

        if (volume === 0) {
            icon.textContent = '🔇';
        } else if (volume < 30) {
            icon.textContent = '🔈';
        } else if (volume < 70) {
            icon.textContent = '🔉';
        } else {
            icon.textContent = '🔊';
        }

        indicator.style.display = 'flex';
    }

    // عرض مؤشر السطوع (محاكاة)
    showBrightnessIndicator(brightnessChange) {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        let indicator = playerContainer.querySelector('.brightness-indicator');

        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'brightness-indicator';
            indicator.innerHTML = `
                <div class="brightness-icon">☀️</div>
                <div class="brightness-bar">
                    <div class="brightness-fill"></div>
                </div>
                <div class="brightness-text"></div>
            `;
            playerContainer.appendChild(indicator);
        }

        const fill = indicator.querySelector('.brightness-fill');
        const text = indicator.querySelector('.brightness-text');
        const icon = indicator.querySelector('.brightness-icon');

        // محاكاة السطوع (50% كقيمة افتراضية)
        const brightness = Math.max(0, Math.min(100, 50 + brightnessChange));
        fill.style.height = `${brightness}%`;
        text.textContent = `${Math.round(brightness)}%`;

        if (brightness < 30) {
            icon.textContent = '🌑';
        } else if (brightness < 70) {
            icon.textContent = '🌤️';
        } else {
            icon.textContent = '☀️';
        }

        indicator.style.display = 'flex';
    }

    // عرض ملاحظات التقديم/التأخير
    showSeekFeedback(seekAmount) {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        let feedback = document.createElement('div');
        feedback.className = 'seek-feedback';

        if (seekAmount > 0) {
            feedback.innerHTML = `<span>⏩ تقديم ${seekAmount} ثانية</span>`;
            feedback.classList.add('forward');
        } else {
            feedback.innerHTML = `<span>⏪ تأخير ${Math.abs(seekAmount)} ثانية</span>`;
            feedback.classList.add('backward');
        }

        playerContainer.appendChild(feedback);

        // إزالة الملاحظة بعد ثانيتين
        setTimeout(() => {
            if (feedback.parentNode) {
                feedback.parentNode.removeChild(feedback);
            }
        }, 2000);
    }

    // إعداد أحداث الاتجاه
    setupOrientationEvents() {
        // مراقبة تغيير الاتجاه
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.handleOrientationChange();
            }, 100);
        });

        // مراقبة تغيير حجم النافذة
        window.addEventListener('resize', () => {
            this.handleOrientationChange();
        });
    }

    // التعامل مع تغيير الاتجاه
    handleOrientationChange() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        const isLandscape = window.innerWidth > window.innerHeight;

        if (isLandscape) {
            playerContainer.classList.add('landscape-mode');
            // في الوضع الأفقي، تحسين عرض عناصر التحكم
            this.optimizeForLandscape();
        } else {
            playerContainer.classList.remove('landscape-mode');
            // في الوضع العمودي، استخدام التخطيط العادي
            this.optimizeForPortrait();
        }

        // إعادة حساب أبعاد المشغل
        this.recalculatePlayerDimensions();
    }

    // تحسين العرض للوضع الأفقي
    optimizeForLandscape() {
        const controls = document.getElementById(this.controlsId);
        if (controls) {
            controls.classList.add('landscape-controls');
        }

        // إخفاء عناصر غير ضرورية في الوضع الأفقي
        const nonEssentialElements = document.querySelectorAll('.mobile-only-portrait');
        nonEssentialElements.forEach(element => {
            element.style.display = 'none';
        });
    }

    // تحسين العرض للوضع العمودي
    optimizeForPortrait() {
        const controls = document.getElementById(this.controlsId);
        if (controls) {
            controls.classList.remove('landscape-controls');
        }

        // إظهار العناصر المخفية
        const nonEssentialElements = document.querySelectorAll('.mobile-only-portrait');
        nonEssentialElements.forEach(element => {
            element.style.display = '';
        });
    }

    // إعادة حساب أبعاد المشغل
    recalculatePlayerDimensions() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        const videoWrapper = playerContainer.querySelector('.video-wrapper');

        // تحديث أبعاد المشغل حسب الاتجاه
        if (window.innerWidth > window.innerHeight) {
            // الوضع الأفقي - استخدام كامل العرض
            videoWrapper.style.maxHeight = '100vh';
        } else {
            // الوضع العمودي - استخدام النسبة العادية
            videoWrapper.style.maxHeight = '';
        }
    }

    // إعداد Picture-in-Picture
    setupPictureInPicture() {
        // التحقق من دعم Picture-in-Picture
        if (!('pictureInPictureEnabled' in document)) {
            return;
        }

        // إضافة زر Picture-in-Picture
        this.addPiPButton();
    }

    // إضافة زر Picture-in-Picture
    addPiPButton() {
        const controlsRight = document.querySelector(`#${this.controlsId} .controls-right`);
        if (!controlsRight) return;

        const pipButton = document.createElement('button');
        pipButton.className = 'control-btn pip-btn';
        pipButton.innerHTML = '📱';
        pipButton.title = 'صورة في صورة';
        pipButton.setAttribute('aria-label', 'تفعيل وضع صورة في صورة');

        pipButton.addEventListener('click', () => {
            this.togglePictureInPicture();
        });

        // إدراج الزر قبل زر الشاشة الكاملة
        const fullscreenBtn = controlsRight.querySelector('.fullscreen-btn');
        if (fullscreenBtn) {
            controlsRight.insertBefore(pipButton, fullscreenBtn);
        } else {
            controlsRight.appendChild(pipButton);
        }
    }

    // تبديل وضع Picture-in-Picture
    async togglePictureInPicture() {
        try {
            const videoElement = document.querySelector(`#${this.playerId}`);

            if (document.pictureInPictureElement) {
                // إنهاء وضع Picture-in-Picture
                await document.exitPictureInPicture();
            } else {
                // تفعيل وضع Picture-in-Picture
                // نحتاج لإنشاء عنصر فيديو مؤقت لأن YouTube iframe لا يدعم PiP مباشرة
                await this.createPiPVideo();
            }
        } catch (error) {
            console.warn('Picture-in-Picture غير مدعوم أو حدث خطأ:', error);
            this.showMessage('وضع صورة في صورة غير متاح حالياً');
        }
    }

    // إنشاء فيديو Picture-in-Picture
    async createPiPVideo() {
        // هذه ميزة متقدمة تتطلب تحويل محتوى YouTube إلى عنصر فيديو
        // للبساطة، سنعرض رسالة توضيحية
        this.showMessage('وضع صورة في صورة متاح للفيديوهات المحلية فقط');
    }

    // تحسينات الأداء للموبايل
    optimizePerformanceForMobile() {
        // تقليل معدل التحديث عند عدم النشاط
        this.setupPowerSavingMode();

        // تحسين جودة الفيديو حسب الشبكة
        this.optimizeVideoQuality();

        // إدارة الذاكرة
        this.setupMemoryManagement();
    }

    // إعداد وضع توفير الطاقة
    setupPowerSavingMode() {
        let inactivityTimer;
        let powerSaveMode = false;

        const resetInactivityTimer = () => {
            clearTimeout(inactivityTimer);

            if (powerSaveMode) {
                powerSaveMode = false;
                this.resumeNormalMode();
            }

            inactivityTimer = setTimeout(() => {
                if (!this.isPlaying) {
                    powerSaveMode = true;
                    this.enterPowerSaveMode();
                }
            }, 30000); // 30 ثانية من عدم النشاط
        };

        // مراقبة النشاط
        ['touchstart', 'touchmove', 'touchend', 'click'].forEach(event => {
            document.addEventListener(event, resetInactivityTimer);
        });

        // مراقبة حالة البطارية
        if ('getBattery' in navigator) {
            navigator.getBattery().then(battery => {
                this.monitorBatteryStatus(battery);
            });
        }
    }

    // دخول وضع توفير الطاقة
    enterPowerSaveMode() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        playerContainer.classList.add('power-save-mode');

        // تقليل معدل التحديث
        if (this.progressUpdateInterval) {
            clearInterval(this.progressUpdateInterval);
            this.progressUpdateInterval = setInterval(() => {
                this.updateProgress();
            }, 2000); // تحديث كل ثانيتين بدلاً من كل ثانية
        }

        // إخفاء عناصر غير ضرورية
        const nonEssentialElements = playerContainer.querySelectorAll('.decorative-element');
        nonEssentialElements.forEach(element => {
            element.style.display = 'none';
        });
    }

    // العودة للوضع العادي
    resumeNormalMode() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        playerContainer.classList.remove('power-save-mode');

        // استعادة معدل التحديث العادي
        if (this.progressUpdateInterval) {
            clearInterval(this.progressUpdateInterval);
            this.progressUpdateInterval = setInterval(() => {
                this.updateProgress();
            }, 1000);
        }

        // إظهار العناصر المخفية
        const nonEssentialElements = playerContainer.querySelectorAll('.decorative-element');
        nonEssentialElements.forEach(element => {
            element.style.display = '';
        });
    }

    // مراقبة حالة البطارية
    monitorBatteryStatus(battery) {
        const updateBatteryInfo = () => {
            const batteryLevel = battery.level * 100;
            const isCharging = battery.charging;

            // تفعيل وضع توفير الطاقة عند انخفاض البطارية
            if (batteryLevel < 20 && !isCharging) {
                this.enableLowPowerMode();
            } else if (batteryLevel > 30 || isCharging) {
                this.disableLowPowerMode();
            }
        };

        battery.addEventListener('chargingchange', updateBatteryInfo);
        battery.addEventListener('levelchange', updateBatteryInfo);
        updateBatteryInfo();
    }

    // تفعيل وضع البطارية المنخفضة
    enableLowPowerMode() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        playerContainer.classList.add('low-power-mode');

        // تقليل جودة الفيديو
        if (this.player && this.player.setPlaybackQuality) {
            this.player.setPlaybackQuality('small');
        }

        // إيقاف التأثيرات البصرية
        playerContainer.style.transition = 'none';

        this.showMessage('⚡ تم تفعيل وضع توفير الطاقة');
    }

    // إلغاء وضع البطارية المنخفضة
    disableLowPowerMode() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        playerContainer.classList.remove('low-power-mode');

        // استعادة التأثيرات البصرية
        playerContainer.style.transition = '';
    }

    // تحسين جودة الفيديو حسب الشبكة
    optimizeVideoQuality() {
        if ('connection' in navigator) {
            const connection = navigator.connection;

            const updateQualityBasedOnConnection = () => {
                if (!this.player || !this.player.setPlaybackQuality) return;

                const effectiveType = connection.effectiveType;

                switch (effectiveType) {
                    case 'slow-2g':
                    case '2g':
                        this.player.setPlaybackQuality('small');
                        this.showMessage('📶 تم تقليل جودة الفيديو للشبكة البطيئة');
                        break;
                    case '3g':
                        this.player.setPlaybackQuality('medium');
                        break;
                    case '4g':
                        this.player.setPlaybackQuality('large');
                        break;
                    default:
                        this.player.setPlaybackQuality('hd720');
                }
            };

            connection.addEventListener('change', updateQualityBasedOnConnection);
            updateQualityBasedOnConnection();
        }
    }

    // إدارة الذاكرة
    setupMemoryManagement() {
        // تنظيف الذاكرة عند إخفاء الصفحة
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseForMemoryOptimization();
            } else {
                this.resumeFromMemoryOptimization();
            }
        });

        // مراقبة استخدام الذاكرة
        if ('memory' in performance) {
            setInterval(() => {
                this.checkMemoryUsage();
            }, 30000); // فحص كل 30 ثانية
        }
    }

    // إيقاف مؤقت لتحسين الذاكرة
    pauseForMemoryOptimization() {
        if (this.isPlaying) {
            this.wasPlayingBeforeHidden = true;
            this.pause();
        }

        // تنظيف المؤقتات غير الضرورية
        if (this.progressUpdateInterval) {
            clearInterval(this.progressUpdateInterval);
        }
    }

    // استئناف بعد تحسين الذاكرة
    resumeFromMemoryOptimization() {
        if (this.wasPlayingBeforeHidden) {
            this.play();
            this.wasPlayingBeforeHidden = false;
        }

        // استعادة المؤقتات
        this.startProgressTracking();
    }

    // فحص استخدام الذاكرة
    checkMemoryUsage() {
        if ('memory' in performance) {
            const memInfo = performance.memory;
            const usedMemory = memInfo.usedJSHeapSize / memInfo.jsHeapSizeLimit;

            // إذا تجاوز استخدام الذاكرة 80%
            if (usedMemory > 0.8) {
                this.performMemoryCleanup();
            }
        }
    }

    // تنظيف الذاكرة
    performMemoryCleanup() {
        // تنظيف سجل الأحداث الأمنية
        if (this.securityEvents && this.securityEvents.length > 50) {
            this.securityEvents = this.securityEvents.slice(-25);
        }

        // تنظيف عناصر DOM المؤقتة
        const tempElements = document.querySelectorAll('.temp-element, .gesture-indicator[style*="display: none"]');
        tempElements.forEach(element => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        });

        // إجبار garbage collection (إذا كان متاحاً)
        if (window.gc) {
            window.gc();
        }
    }

    // تحسين الأداء
    optimizePerformance() {
        // تقليل تكرار تحديث التقدم عند عدم التشغيل
        if (this.progressUpdateInterval) {
            clearInterval(this.progressUpdateInterval);
        }

        this.progressUpdateInterval = setInterval(() => {
            if (this.isReady && this.player) {
                this.currentTime = this.player.getCurrentTime();
                this.duration = this.player.getDuration();

                const percentage = this.duration > 0 ? (this.currentTime / this.duration) : 0;

                // تحديث العرض فقط إذا كان هناك تغيير ملحوظ
                if (Math.abs(percentage - this.lastPercentage) > 0.001) {
                    this.updateProgressDisplay(percentage);
                    this.updateTimeDisplay();
                    this.updateBufferProgress();
                    this.lastPercentage = percentage;
                }

                // استدعاء callback التحديث
                this.events.onTimeUpdate({
                    currentTime: this.currentTime,
                    duration: this.duration,
                    percentage: percentage * 100
                });
            }
        }, this.isPlaying ? 500 : 2000); // تحديث أسرع أثناء التشغيل
    }

    // إعادة تهيئة المشغل
    reinitialize(newVideoId) {
        if (this.player && this.isReady) {
            this.videoId = newVideoId;
            this.player.loadVideoById(newVideoId);
            this.currentTime = 0;
            this.updateProgressDisplay(0);
            this.updateTimeDisplay();
        }
    }

    // الحصول على معلومات الفيديو
    getVideoInfo() {
        if (!this.isReady) return null;

        try {
            return {
                title: this.player.getVideoData().title,
                author: this.player.getVideoData().author,
                duration: this.duration,
                currentTime: this.currentTime,
                quality: this.player.getPlaybackQuality(),
                availableQualities: this.player.getAvailableQualityLevels(),
                playbackRate: this.player.getPlaybackRate(),
                volume: this.volume,
                isMuted: this.isMuted,
                isPlaying: this.isPlaying
            };
        } catch (error) {
            console.error('Error getting video info:', error);
            return null;
        }
    }

    // حفظ إعدادات المستخدم
    saveUserPreferences() {
        const preferences = {
            volume: this.volume,
            playbackRate: this.player ? this.player.getPlaybackRate() : 1,
            quality: this.player ? this.player.getPlaybackQuality() : 'auto'
        };

        localStorage.setItem('videoPlayerPreferences', JSON.stringify(preferences));
    }

    // تحميل إعدادات المستخدم
    loadUserPreferences() {
        try {
            const preferences = JSON.parse(localStorage.getItem('videoPlayerPreferences'));
            if (preferences) {
                if (preferences.volume !== undefined) {
                    this.setVolume(preferences.volume);
                }
                if (preferences.playbackRate !== undefined) {
                    this.setPlaybackRate(preferences.playbackRate);
                    document.getElementById('speedSelect').value = preferences.playbackRate;
                }
                if (preferences.quality !== undefined && preferences.quality !== 'auto') {
                    this.setQuality(preferences.quality);
                    document.getElementById('qualitySelect').value = preferences.quality;
                }
            }
        } catch (error) {
            console.error('Error loading user preferences:', error);
        }
    }

    destroy() {
        // حفظ إعدادات المستخدم
        this.saveUserPreferences();

        if (this.progressUpdateInterval) {
            clearInterval(this.progressUpdateInterval);
        }

        if (this.hideControlsTimeout) {
            clearTimeout(this.hideControlsTimeout);
        }

        if (this.player) {
            this.player.destroy();
        }

        // إزالة أحداث الشاشة الكاملة
        document.removeEventListener('fullscreenchange', this.updateFullscreenButton);
        document.removeEventListener('webkitfullscreenchange', this.updateFullscreenButton);
        document.removeEventListener('msfullscreenchange', this.updateFullscreenButton);
    }
}

// دالة مساعدة لإنشاء مشغل فيديو جديد
function createAdvancedVideoPlayer(containerId, videoId, options = {}) {
    return new AdvancedVideoPlayer(containerId, videoId, options);
}

// تصدير الكلاس والدالة للاستخدام العام
window.AdvancedVideoPlayer = AdvancedVideoPlayer;
window.createAdvancedVideoPlayer = createAdvancedVideoPlayer;
