{% extends "base.html" %}

{% block title %}معاينة الكورس - {{ course.title }} - {{ platform_name }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/advanced-video-player.css') }}">
<style>
.preview-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.preview-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

.preview-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.preview-body {
    padding: 2rem;
}

.course-info-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.lesson-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    cursor: pointer;
}

.lesson-item:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.lesson-item.active {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.lesson-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.lesson-title {
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    flex-grow: 1;
}

.lesson-type-badge {
    background: #667eea;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.content-display {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    min-height: 400px;
}

.welcome-message {
    text-align: center;
    color: #6c757d;
    padding: 3rem;
}

.preview-toolbar {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 2rem;
    border: 1px solid #e9ecef;
}

.instructor-badge {
    background: #28a745;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.page-indicator {
    background: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    margin-left: 1rem;
}
</style>
{% endblock %}

{% block content %}
<div class="preview-container">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="preview-card">
                    <div class="preview-header">
                        <h2><i class="fas fa-eye me-2"></i>معاينة الكورس</h2>
                        <p class="mb-0">{{ course.title }}</p>
                        <span class="instructor-badge mt-2 d-inline-block">
                            <i class="fas fa-chalkboard-teacher me-1"></i>معاينة المدرس
                        </span>
                    </div>
                    
                    <div class="preview-body">
                        <!-- شريط الأدوات -->
                        <div class="preview-toolbar">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معاينة كما يراها الطلاب</h5>
                                    <small class="text-muted">يمكنك التنقل بين الدروس لمعاينة المحتوى</small>
                                </div>
                                <div class="col-md-4 text-end">
                                    <a href="{{ url_for('instructor_edit_course_content', course_id=course.id) }}" class="btn btn-primary">
                                        <i class="fas fa-edit me-2"></i>العودة للتحرير
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- معلومات الكورس -->
                        <div class="course-info-card">
                            <div class="row">
                                <div class="col-md-8">
                                    <h4>{{ course.title }}</h4>
                                    <div class="course-meta">
                                        <div class="d-flex flex-wrap gap-3 mt-2">
                                            <div>
                                                <i class="fas fa-user text-primary me-2"></i>
                                                <span>{{ course.instructor_name }}</span>
                                            </div>
                                            <div>
                                                <i class="fas fa-layer-group text-primary me-2"></i>
                                                <span>
                                                    {% if course.is_general %}
                                                        كورس عام
                                                    {% else %}
                                                        المرحلة {{ course.stage }}
                                                    {% endif %}
                                                </span>
                                            </div>
                                            <div>
                                                <i class="fas fa-calendar text-primary me-2"></i>
                                                <span id="courseCreatedDate">{{ course.created_at }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    {% if course.description %}
                                    <div class="mt-3">
                                        <p class="text-muted">{{ course.description }}</p>
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="course-stats">
                                        <div class="stat-item">
                                            <span class="badge bg-primary" id="lessonsCountBadge">0 دروس</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- قائمة الدروس -->
                            <div class="col-md-4">
                                <div class="lessons-container">
                                    <h5><i class="fas fa-list me-2"></i>محتوى الكورس</h5>
                                    <div id="lessonsList" class="lessons-list">
                                        <!-- سيتم تحميل الدروس هنا -->
                                    </div>
                                </div>
                            </div>
                            
                            <!-- عرض المحتوى -->
                            <div class="col-md-8">
                                <div class="content-display">
                                    <div id="welcomeMessage" class="welcome-message">
                                        <i class="fas fa-play-circle fa-4x mb-3 text-primary"></i>
                                        <h4>مرحباً بك في الكورس</h4>
                                        <p>اختر درساً من القائمة لبدء المعاينة</p>
                                    </div>
                                    
                                    <div id="lessonContent" style="display: none;">
                                        <!-- سيتم عرض محتوى الدرس هنا -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/advanced-video-player.js') }}"></script>
<script>
let currentCourse = null;
let currentLesson = null;
let lessons = [];
let advancedPlayer = null;

$(document).ready(function() {
    currentCourse = {
        id: '{{ course.id }}',
        title: '{{ course.title }}',
        instructor_name: '{{ course.instructor_name }}'
    };
    
    loadLessons();
    
    // تدمير المشغل عند مغادرة الصفحة
    window.addEventListener('beforeunload', function() {
        if (advancedPlayer) {
            advancedPlayer.destroy();
        }
    });
});

// دالة مساعدة للطلبات مع المصادقة
async function fetchWithAuth(url, options = {}) {
    const token = localStorage.getItem('access_token');
    const defaultOptions = {
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    };

    const mergedOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };

    const response = await fetch(url, mergedOptions);
    return await response.json();
}

async function loadLessons() {
    try {
        const response = await fetchWithAuth(`/api/instructor/courses/${currentCourse.id}/lessons`);

        if (response.success) {
            lessons = response.lessons || [];
            displayLessons(lessons);
            updateLessonsCount();
        } else {
            showAlert('فشل في تحميل الدروس: ' + response.message, 'danger');
        }
    } catch (error) {
        console.error('خطأ في تحميل الدروس:', error);
        showAlert('حدث خطأ في تحميل الدروس', 'danger');
    }
}

function displayLessons(lessons) {
    const container = $('#lessonsList');
    
    if (lessons.length === 0) {
        container.html(`
            <div class="text-center text-muted py-4">
                <i class="fas fa-book-open fa-3x mb-3"></i>
                <p>لا توجد دروس بعد</p>
                <p class="small">ابدأ بإضافة محتوى في محرر الكورس</p>
            </div>
        `);
        return;
    }
    
    let html = '';
    lessons.forEach((lesson, index) => {
        html += `
            <div class="lesson-item" onclick="selectLesson('${lesson.id}')">
                <div class="lesson-header">
                    <h6 class="lesson-title">${lesson.title}</h6>
                    <span class="lesson-type-badge">${getContentTypeLabel(lesson.content_type)}</span>
                </div>
                <div class="lesson-meta">
                    <small class="text-muted">
                        <i class="fas fa-sort-numeric-up me-1"></i>الترتيب: ${lesson.order}
                        ${lesson.page ? `<span class="page-indicator">صفحة ${lesson.page}</span>` : ''}
                    </small>
                </div>
            </div>
        `;
    });
    
    container.html(html);
}

function getContentTypeLabel(contentType) {
    const labels = {
        'text': 'نص',
        'image': 'صورة', 
        'video': 'فيديو',
        'mcq': 'أسئلة متعددة',
        'essay': 'سؤال مقالي'
    };
    return labels[contentType] || contentType;
}

function updateLessonsCount() {
    $('#lessonsCountBadge').text(`${lessons.length} ${lessons.length === 1 ? 'درس' : 'دروس'}`);
}

function selectLesson(lessonId) {
    const lesson = lessons.find(l => l.id === lessonId);
    if (!lesson) return;
    
    currentLesson = lesson;
    
    // تحديث الواجهة
    $('.lesson-item').removeClass('active');
    $(`.lesson-item`).each(function() {
        if ($(this).attr('onclick').includes(lessonId)) {
            $(this).addClass('active');
        }
    });
    
    displayLessonContent(lesson);
}

function displayLessonContent(lesson) {
    const container = $('#lessonContent');
    let html = '';
    
    // تدمير المشغل السابق إذا كان موجوداً
    if (advancedPlayer) {
        advancedPlayer.destroy();
        advancedPlayer = null;
    }
    
    html += `<h4>${lesson.title}</h4>`;
    
    switch (lesson.content_type) {
        case 'text':
            html += `
                <div class="lesson-text-content">
                    ${lesson.content_data.html || lesson.content_data.content || 'لا يوجد محتوى'}
                </div>
            `;
            break;
            
        case 'image':
            html += `
                <div class="lesson-image-content text-center">
                    <img src="${lesson.content_data.image_url}" class="img-fluid rounded mb-3" alt="${lesson.content_data.alt_text || lesson.title}">
                    ${lesson.content_data.caption ? `<p class="text-muted">${lesson.content_data.caption}</p>` : ''}
                </div>
            `;
            break;
            
        case 'video':
            html += `
                <div class="lesson-video-content">
                    <div id="advancedVideoPlayer" class="advanced-video-player mb-3"></div>
                    ${lesson.content_data.description ? `<p class="text-muted">${lesson.content_data.description}</p>` : ''}
                </div>
            `;
            break;
            
        case 'mcq':
            html += `
                <div class="lesson-mcq-content">
                    <div class="question-text mb-3">
                        <h5>السؤال:</h5>
                        <p>${lesson.content_data.question}</p>
                    </div>
                    <div class="options">
                        <h6>الخيارات:</h6>
                        <ul class="list-group">
            `;
            lesson.content_data.options.forEach((option, index) => {
                const isCorrect = lesson.content_data.correct_answers.includes(index);
                html += `
                    <li class="list-group-item ${isCorrect ? 'list-group-item-success' : ''}">
                        ${option} ${isCorrect ? '<i class="fas fa-check text-success ms-2"></i>' : ''}
                    </li>
                `;
            });
            html += `
                        </ul>
                    </div>
                    ${lesson.content_data.feedback ? `<div class="mt-3"><h6>التفسير:</h6><p class="text-muted">${lesson.content_data.feedback}</p></div>` : ''}
                </div>
            `;
            break;
            
        case 'essay':
            html += `
                <div class="lesson-essay-content">
                    <div class="question-text mb-3">
                        <h5>السؤال:</h5>
                        <p>${lesson.content_data.question}</p>
                    </div>
                    <div class="essay-info">
                        <p><strong>الحد الأقصى للكلمات:</strong> ${lesson.content_data.max_words}</p>
                        ${lesson.content_data.rubric ? `<div><strong>معايير التقييم:</strong><p class="text-muted">${lesson.content_data.rubric}</p></div>` : ''}
                    </div>
                </div>
            `;
            break;
    }
    
    container.html(html);
    
    // إخفاء رسالة الترحيب
    $('#welcomeMessage').hide();
    container.show();
    
    // تهيئة مشغل الفيديو المتقدم إذا كان موجوداً
    if (lesson.content_type === 'video' && lesson.content_data && lesson.content_data.youtube_id) {
        initAdvancedVideoPlayer(lesson.content_data.youtube_id);
    }
}

function initAdvancedVideoPlayer(youtubeId) {
    // إنشاء مشغل فيديو جديد
    advancedPlayer = new AdvancedVideoPlayer('advancedVideoPlayer', youtubeId, {
        autoplay: false,
        showControls: false,
        enableKeyboard: true,
        onReady: function(event) {
            console.log('مشغل الفيديو المتقدم جاهز للمعاينة');
        }
    });
}

function showAlert(message, type) {
    // عرض رسالة تنبيه
    const alertClass = type === 'danger' ? 'alert-danger' : 'alert-success';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // إضافة الرسالة في أعلى الصفحة
    $('.preview-body').prepend(alertHtml);
    
    // إزالة الرسالة بعد 5 ثوان
    setTimeout(() => {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
{% endblock %}
