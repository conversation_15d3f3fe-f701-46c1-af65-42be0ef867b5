/**
 * تصميم نظام البحث والتصفية المتقدم
 */

/* ===== حاوي البحث الرئيسي ===== */
.advanced-search-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.search-header {
    text-align: center;
    color: white;
    margin-bottom: 2rem;
}

.search-header h2 {
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.search-header p {
    opacity: 0.9;
    font-size: 1.1rem;
}

/* ===== شريط البحث الرئيسي ===== */
.main-search-bar {
    position: relative;
    margin-bottom: 1.5rem;
}

.search-input-group {
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border-radius: 50px;
    padding: 0.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.search-input-group:focus-within {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.search-input-group .search-icon {
    color: #667eea;
    font-size: 1.2rem;
    margin: 0 1rem;
}

.search-input-group input {
    border: none;
    outline: none;
    flex: 1;
    padding: 1rem 0;
    font-size: 1.1rem;
    background: transparent;
}

.search-input-group input::placeholder {
    color: #adb5bd;
}

.search-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    border-radius: 50px;
    padding: 1rem 2rem;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* ===== فلاتر البحث المتقدمة ===== */
.advanced-filters {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
}

.filters-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.filters-toggle:hover {
    color: #f8f9fa;
}

.filters-toggle i {
    transition: transform 0.3s ease;
}

.filters-toggle[aria-expanded="true"] i {
    transform: rotate(180deg);
}

.filter-group {
    margin-bottom: 1rem;
}

.filter-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    display: block;
}

.filter-group select,
.filter-group input {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem;
    transition: all 0.3s ease;
    width: 100%;
}

.filter-group select:focus,
.filter-group input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    outline: none;
}

/* ===== أزرار الترتيب ===== */
.sort-controls {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.sort-btn {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 25px;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    color: #495057;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.sort-btn:hover {
    background: #e9ecef;
    border-color: #667eea;
}

.sort-btn.active {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

.sort-btn.active::after {
    content: '';
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
}

.sort-btn.active.asc::after {
    border-bottom: 6px solid white;
}

.sort-btn.active.desc::after {
    border-top: 6px solid white;
}

/* ===== أزرار الإجراءات ===== */
.search-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    margin-top: 1rem;
}

.action-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 25px;
    padding: 0.5rem 1rem;
    color: white;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* ===== إحصائيات البحث ===== */
.search-stats {
    margin-bottom: 1.5rem;
}

.search-stats .badge {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
    border-radius: 25px;
}

/* ===== نتائج البحث ===== */
.search-results {
    min-height: 400px;
}

.course-card {
    border: none;
    border-radius: 15px;
    transition: all 0.3s ease;
    overflow: hidden;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.course-card .card-body {
    padding: 1.5rem;
}

.course-card .card-title {
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.course-meta {
    border-top: 1px solid #f8f9fa;
    padding-top: 1rem;
}

.course-meta .fas {
    width: 16px;
    text-align: center;
}

/* ===== حالة فارغة ===== */
.empty-state {
    padding: 3rem 1rem;
    text-align: center;
    color: #6c757d;
}

.empty-state i {
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h4 {
    color: #495057;
    margin-bottom: 1rem;
}

/* ===== التحميل ===== */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: #6c757d;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
    margin-bottom: 1rem;
}

/* ===== التصفح (Pagination) ===== */
.pagination {
    margin-top: 2rem;
}

.pagination .page-link {
    border: none;
    border-radius: 10px;
    margin: 0 0.25rem;
    padding: 0.75rem 1rem;
    color: #667eea;
    font-weight: 600;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
    background: #667eea;
    border-color: #667eea;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* ===== الاستجابة للموبايل ===== */
@media (max-width: 768px) {
    .advanced-search-container {
        padding: 1.5rem;
        border-radius: 15px;
    }

    .search-input-group {
        border-radius: 15px;
    }

    .search-btn {
        border-radius: 15px;
        padding: 0.75rem 1.5rem;
    }

    .sort-controls {
        justify-content: center;
    }

    .search-actions {
        flex-direction: column;
        align-items: center;
    }

    .action-btn {
        width: 100%;
        max-width: 200px;
    }

    .course-card {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .advanced-search-container {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .search-header h2 {
        font-size: 1.5rem;
    }

    .search-header p {
        font-size: 1rem;
    }

    .search-input-group input {
        padding: 0.75rem 0;
        font-size: 1rem;
    }

    .advanced-filters {
        padding: 1rem;
    }

    .filter-group select,
    .filter-group input {
        padding: 0.5rem;
    }

    .sort-btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
}

/* ===== تحسينات الوصولية ===== */
.search-input-group:focus-within,
.filter-group select:focus,
.filter-group input:focus {
    outline: 3px solid rgba(102, 126, 234, 0.3);
    outline-offset: 2px;
}

.sort-btn:focus,
.action-btn:focus {
    outline: 3px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
}

/* ===== تأثيرات الحركة ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.course-card {
    animation: fadeInUp 0.5s ease forwards;
}

.course-card:nth-child(1) { animation-delay: 0.1s; }
.course-card:nth-child(2) { animation-delay: 0.2s; }
.course-card:nth-child(3) { animation-delay: 0.3s; }
.course-card:nth-child(4) { animation-delay: 0.4s; }
.course-card:nth-child(5) { animation-delay: 0.5s; }
.course-card:nth-child(6) { animation-delay: 0.6s; }

/* ===== تحسينات الأداء ===== */
.course-card,
.search-input-group,
.sort-btn,
.action-btn {
    will-change: transform;
}

.advanced-search-container {
    contain: layout style paint;
}

/* ===== الوضع المظلم ===== */
@media (prefers-color-scheme: dark) {
    .advanced-filters {
        background: rgba(33, 37, 41, 0.95);
        color: #f8f9fa;
    }

    .filter-group label {
        color: #f8f9fa;
    }

    .filter-group select,
    .filter-group input {
        background: #495057;
        border-color: #6c757d;
        color: #f8f9fa;
    }

    .course-card {
        background: #343a40;
        color: #f8f9fa;
    }

    .course-card .card-title {
        color: #f8f9fa;
    }

    .empty-state {
        color: #adb5bd;
    }

    .empty-state h4 {
        color: #f8f9fa;
    }
}
