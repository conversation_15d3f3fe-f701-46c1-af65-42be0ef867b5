#!/usr/bin/env python3
"""
سكريبت تشغيل الاختبارات الشاملة
Comprehensive Test Runner Script
"""

import os
import sys
import unittest
import time
import json
from datetime import datetime
import subprocess

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "=" * 60)
    print(f"🧪 {title}")
    print("=" * 60)

def print_section(title):
    """طباعة قسم فرعي"""
    print(f"\n📋 {title}")
    print("-" * 40)

def run_system_tests():
    """تشغيل اختبارات النظام الشاملة"""
    print_section("اختبارات النظام الشاملة")
    
    try:
        # تشغيل اختبارات النظام
        result = subprocess.run([
            sys.executable, '-m', 'unittest',
            'tests.test_system_comprehensive_fixed', '-v'
        ], capture_output=True, text=True, cwd=os.getcwd())
        
        if result.returncode == 0:
            print("✅ نجحت جميع اختبارات النظام")
            print(result.stdout)
        else:
            print("❌ فشلت بعض اختبارات النظام")
            print(result.stderr)
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل اختبارات النظام: {e}")
        return False

def run_bot_tests():
    """تشغيل اختبارات البوت"""
    print_section("اختبارات تكامل البوت")
    
    try:
        # تشغيل اختبارات البوت
        result = subprocess.run([
            sys.executable, '-m', 'unittest',
            'tests.test_bot_integration_fixed', '-v'
        ], capture_output=True, text=True, cwd=os.getcwd())
        
        if result.returncode == 0:
            print("✅ نجحت جميع اختبارات البوت")
            print(result.stdout)
        else:
            print("❌ فشلت بعض اختبارات البوت")
            print(result.stderr)
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل اختبارات البوت: {e}")
        return False

def check_dependencies():
    """فحص التبعيات المطلوبة"""
    print_section("فحص التبعيات")
    
    required_packages = [
        'flask',
        'firebase-admin',
        'python-dotenv',
        'pyjwt',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} مثبت")
        except ImportError:
            print(f"❌ {package} غير مثبت")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ المكتبات المفقودة: {', '.join(missing_packages)}")
        print("يرجى تثبيتها باستخدام: pip install -r requirements.txt")
        return False
    
    return True

def check_environment():
    """فحص متغيرات البيئة"""
    print_section("فحص متغيرات البيئة")
    
    required_env_vars = [
        'FLASK_SECRET_KEY',
        'FIREBASE_DATABASE_URL'
    ]
    
    missing_vars = []
    
    for var in required_env_vars:
        if os.getenv(var):
            print(f"✅ {var} موجود")
        else:
            print(f"❌ {var} غير موجود")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n⚠️ متغيرات البيئة المفقودة: {', '.join(missing_vars)}")
        print("يرجى إنشاء ملف .env مع القيم المطلوبة")
        return False
    
    return True

def check_file_structure():
    """فحص هيكل الملفات"""
    print_section("فحص هيكل الملفات")
    
    required_files = [
        'app.py',
        'requirements.txt',
        'utils/firebase_utils.py',
        'utils/auth_utils.py',
        'templates/base.html',
        'static/css/main.css',
        'static/js/main.js'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} موجود")
        else:
            print(f"❌ {file_path} غير موجود")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️ الملفات المفقودة: {', '.join(missing_files)}")
        return False
    
    return True

def run_performance_tests():
    """تشغيل اختبارات الأداء"""
    print_section("اختبارات الأداء")
    
    try:
        # اختبار سرعة استيراد الوحدات
        start_time = time.time()
        
        import app
        import utils.firebase_utils
        import utils.auth_utils
        
        import_time = time.time() - start_time
        
        print(f"✅ وقت استيراد الوحدات: {import_time:.3f} ثانية")
        
        if import_time < 2.0:
            print("✅ سرعة الاستيراد مقبولة")
            return True
        else:
            print("⚠️ سرعة الاستيراد بطيئة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الأداء: {e}")
        return False

def run_security_tests():
    """تشغيل اختبارات الأمان"""
    print_section("اختبارات الأمان")
    
    security_checks = []
    
    # فحص ملف .env
    if os.path.exists('.env'):
        with open('.env', 'r') as f:
            env_content = f.read()
            if 'SECRET_KEY' in env_content and len(env_content.split('SECRET_KEY=')[1].split('\n')[0]) > 20:
                print("✅ مفتاح الأمان قوي")
                security_checks.append(True)
            else:
                print("❌ مفتاح الأمان ضعيف")
                security_checks.append(False)
    else:
        print("⚠️ ملف .env غير موجود")
        security_checks.append(False)
    
    # فحص ملفات Python للثغرات الأمنية الأساسية
    python_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    dangerous_patterns = ['eval(', 'exec(', 'os.system(', 'subprocess.call(']
    security_issues = []
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                for pattern in dangerous_patterns:
                    if pattern in content:
                        security_issues.append(f"{file_path}: {pattern}")
        except:
            continue
    
    if security_issues:
        print("⚠️ مشاكل أمنية محتملة:")
        for issue in security_issues:
            print(f"  - {issue}")
        security_checks.append(False)
    else:
        print("✅ لم يتم العثور على مشاكل أمنية واضحة")
        security_checks.append(True)
    
    return all(security_checks)

def generate_test_report(results):
    """إنشاء تقرير الاختبارات"""
    print_section("تقرير الاختبارات")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'results': results,
        'summary': {
            'total_tests': len(results),
            'passed': sum(1 for r in results.values() if r),
            'failed': sum(1 for r in results.values() if not r)
        }
    }
    
    # حفظ التقرير
    os.makedirs('test_reports', exist_ok=True)
    report_file = f"test_reports/test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"📄 تم حفظ التقرير في: {report_file}")
    
    # طباعة ملخص
    print(f"\n📊 ملخص النتائج:")
    print(f"   إجمالي الاختبارات: {report['summary']['total_tests']}")
    print(f"   نجح: {report['summary']['passed']}")
    print(f"   فشل: {report['summary']['failed']}")
    
    success_rate = (report['summary']['passed'] / report['summary']['total_tests']) * 100
    print(f"   معدل النجاح: {success_rate:.1f}%")
    
    return success_rate >= 80  # 80% معدل نجاح مقبول

def main():
    """الدالة الرئيسية"""
    print_header("اختبارات النظام الشاملة")
    print(f"🕐 بدء الاختبارات في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # نتائج الاختبارات
    results = {}
    
    # 1. فحص التبعيات
    results['dependencies'] = check_dependencies()
    
    # 2. فحص متغيرات البيئة
    results['environment'] = check_environment()
    
    # 3. فحص هيكل الملفات
    results['file_structure'] = check_file_structure()
    
    # 4. اختبارات الأداء
    results['performance'] = run_performance_tests()
    
    # 5. اختبارات الأمان
    results['security'] = run_security_tests()
    
    # 6. اختبارات النظام
    if all([results['dependencies'], results['environment'], results['file_structure']]):
        results['system_tests'] = run_system_tests()
    else:
        print("⚠️ تم تخطي اختبارات النظام بسبب فشل الفحوصات الأولية")
        results['system_tests'] = False
    
    # 7. اختبارات البوت
    results['bot_tests'] = run_bot_tests()
    
    # إنشاء التقرير
    overall_success = generate_test_report(results)
    
    # النتيجة النهائية
    print_header("النتيجة النهائية")
    
    if overall_success:
        print("🎉 تم اجتياز جميع الاختبارات بنجاح!")
        print("✅ النظام جاهز للاستخدام")
        return 0
    else:
        print("❌ فشلت بعض الاختبارات")
        print("⚠️ يرجى مراجعة التقرير وإصلاح المشاكل")
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
