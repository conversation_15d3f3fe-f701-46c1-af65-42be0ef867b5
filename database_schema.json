{"database_name": "منصة الكورسات التعليمية", "version": "1.0.0", "description": "مخطط قاعدة البيانات لمنصة الكورسات التعليمية مع بوت التليجرام", "created_at": "2025-01-01", "collections": {"users": {"description": "بيانات المستخدمين (أدمن، مدرسين، طلاب)", "structure": {"email": {"type": "string", "required": true, "unique": true, "description": "البريد الإلكتروني للمستخدم"}, "telegram_id": {"type": "string", "required": true, "unique": true, "description": "معرف التليجرام الفريد"}, "role": {"type": "string", "required": true, "enum": ["admin", "instructor", "student"], "description": "دور المستخدم في النظام"}, "first_name": {"type": "string", "required": true, "description": "الاسم الأول"}, "last_name": {"type": "string", "required": false, "description": "الاسم الأخير"}, "full_name": {"type": "string", "computed": true, "description": "الاسم الكامل (محسوب تلقائياً)"}, "specialization_id": {"type": "string", "required": false, "reference": "specializations", "description": "معرف التخصص (للمدرسين والطلاب)"}, "permissions": {"type": "object", "required": false, "description": "صلاحيات المستخدم (للمدرسين)", "properties": {"can_create_courses": "boolean", "can_manage_students": "boolean", "allowed_stages": "array of numbers", "can_create_general_courses": "boolean"}}, "active": {"type": "boolean", "default": true, "description": "حالة نشاط المستخدم"}, "created_at": {"type": "string", "format": "ISO 8601", "auto_generated": true, "description": "تاريخ إنشاء الحساب"}, "updated_at": {"type": "string", "format": "ISO 8601", "auto_updated": true, "description": "تاريخ آخر تحديث"}, "last_login": {"type": "string", "format": "ISO 8601", "required": false, "description": "تاريخ آخر تسجيل دخول"}, "profile": {"type": "object", "description": "بيانات الملف الشخصي", "properties": {"avatar_url": "string", "bio": "string", "phone": "string"}}}, "indexes": ["telegram_id", "email", "role", "specialization_id"]}, "specializations": {"description": "التخصصات المتاحة في المنصة", "structure": {"name": {"type": "string", "required": true, "description": "اسم التخصص بالعربية"}, "name_en": {"type": "string", "required": true, "description": "اسم التخصص بالإنجليزية"}, "icon": {"type": "string", "required": true, "description": "أيقونة التخصص (Font Awesome class)"}, "description": {"type": "string", "required": false, "description": "وصف التخصص"}, "stages": {"type": "array", "items": "number", "default": [2, 3, 4], "description": "المراحل الدراسية المتاحة"}, "active": {"type": "boolean", "default": true, "description": "حالة نشاط التخصص"}, "created_at": {"type": "string", "format": "ISO 8601", "auto_generated": true}, "updated_at": {"type": "string", "format": "ISO 8601", "auto_updated": true}, "courses_count": {"type": "number", "default": 0, "computed": true, "description": "عد<PERSON> الكورسات في التخصص"}, "students_count": {"type": "number", "default": 0, "computed": true, "description": "<PERSON><PERSON><PERSON> الطلاب المسجلين"}}, "indexes": ["name", "active"]}, "courses": {"description": "الكورسات التعليمية", "structure": {"title": {"type": "string", "required": true, "description": "عنوان الكورس"}, "description": {"type": "string", "required": false, "description": "وصف الكورس"}, "instructor_id": {"type": "string", "required": true, "reference": "users", "description": "معرف المدرس المسؤول"}, "specialization_id": {"type": "string", "required": false, "reference": "specializations", "description": "معرف التخصص (null للكورسات العامة)"}, "stage": {"type": "number", "required": true, "enum": [2, 3, 4], "description": "المرحلة الدراسية"}, "is_general": {"type": "boolean", "default": false, "description": "كورس عام (متاح لجميع التخصصات)"}, "status": {"type": "string", "enum": ["draft", "published", "archived"], "default": "draft", "description": "حالة الكورس"}, "thumbnail_url": {"type": "string", "required": false, "description": "رابط الصورة المصغرة"}, "created_at": {"type": "string", "format": "ISO 8601", "auto_generated": true}, "updated_at": {"type": "string", "format": "ISO 8601", "auto_updated": true}, "published_at": {"type": "string", "format": "ISO 8601", "required": false, "description": "تاريخ النشر"}, "settings": {"type": "object", "description": "إعدادات الكورس", "properties": {"allow_comments": "boolean", "allow_downloads": "boolean", "require_completion_order": "boolean"}}, "stats": {"type": "object", "computed": true, "description": "إحصائيات الكورس", "properties": {"total_lessons": "number", "total_duration": "number", "enrolled_students": "number", "completion_rate": "number"}}}, "indexes": ["instructor_id", "specialization_id", "stage", "status", "is_general"]}, "lessons": {"description": "دروس الكورسات", "structure": {"course_id": {"type": "string", "required": true, "reference": "courses", "description": "معرف الكورس"}, "title": {"type": "string", "required": true, "description": "عنوان الدرس"}, "content_type": {"type": "string", "enum": ["video", "text", "quiz", "assignment"], "required": true, "description": "نوع محتوى الدرس"}, "content_data": {"type": "object", "required": true, "description": "بيانات المحتوى حسب النوع"}, "order": {"type": "number", "required": true, "description": "ترتيب الدرس في الكورس"}, "duration": {"type": "number", "default": 0, "description": "مدة الدرس بالثواني"}, "created_at": {"type": "string", "format": "ISO 8601", "auto_generated": true}, "updated_at": {"type": "string", "format": "ISO 8601", "auto_updated": true}, "settings": {"type": "object", "description": "إعدادات الدرس", "properties": {"is_preview": "boolean", "require_previous_completion": "boolean"}}}, "indexes": ["course_id", "order"]}, "enrollments": {"description": "تسجيلات الطلاب في الكورسات", "structure": {"student_id": {"type": "string", "required": true, "reference": "users", "description": "معر<PERSON> الطالب"}, "course_id": {"type": "string", "required": true, "reference": "courses", "description": "معرف الكورس"}, "status": {"type": "string", "enum": ["active", "completed", "suspended"], "default": "active", "description": "حالة التسجيل"}, "enrolled_at": {"type": "string", "format": "ISO 8601", "auto_generated": true, "description": "تاريخ التسجيل"}, "updated_at": {"type": "string", "format": "ISO 8601", "auto_updated": true}, "completed_at": {"type": "string", "format": "ISO 8601", "required": false, "description": "تاريخ إكمال الكورس"}, "progress": {"type": "object", "description": "تقدم الطالب", "properties": {"completed_lessons": "array of strings", "current_lesson": "string", "completion_percentage": "number", "total_watch_time": "number"}}}, "indexes": ["student_id", "course_id", "status", ["student_id", "course_id"]]}, "activation_codes": {"description": "أكواد تفعيل الكورسات", "structure": {"code": {"type": "string", "required": true, "unique": true, "description": "<PERSON>و<PERSON> التفعيل"}, "course_id": {"type": "string", "required": true, "reference": "courses", "description": "معرف الكورس"}, "created_by": {"type": "string", "required": true, "reference": "users", "description": "من<PERSON><PERSON> الكود"}, "max_uses": {"type": "number", "default": 1, "description": "الح<PERSON> الأقصى لاستخدام الكود"}, "current_uses": {"type": "number", "default": 0, "description": "عدد الاستخدامات الحالية"}, "active": {"type": "boolean", "default": true, "description": "حالة نشاط الكود"}, "created_at": {"type": "string", "format": "ISO 8601", "auto_generated": true}, "expires_at": {"type": "string", "format": "ISO 8601", "required": false, "description": "تاريخ انتهاء الكود"}, "used_by": {"type": "array", "items": "string", "description": "قائمة معرفات المستخدمين الذين استخدموا الكود"}}, "indexes": ["code", "course_id", "created_by", "active"]}, "invitation_links": {"description": "روابط الدعوة لإنشاء الحسابات", "structure": {"link_id": {"type": "string", "required": true, "unique": true, "description": "معرف الرابط الفريد"}, "created_by": {"type": "string", "required": true, "reference": "users", "description": "منشئ الرابط"}, "link_type": {"type": "string", "enum": ["instructor_signup", "student_invite"], "required": true, "description": "نوع الرابط"}, "target_data": {"type": "object", "required": true, "description": "بيانات إضافية حسب نوع الرابط"}, "max_uses": {"type": "number", "default": 1, "description": "الح<PERSON> الأقصى للاستخدام"}, "current_uses": {"type": "number", "default": 0, "description": "عدد الاستخدامات الحالية"}, "active": {"type": "boolean", "default": true, "description": "حالة نشاط الرابط"}, "created_at": {"type": "string", "format": "ISO 8601", "auto_generated": true}, "expires_at": {"type": "string", "format": "ISO 8601", "required": false, "description": "تاريخ انتهاء الرابط"}, "used_by": {"type": "array", "items": "string", "description": "قائمة معرفات المستخدمين الذين استخدموا الرابط"}}, "indexes": ["link_id", "created_by", "link_type", "active"]}, "system_info": {"description": "معلومات النظام والإعدادات العامة", "structure": {"initialized_at": {"type": "string", "format": "ISO 8601", "description": "تاريخ تهيئة النظام"}, "version": {"type": "string", "description": "إصدار النظام"}, "platform_name": {"type": "string", "description": "اسم المنصة"}}}}, "security_rules": {"description": "قواعد الأمان لقاعدة البيانات", "rules": {"users": {"read": "auth != null && (auth.uid == $uid || auth.token.role == 'admin')", "write": "auth != null && (auth.uid == $uid || auth.token.role == 'admin')"}, "specializations": {"read": "auth != null", "write": "auth != null && auth.token.role == 'admin'"}, "courses": {"read": "auth != null", "write": "auth != null && (auth.token.role == 'admin' || auth.token.role == 'instructor')"}, "lessons": {"read": "auth != null", "write": "auth != null && (auth.token.role == 'admin' || auth.token.role == 'instructor')"}, "enrollments": {"read": "auth != null && (auth.uid == resource.data.student_id || auth.token.role == 'admin' || auth.token.role == 'instructor')", "write": "auth != null && (auth.uid == resource.data.student_id || auth.token.role == 'admin')"}, "activation_codes": {"read": "auth != null && (auth.token.role == 'admin' || auth.token.role == 'instructor')", "write": "auth != null && (auth.token.role == 'admin' || auth.token.role == 'instructor')"}, "invitation_links": {"read": "auth != null && auth.token.role == 'admin'", "write": "auth != null && auth.token.role == 'admin'"}}}}