"""
معالجات إنشاء حسابات المدرسين
Instructor Account Creation Handlers

هذا الملف يحتوي على معالجات البوت الخاصة بإنشاء روابط دعوة المدرسين
ويتضمن محادثة تفاعلية لجمع البيانات المطلوبة وإنشاء الروابط الآمنة
"""

import logging
import sys
import os
from typing import Dict, Any

# إضافة المجلد الرئيسي للمسار
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from utils.firebase_utils import get_firebase_manager
from utils.user_creation_utils import UserCreationManager
from models.database_models import UserRole
from bot.utils.invitation_utils import get_bot_invitation_manager

# إعداد نظام التسجيل
logger = logging.getLogger(__name__)

# حالات المحادثة
CONVERSATION_STATES = {
    'WAITING_FOR_NAME': 'waiting_for_name',
    'WAITING_FOR_SPECIALIZATION': 'waiting_for_specialization',
    'WAITING_FOR_CONFIRMATION': 'waiting_for_confirmation'
}

# تخزين مؤقت لحالات المحادثة
user_conversations = {}

class InstructorCreationHandler:
    """معالج إنشاء حسابات المدرسين"""
    
    def __init__(self, bot):
        """تهيئة المعالج"""
        self.bot = bot
        self.firebase_manager = get_firebase_manager()
        self.invitation_manager = get_bot_invitation_manager()
        self.user_creation_manager = UserCreationManager()

        # الحصول على التخصصات المتاحة
        self.specializations = self._get_specializations()
    
    def _get_specializations(self) -> Dict[str, Any]:
        """الحصول على قائمة التخصصات المتاحة"""
        try:
            specializations = self.firebase_manager.get_all_specializations()
            if specializations and isinstance(specializations, dict):
                return specializations
            else:
                # التخصصات الافتراضية إذا لم توجد في قاعدة البيانات أو كانت قائمة
                logger.info("استخدام التخصصات الافتراضية")
                return {
                    'medical_analysis': {
                        'name': 'التحليل الطبي',
                        'name_en': 'Medical Analysis',
                        'icon': '🔬'
                    },
                    'radiology': {
                        'name': 'الأشعة',
                        'name_en': 'Radiology',
                        'icon': '🩻'
                    },
                    'anesthesia': {
                        'name': 'التخدير',
                        'name_en': 'Anesthesia',
                        'icon': '💉'
                    }
                }
        except Exception as e:
            logger.error(f"خطأ في الحصول على التخصصات: {e}")
            # إرجاع التخصصات الافتراضية في حالة الخطأ
            return {
                'medical_analysis': {
                    'name': 'التحليل الطبي',
                    'name_en': 'Medical Analysis',
                    'icon': '🔬'
                },
                'radiology': {
                    'name': 'الأشعة',
                    'name_en': 'Radiology',
                    'icon': '🩻'
                },
                'anesthesia': {
                    'name': 'التخدير',
                    'name_en': 'Anesthesia',
                    'icon': '💉'
                }
            }
    
    def start_instructor_creation(self, message, bot_owner_id: str):
        """بدء عملية إنشاء حساب مدرس"""
        user_id = str(message.from_user.id)
        
        # التحقق من صلاحية المستخدم
        if user_id != bot_owner_id:
            self.bot.send_message(message.chat.id, "❌ غير مسموح! هذه الوظيفة للأدمن فقط.")
            return
        
        # بدء المحادثة
        user_conversations[user_id] = {
            'state': CONVERSATION_STATES['WAITING_FOR_NAME'],
            'data': {}
        }
        
        welcome_text = """
🎓 إنشاء حساب مدرس جديد

سأقوم بإرشادك خلال عملية إنشاء رابط دعوة لمدرس جديد.

📝 أولاً، أدخل اسم المدرس الكامل:
        """
        
        self.bot.send_message(message.chat.id, welcome_text)
    
    def handle_instructor_name(self, message, bot_owner_id: str):
        """معالجة إدخال اسم المدرس"""
        user_id = str(message.from_user.id)
        
        # التحقق من حالة المحادثة
        if (user_id not in user_conversations or 
            user_conversations[user_id]['state'] != CONVERSATION_STATES['WAITING_FOR_NAME']):
            return False
        
        instructor_name = message.text.strip()
        
        # التحقق من صحة الاسم
        if len(instructor_name) < 2:
            self.bot.send_message(message.chat.id, "❌ يرجى إدخال اسم صحيح (حرفين على الأقل)")
            return True
        
        # حفظ الاسم والانتقال للخطوة التالية
        user_conversations[user_id]['data']['instructor_name'] = instructor_name
        user_conversations[user_id]['state'] = CONVERSATION_STATES['WAITING_FOR_SPECIALIZATION']
        
        # عرض التخصصات المتاحة
        self._show_specializations(message.chat.id)
        return True
    
    def _show_specializations(self, chat_id: int):
        """عرض التخصصات المتاحة للاختيار"""
        try:
            from telebot import types
            
            text = """
🎓 اختر تخصص المدرس:
            """
            
            markup = types.InlineKeyboardMarkup(row_width=1)
            
            for spec_id, spec_data in self.specializations.items():
                icon = spec_data.get('icon', '📚')
                name = spec_data.get('name', spec_id)
                
                button = types.InlineKeyboardButton(
                    f"{icon} {name}",
                    callback_data=f"select_spec_{spec_id}"
                )
                markup.add(button)
            
            # زر الإلغاء
            cancel_button = types.InlineKeyboardButton(
                "❌ إلغاء",
                callback_data="cancel_instructor_creation"
            )
            markup.add(cancel_button)
            
            self.bot.send_message(chat_id, text, reply_markup=markup)
            
        except Exception as e:
            logger.error(f"خطأ في عرض التخصصات: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض التخصصات")
    
    def handle_specialization_selection(self, call, bot_owner_id: str):
        """معالجة اختيار التخصص"""
        user_id = str(call.from_user.id)
        
        # التحقق من الصلاحية
        if user_id != bot_owner_id:
            self.bot.answer_callback_query(call.id, "❌ غير مسموح!")
            return False
        
        # التحقق من حالة المحادثة
        if (user_id not in user_conversations or 
            user_conversations[user_id]['state'] != CONVERSATION_STATES['WAITING_FOR_SPECIALIZATION']):
            self.bot.answer_callback_query(call.id, "❌ حالة محادثة غير صحيحة")
            return False
        
        # استخراج معرف التخصص
        if not call.data.startswith('select_spec_'):
            return False
        
        specialization_id = call.data.replace('select_spec_', '')
        
        # التحقق من وجود التخصص
        if specialization_id not in self.specializations:
            self.bot.answer_callback_query(call.id, "❌ تخصص غير صحيح")
            return False
        
        # حفظ التخصص والانتقال للتأكيد
        user_conversations[user_id]['data']['specialization_id'] = specialization_id
        user_conversations[user_id]['state'] = CONVERSATION_STATES['WAITING_FOR_CONFIRMATION']
        
        self.bot.answer_callback_query(call.id, "✅ تم اختيار التخصص")
        
        # عرض ملخص البيانات للتأكيد
        self._show_confirmation(call.message.chat.id, user_id)
        return True
    
    def _show_confirmation(self, chat_id: int, user_id: str):
        """عرض ملخص البيانات للتأكيد"""
        try:
            from telebot import types
            
            conversation_data = user_conversations[user_id]['data']
            instructor_name = conversation_data['instructor_name']
            specialization_id = conversation_data['specialization_id']
            
            # الحصول على بيانات التخصص
            spec_data = self.specializations.get(specialization_id, {})
            spec_name = spec_data.get('name', specialization_id)
            spec_icon = spec_data.get('icon', '📚')
            
            confirmation_text = f"""
📋 تأكيد بيانات المدرس الجديد:

👨‍🏫 الاسم: {instructor_name}
{spec_icon} التخصص: {spec_name}

هل تريد إنشاء رابط الدعوة لهذا المدرس؟
            """
            
            markup = types.InlineKeyboardMarkup(row_width=2)
            markup.add(
                types.InlineKeyboardButton("✅ تأكيد", callback_data="confirm_instructor_creation"),
                types.InlineKeyboardButton("❌ إلغاء", callback_data="cancel_instructor_creation")
            )
            
            self.bot.send_message(chat_id, confirmation_text, reply_markup=markup)
            
        except Exception as e:
            logger.error(f"خطأ في عرض التأكيد: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في عرض التأكيد")
    
    def handle_confirmation(self, call, bot_owner_id: str):
        """معالجة تأكيد إنشاء الرابط"""
        user_id = str(call.from_user.id)
        
        # التحقق من الصلاحية
        if user_id != bot_owner_id:
            self.bot.answer_callback_query(call.id, "❌ غير مسموح!")
            return False
        
        # التحقق من حالة المحادثة
        if (user_id not in user_conversations or 
            user_conversations[user_id]['state'] != CONVERSATION_STATES['WAITING_FOR_CONFIRMATION']):
            self.bot.answer_callback_query(call.id, "❌ حالة محادثة غير صحيحة")
            return False
        
        if call.data == "confirm_instructor_creation":
            # إنشاء رابط الدعوة
            self._create_invitation_link(call.message.chat.id, user_id)
        elif call.data == "cancel_instructor_creation":
            # إلغاء العملية
            self._cancel_creation(call.message.chat.id, user_id)
        
        self.bot.answer_callback_query(call.id, "✅ تم")
        return True
    
    def _create_invitation_link(self, chat_id: int, user_id: str):
        """إنشاء رابط الدعوة الفعلي"""
        try:
            conversation_data = user_conversations[user_id]['data']
            instructor_name = conversation_data['instructor_name']
            specialization_id = conversation_data['specialization_id']
            
            # إنشاء رابط الدعوة
            success, invitation_url, message = self.invitation_manager.create_instructor_invitation_link(
                created_by=user_id,
                instructor_name=instructor_name,
                specialization_id=specialization_id,
                expires_hours=24
            )
            
            if success:
                # الحصول على بيانات التخصص
                spec_data = self.specializations.get(specialization_id, {})
                spec_name = spec_data.get('name', specialization_id)
                spec_icon = spec_data.get('icon', '📚')
                
                success_text = f"""
✅ تم إنشاء رابط الدعوة بنجاح!

👨‍🏫 المدرس: {instructor_name}
{spec_icon} التخصص: {spec_name}

🔗 رابط الدعوة:
{invitation_url}

⏰ صالح لمدة: 24 ساعة
🔢 عدد الاستخدامات: مرة واحدة فقط

📋 تعليمات:
• أرسل هذا الرابط للمدرس
• عند الضغط على الرابط سيفتح البوت مباشرة
• سيتم إنشاء حسابه تلقائياً وإعطاؤه بيانات الدخول
• الرابط سينتهي بعد الاستخدام أو بعد 24 ساعة
                """
                
                self.bot.send_message(chat_id, success_text)
            else:
                error_text = f"❌ فشل في إنشاء رابط الدعوة: {message}"
                self.bot.send_message(chat_id, error_text)
            
            # تنظيف حالة المحادثة
            if user_id in user_conversations:
                del user_conversations[user_id]
                
        except Exception as e:
            logger.error(f"خطأ في إنشاء رابط الدعوة: {e}")
            self.bot.send_message(chat_id, "❌ حدث خطأ في إنشاء رابط الدعوة")
            
            # تنظيف حالة المحادثة
            if user_id in user_conversations:
                del user_conversations[user_id]
    
    def _cancel_creation(self, chat_id: int, user_id: str):
        """إلغاء عملية إنشاء الرابط"""
        try:
            cancel_text = """
❌ تم إلغاء عملية إنشاء رابط الدعوة.

يمكنك البدء من جديد باستخدام زر "👨‍🏫 إنشاء حساب مدرس"
            """
            
            self.bot.send_message(chat_id, cancel_text)
            
            # تنظيف حالة المحادثة
            if user_id in user_conversations:
                del user_conversations[user_id]
                
        except Exception as e:
            logger.error(f"خطأ في إلغاء العملية: {e}")
    
    def handle_text_message(self, message, bot_owner_id: str) -> bool:
        """معالجة الرسائل النصية في المحادثة"""
        user_id = str(message.from_user.id)
        
        # التحقق من وجود محادثة نشطة
        if user_id not in user_conversations:
            return False
        
        conversation_state = user_conversations[user_id]['state']
        
        # توجيه الرسالة للمعالج المناسب
        if conversation_state == CONVERSATION_STATES['WAITING_FOR_NAME']:
            return self.handle_instructor_name(message, bot_owner_id)
        
        return False
    
    def handle_callback_query(self, call, bot_owner_id: str) -> bool:
        """معالجة الاستعلامات المضمنة"""
        # معالجة اختيار التخصص
        if call.data.startswith('select_spec_'):
            return self.handle_specialization_selection(call, bot_owner_id)
        
        # معالجة التأكيد أو الإلغاء
        if call.data in ['confirm_instructor_creation', 'cancel_instructor_creation']:
            return self.handle_confirmation(call, bot_owner_id)
        
        return False

    def handle_invitation_link(self, message, link_id: str):
        """معالجة رابط دعوة المدرس"""
        try:
            user_id = str(message.from_user.id)
            username = message.from_user.username or "غير محدد"
            first_name = message.from_user.first_name or "مستخدم"

            # التحقق من صحة رابط الدعوة
            is_valid, link_data, validation_message = self.invitation_manager.validate_invitation_link(link_id)

            if not is_valid:
                error_text = f"""
❌ رابط الدعوة غير صحيح

السبب: {validation_message}

إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع إدارة المنصة.
                """
                self.bot.send_message(message.chat.id, error_text)
                return

            # التحقق من نوع الرابط
            if link_data.get('link_type') != 'instructor_signup':
                self.bot.send_message(message.chat.id, "❌ هذا الرابط غير مخصص لإنشاء حسابات المدرسين")
                return

            # الحصول على بيانات المدرس من الرابط
            target_data = link_data.get('target_data', {})
            instructor_name = target_data.get('instructor_name')
            specialization_id = target_data.get('specialization_id')

            if not instructor_name or not specialization_id:
                self.bot.send_message(message.chat.id, "❌ بيانات الرابط غير مكتملة")
                return

            # التحقق من عدم وجود حساب مسبق لهذا المستخدم
            existing_user = self.firebase_manager.get_user_by_telegram_id(user_id)
            if existing_user:
                self.bot.send_message(message.chat.id, "❌ يوجد حساب مسجل مسبقاً بهذا المعرف")
                return

            # إنشاء حساب المدرس
            success, account_info, creation_message = self.user_creation_manager.create_instructor_account(
                first_name=instructor_name.split()[0] if instructor_name.split() else instructor_name,
                last_name=' '.join(instructor_name.split()[1:]) if len(instructor_name.split()) > 1 else "",
                telegram_id=user_id,
                specialization_id=specialization_id
            )

            if success:
                # تحديث حالة استخدام الرابط
                self.invitation_manager.use_invitation_link(link_id, user_id)

                # الحصول على بيانات التخصص
                spec_data = self.specializations.get(specialization_id, {})
                spec_name = spec_data.get('name', specialization_id)
                spec_icon = spec_data.get('icon', '📚')

                # إرسال بيانات الحساب للمدرس
                success_text = f"""
🎉 مرحباً بك في منصة الكورسات التعليمية!

تم إنشاء حسابك بنجاح كمدرس في تخصص {spec_icon} {spec_name}

📧 بيانات تسجيل الدخول:
• الإيميل: {account_info['email']}
• كلمة المرور: {account_info['password']}

🌐 رابط المنصة: {self.invitation_manager.platform_url}

⚠️ تعليمات مهمة:
• احتفظ بهذه البيانات في مكان آمن
• يمكنك تغيير كلمة المرور بعد تسجيل الدخول
• استخدم هذه البيانات للدخول إلى المنصة وإدارة كورساتك

🎓 مرحباً بك في فريق المدرسين!
                """

                self.bot.send_message(message.chat.id, success_text)

                logger.info(f"تم إنشاء حساب مدرس جديد عبر رابط الدعوة: {account_info['email']}")

            else:
                error_text = f"""
❌ فشل في إنشاء الحساب

السبب: {creation_message}

يرجى المحاولة مرة أخرى أو التواصل مع إدارة المنصة.
                """
                self.bot.send_message(message.chat.id, error_text)

        except Exception as e:
            logger.error(f"خطأ في معالجة رابط دعوة المدرس: {e}")
            self.bot.send_message(message.chat.id, "❌ حدث خطأ في النظام. يرجى المحاولة مرة أخرى.")
