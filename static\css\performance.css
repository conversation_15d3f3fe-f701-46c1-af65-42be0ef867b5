/* تحسينات الأداء والسرعة */

/* تحسين تحميل الخطوط */
@font-face {
    font-family: 'Cairo';
    font-display: swap;
    src: url('https://fonts.gstatic.com/s/cairo/v28/SLXgc1nY6HkvalIhTp2mxdt0UX8gfwhfAA.woff2') format('woff2');
}

/* تحسين الصور */
img {
    max-width: 100%;
    height: auto;
}

/* Lazy loading للصور */
img.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

img.lazy.loaded {
    opacity: 1;
}

/* تحسين الأنيميشن */
* {
    will-change: auto;
}

.animate {
    will-change: transform, opacity;
}

/* تحسين الانتقالات */
.transition-optimized {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* تحسين الجداول الكبيرة */
.table-performance {
    contain: layout style paint;
}

.table-performance tbody tr {
    contain: layout style;
}

/* تحسين النماذج */
.form-optimized input,
.form-optimized select,
.form-optimized textarea {
    will-change: auto;
}

.form-optimized input:focus,
.form-optimized select:focus,
.form-optimized textarea:focus {
    will-change: transform;
}

/* تحسين الأزرار */
.btn-optimized {
    transform: translateZ(0);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.btn-optimized:hover {
    transform: translateY(-1px) translateZ(0);
}

/* تحسين الكروت */
.card-optimized {
    contain: layout style paint;
    transform: translateZ(0);
}

/* تحسين القوائم المنسدلة */
.dropdown-menu-optimized {
    contain: layout style paint;
    transform: translateZ(0);
}

/* تحسين المودالات */
.modal-optimized .modal-dialog {
    transform: translateZ(0);
}

/* تحسين الـ Loading */
.loading-optimized {
    contain: layout style paint;
}

/* تحسين الـ Spinner */
.spinner-optimized {
    animation: spin 1s linear infinite;
    will-change: transform;
}

@keyframes spin {
    from { transform: rotate(0deg) translateZ(0); }
    to { transform: rotate(360deg) translateZ(0); }
}

/* تحسين الـ Scrolling */
.scroll-optimized {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    contain: layout style paint;
}

/* تحسين الـ Grid */
.grid-optimized {
    contain: layout style;
}

.grid-optimized .grid-item {
    contain: layout style paint;
}

/* تحسين الـ Flexbox */
.flex-optimized {
    contain: layout style;
}

/* تحسين الـ Navigation */
.nav-optimized {
    contain: layout style paint;
    transform: translateZ(0);
}

/* تحسين الـ Sidebar */
.sidebar-optimized {
    contain: layout style paint;
    transform: translateZ(0);
}

/* تحسين الـ Content */
.content-optimized {
    contain: layout style;
}

/* تحسين الـ Footer */
.footer-optimized {
    contain: layout style paint;
}

/* تحسين الـ Header */
.header-optimized {
    contain: layout style paint;
    transform: translateZ(0);
}

/* تحسين الـ Video Player */
.video-player-optimized {
    contain: layout style paint;
    transform: translateZ(0);
}

/* تحسين الـ Charts */
.chart-optimized {
    contain: layout style paint;
}

/* تحسين الـ Tabs */
.tabs-optimized {
    contain: layout style;
}

.tabs-optimized .tab-content {
    contain: layout style paint;
}

/* تحسين الـ Accordion */
.accordion-optimized {
    contain: layout style;
}

.accordion-optimized .accordion-item {
    contain: layout style paint;
}

/* تحسين الـ Carousel */
.carousel-optimized {
    contain: layout style paint;
    transform: translateZ(0);
}

/* تحسين الـ Progress Bars */
.progress-optimized {
    contain: layout style paint;
}

.progress-optimized .progress-bar {
    will-change: width;
}

/* تحسين الـ Badges */
.badge-optimized {
    contain: layout style paint;
}

/* تحسين الـ Alerts */
.alert-optimized {
    contain: layout style paint;
}

/* تحسين الـ Tooltips */
.tooltip-optimized {
    contain: layout style paint;
    transform: translateZ(0);
}

/* تحسين الـ Popovers */
.popover-optimized {
    contain: layout style paint;
    transform: translateZ(0);
}

/* تحسين الـ Breadcrumbs */
.breadcrumb-optimized {
    contain: layout style;
}

/* تحسين الـ Pagination */
.pagination-optimized {
    contain: layout style;
}

/* تحسين الـ List Groups */
.list-group-optimized {
    contain: layout style;
}

.list-group-optimized .list-group-item {
    contain: layout style paint;
}

/* تحسين الـ Cards */
.card-deck-optimized {
    contain: layout style;
}

.card-deck-optimized .card {
    contain: layout style paint;
}

/* تحسين الـ Media Objects */
.media-optimized {
    contain: layout style;
}

/* تحسين الـ Jumbotron */
.jumbotron-optimized {
    contain: layout style paint;
}

/* تحسين الـ Wells */
.well-optimized {
    contain: layout style paint;
}

/* تحسين الـ Panels */
.panel-optimized {
    contain: layout style;
}

.panel-optimized .panel-body {
    contain: layout style paint;
}

/* تحسين الـ Thumbnails */
.thumbnail-optimized {
    contain: layout style paint;
}

/* تحسين الـ Labels */
.label-optimized {
    contain: layout style paint;
}

/* تحسين الـ Input Groups */
.input-group-optimized {
    contain: layout style;
}

/* تحسين الـ Button Groups */
.btn-group-optimized {
    contain: layout style;
}

/* تحسين الـ Dropdowns */
.dropdown-optimized {
    contain: layout style;
}

/* تحسين الـ Navs */
.nav-tabs-optimized,
.nav-pills-optimized {
    contain: layout style;
}

/* تحسين الـ Navbar */
.navbar-optimized {
    contain: layout style paint;
    transform: translateZ(0);
}

/* تحسين الـ Wells */
.well-optimized {
    contain: layout style paint;
}

/* Critical CSS للتحميل السريع */
.critical-css {
    font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* تحسين الطباعة */
@media print {
    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    .no-print {
        display: none !important;
    }
}

/* تحسين الشاشات الصغيرة */
@media (max-width: 768px) {
    .mobile-optimized {
        contain: layout style;
    }
    
    .mobile-optimized img {
        max-width: 100%;
        height: auto;
    }
    
    .mobile-optimized .table-responsive {
        contain: layout style paint;
    }
}

/* تحسين الشاشات الكبيرة */
@media (min-width: 1200px) {
    .desktop-optimized {
        contain: layout style;
    }
}
