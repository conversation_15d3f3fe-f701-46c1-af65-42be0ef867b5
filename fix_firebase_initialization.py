#!/usr/bin/env python3
"""
إصلاح مشكلة تهيئة Firebase
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_firebase_initialization():
    """اختبار تهيئة Firebase"""
    
    print("🔍 اختبار تهيئة Firebase...")
    
    try:
        # استيراد FirebaseManager
        from utils.firebase_utils import firebase_manager
        
        print(f"📊 حالة Firebase Manager:")
        print(f"   مهيأ: {firebase_manager._initialized}")
        print(f"   قاعدة البيانات: {firebase_manager.database is not None}")
        
        if not firebase_manager._initialized:
            print("❌ Firebase غير مهيأ - محاولة إعادة التهيئة...")

            # استيراد الإعدادات
            from config import Config
            config = Config()
            config_dict = {
                'FIREBASE_CONFIG': config.FIREBASE_CONFIG,
                'FIREBASE_DATABASE_URL': config.FIREBASE_DATABASE_URL
            }

            # محاولة إعادة التهيئة
            firebase_manager.initialize(config_dict)

            print(f"📊 حالة Firebase بعد إعادة التهيئة:")
            print(f"   مهيأ: {firebase_manager._initialized}")
            print(f"   قاعدة البيانات: {firebase_manager.database is not None}")
        
        # اختبار جلب الكورسات
        instructor_id = "-OU9DAh78GJ6woLcC34G"
        print(f"\n📚 اختبار جلب كورسات المدرس {instructor_id}...")
        
        courses = firebase_manager.get_courses_by_instructor(instructor_id)
        print(f"📊 عدد الكورسات المرجعة: {len(courses)}")
        
        if courses:
            print("✅ الكورسات:")
            for i, course in enumerate(courses, 1):
                print(f"  {i}. {course.get('title', 'بدون عنوان')} - {course.get('status', 'غير محدد')}")
        else:
            print("❌ لا توجد كورسات")
            
            # اختبار مباشر لقاعدة البيانات
            print("\n🔍 اختبار مباشر لقاعدة البيانات...")
            if firebase_manager.database:
                try:
                    courses_direct = firebase_manager.database.child('courses').order_by_child('instructor_id').equal_to(instructor_id).get()
                    print(f"📊 نتيجة الاستعلام المباشر: {len(courses_direct) if courses_direct else 0}")
                    
                    if courses_direct:
                        print("✅ الكورسات من الاستعلام المباشر:")
                        for course_id, course_data in courses_direct.items():
                            print(f"  - {course_data.get('title', 'بدون عنوان')}")
                    else:
                        print("❌ لا توجد نتائج من الاستعلام المباشر")
                        
                        # اختبار جلب جميع الكورسات
                        print("\n🔍 جلب جميع الكورسات...")
                        all_courses = firebase_manager.database.child('courses').get()
                        if all_courses:
                            print(f"📊 إجمالي الكورسات: {len(all_courses)}")
                            instructor_courses = []
                            for course_id, course_data in all_courses.items():
                                if course_data.get('instructor_id') == instructor_id:
                                    instructor_courses.append(course_data)
                            print(f"📊 كورسات هذا المدرس: {len(instructor_courses)}")
                        else:
                            print("❌ لا توجد كورسات في قاعدة البيانات")
                
                except Exception as e:
                    print(f"❌ خطأ في الاستعلام المباشر: {e}")
            else:
                print("❌ قاعدة البيانات غير متاحة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def fix_firebase_manager():
    """إصلاح Firebase Manager"""

    print("🔧 إصلاح Firebase Manager...")

    try:
        from utils.firebase_utils import firebase_manager
        from config import Config

        # فرض إعادة التهيئة
        firebase_manager._initialized = False
        firebase_manager.database = None
        firebase_manager.app = None

        # إعداد الإعدادات
        config = Config()
        config_dict = {
            'FIREBASE_CONFIG': config.FIREBASE_CONFIG,
            'FIREBASE_DATABASE_URL': config.FIREBASE_DATABASE_URL
        }

        # إعادة التهيئة
        firebase_manager.initialize(config_dict)

        print(f"✅ تم إصلاح Firebase Manager")
        print(f"   مهيأ: {firebase_manager._initialized}")

        return firebase_manager._initialized

    except Exception as e:
        print(f"❌ فشل في إصلاح Firebase Manager: {e}")
        return False

if __name__ == "__main__":
    print("🚀 بدء إصلاح مشكلة Firebase...")
    
    # اختبار الحالة الحالية
    test_firebase_initialization()
    
    print("\n" + "="*50)
    
    # إصلاح Firebase Manager
    if fix_firebase_manager():
        print("\n🔍 اختبار بعد الإصلاح...")
        test_firebase_initialization()
    
    print("\n✅ انتهى الإصلاح")
