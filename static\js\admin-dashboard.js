/**
 * إدارة لوحة تحكم الأدمن
 * Admin Dashboard Management
 */

class AdminDashboard {
    constructor() {
        this.init();
    }

    init() {
        this.loadStatistics();
        this.loadSpecializations();
        this.setupEventListeners();
    }

    /**
     * تحميل الإحصائيات
     */
    async loadStatistics() {
        try {
            const response = await fetch('/api/admin/statistics');
            if (response.ok) {
                const stats = await response.json();
                this.updateStatistics(stats);
            }
        } catch (error) {
            console.error('خطأ في تحميل الإحصائيات:', error);
        }
    }

    /**
     * تحديث عرض الإحصائيات
     */
    updateStatistics(stats) {
        const elements = {
            'total-users': stats.total_users || 0,
            'total-instructors': stats.total_instructors || 0,
            'total-students': stats.total_students || 0,
            'total-courses': stats.total_courses || 0
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                this.animateNumber(element, value);
            }
        });
    }

    /**
     * تحريك الأرقام
     */
    animateNumber(element, targetValue) {
        const startValue = 0;
        const duration = 2000;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const currentValue = Math.floor(startValue + (targetValue - startValue) * progress);
            element.textContent = currentValue.toLocaleString('ar-EG');

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    /**
     * تحميل التخصصات
     */
    async loadSpecializations() {
        try {
            const response = await fetch('/api/specializations');
            if (response.ok) {
                const specializations = await response.json();
                this.displaySpecializations(specializations);
            }
        } catch (error) {
            console.error('خطأ في تحميل التخصصات:', error);
            this.showError('فشل في تحميل التخصصات');
        }
    }

    /**
     * عرض التخصصات
     */
    displaySpecializations(specializations) {
        const container = document.getElementById('specializations-container');
        
        if (!specializations || specializations.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد تخصصات متاحة</h5>
                    <p class="text-muted">ابدأ بإضافة تخصص جديد</p>
                </div>
            `;
            return;
        }

        const specializationsHTML = specializations.map(spec => `
            <div class="col-md-4 mb-3">
                <div class="specialization-card">
                    <div class="text-center p-3">
                        <div class="specialization-icon">
                            <i class="${spec.icon || 'fas fa-stethoscope'}" style="color: ${spec.color || '#667eea'}"></i>
                        </div>
                        <h5 class="mb-2">${spec.name}</h5>
                        <p class="text-muted small">${spec.name_en || ''}</p>
                        
                        ${spec.description ? `<p class="text-muted small">${spec.description}</p>` : ''}
                        
                        <div class="mb-3">
                            ${(spec.stages || []).map(stage => 
                                `<span class="badge bg-primary me-1">المرحلة ${stage}</span>`
                            ).join('')}
                        </div>
                        
                        <div class="d-flex justify-content-center gap-2">
                            <button class="btn btn-sm btn-outline-primary" onclick="adminDashboard.editSpecialization('${spec.id}')">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="adminDashboard.deleteSpecialization('${spec.id}')">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = `<div class="row">${specializationsHTML}</div>`;
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // معاينة الأيقونة
        const iconSelect = document.getElementById('spec_icon');
        const iconPreview = document.getElementById('icon-preview');
        
        if (iconSelect && iconPreview) {
            iconSelect.addEventListener('change', (e) => {
                const iconClass = e.target.value;
                if (iconClass) {
                    iconPreview.className = iconClass + ' fa-3x text-primary';
                } else {
                    iconPreview.className = 'fas fa-question fa-3x text-muted';
                }
            });
        }

        // حفظ التخصص
        const saveBtn = document.getElementById('saveSpecializationBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveSpecialization());
        }
    }

    /**
     * حفظ التخصص
     */
    async saveSpecialization() {
        const form = document.getElementById('addSpecializationForm');
        const formData = new FormData(form);
        
        // جمع المراحل المحددة
        const stages = [];
        form.querySelectorAll('input[name="stages"]:checked').forEach(checkbox => {
            stages.push(parseInt(checkbox.value));
        });

        const specializationData = {
            name: formData.get('name'),
            name_en: formData.get('name_en'),
            description: formData.get('description'),
            icon: formData.get('icon'),
            stages: stages,
            active: true
        };

        try {
            const response = await fetch('/api/admin/specializations', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(specializationData)
            });

            if (response.ok) {
                const result = await response.json();
                this.showSuccess('تم حفظ التخصص بنجاح');
                
                // إغلاق النافذة المنبثقة
                const modal = bootstrap.Modal.getInstance(document.getElementById('addSpecializationModal'));
                modal.hide();
                
                // إعادة تحميل التخصصات
                this.loadSpecializations();
                
                // مسح النموذج
                form.reset();
                document.getElementById('icon-preview').className = 'fas fa-question fa-3x text-muted';
            } else {
                const error = await response.json();
                this.showError(error.message || 'فشل في حفظ التخصص');
            }
        } catch (error) {
            console.error('خطأ في حفظ التخصص:', error);
            this.showError('حدث خطأ أثناء حفظ التخصص');
        }
    }

    /**
     * تعديل تخصص
     */
    async editSpecialization(specializationId) {
        try {
            const response = await fetch(`/api/specializations/${specializationId}`);
            if (response.ok) {
                const specialization = await response.json();
                this.populateEditForm(specialization);
                
                // فتح النافذة المنبثقة
                const modal = new bootstrap.Modal(document.getElementById('addSpecializationModal'));
                modal.show();
            }
        } catch (error) {
            console.error('خطأ في تحميل بيانات التخصص:', error);
            this.showError('فشل في تحميل بيانات التخصص');
        }
    }

    /**
     * ملء نموذج التعديل
     */
    populateEditForm(specialization) {
        document.getElementById('spec_name').value = specialization.name || '';
        document.getElementById('spec_name_en').value = specialization.name_en || '';
        document.getElementById('spec_description').value = specialization.description || '';
        document.getElementById('spec_icon').value = specialization.icon || '';
        
        // تحديث معاينة الأيقونة
        const iconPreview = document.getElementById('icon-preview');
        if (specialization.icon) {
            iconPreview.className = specialization.icon + ' fa-3x text-primary';
        }
        
        // تحديد المراحل
        ['stage2', 'stage3', 'stage4'].forEach(stageId => {
            const checkbox = document.getElementById(stageId);
            const stageNumber = parseInt(stageId.replace('stage', ''));
            checkbox.checked = specialization.stages && specialization.stages.includes(stageNumber);
        });
    }

    /**
     * حذف تخصص
     */
    async deleteSpecialization(specializationId) {
        if (!confirm('هل أنت متأكد من حذف هذا التخصص؟\nسيتم حذف جميع البيانات المرتبطة به.')) {
            return;
        }

        try {
            const response = await fetch(`/api/admin/specializations/${specializationId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                this.showSuccess('تم حذف التخصص بنجاح');
                this.loadSpecializations();
            } else {
                const error = await response.json();
                this.showError(error.message || 'فشل في حذف التخصص');
            }
        } catch (error) {
            console.error('خطأ في حذف التخصص:', error);
            this.showError('حدث خطأ أثناء حذف التخصص');
        }
    }

    /**
     * عرض رسالة نجاح
     */
    showSuccess(message) {
        this.showAlert(message, 'success');
    }

    /**
     * عرض رسالة خطأ
     */
    showError(message) {
        this.showAlert(message, 'danger');
    }

    /**
     * عرض تنبيه
     */
    showAlert(message, type) {
        const alertHTML = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // إضافة التنبيه في أعلى الصفحة
        const container = document.querySelector('.container-fluid');
        container.insertAdjacentHTML('afterbegin', alertHTML);
        
        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}

// تهيئة لوحة التحكم عند تحميل الصفحة
let adminDashboard;
document.addEventListener('DOMContentLoaded', () => {
    adminDashboard = new AdminDashboard();
});
