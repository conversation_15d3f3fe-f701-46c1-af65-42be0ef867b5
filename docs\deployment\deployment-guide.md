# 🚀 دليل النشر والإطلاق
## Deployment and Launch Guide

دليل شامل لنشر منصة الكورسات التعليمية في بيئة الإنتاج.

---

## 🎯 نظرة عامة على النشر

### خيارات النشر المتاحة
1. **الخادم المخصص (VPS/Dedicated Server)**
2. **الحوسبة السحابية (AWS, Google Cloud, Azure)**
3. **منصات النشر (Heroku, DigitalOcean App Platform)**
4. **الحاويات (Docker + Kubernetes)**

---

## 🖥️ النشر على خادم مخصص (VPS)

### 1. متطلبات الخادم
```bash
# المواصفات المُوصى بها
CPU: 2 cores minimum, 4 cores recommended
RAM: 4GB minimum, 8GB recommended
Storage: 50GB SSD minimum
OS: Ubuntu 20.04 LTS or CentOS 8
Network: 100Mbps connection
```

### 2. إ<PERSON><PERSON><PERSON> الخادم
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت Python 3.8+
sudo apt install python3.8 python3.8-venv python3-pip -y

# تثبيت Nginx
sudo apt install nginx -y

# تثبيت Supervisor
sudo apt install supervisor -y

# تثبيت Redis (للتخزين المؤقت)
sudo apt install redis-server -y

# تثبيت Git
sudo apt install git -y
```

### 3. إعداد المستخدم والمجلدات
```bash
# إنشاء مستخدم للتطبيق
sudo adduser courseplatform
sudo usermod -aG sudo courseplatform

# التبديل للمستخدم الجديد
su - courseplatform

# إنشاء مجلد التطبيق
mkdir -p /home/<USER>/app
cd /home/<USER>/app
```

### 4. تحميل وإعداد التطبيق
```bash
# استنساخ المشروع
git clone <repository-url> .

# إنشاء البيئة الافتراضية
python3 -m venv venv
source venv/bin/activate

# تثبيت المتطلبات
pip install -r requirements.txt

# إنشاء ملف .env للإنتاج
cp .env.example .env.production
```

### 5. إعداد متغيرات البيئة للإنتاج
```env
# .env.production
FLASK_ENV=production
FLASK_DEBUG=False
FLASK_SECRET_KEY=your_very_secure_production_secret_key
FLASK_PORT=5000

# Firebase Production Settings
FIREBASE_PROJECT_ID=your-production-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_DATABASE_URL=https://your-production-project-default-rtdb.firebaseio.com/

# Production URLs
PLATFORM_URL=https://yourdomain.com
PLATFORM_NAME=منصة الكورسات التعليمية

# Security Settings
JWT_SECRET_KEY=your_production_jwt_secret
JWT_EXPIRATION_HOURS=24

# Cache Settings (Redis)
CACHE_TYPE=redis
CACHE_REDIS_URL=redis://localhost:6379/0
CACHE_DEFAULT_TIMEOUT=300
```

---

## 🌐 إعداد Nginx

### 1. إنشاء ملف إعداد Nginx
```bash
sudo nano /etc/nginx/sites-available/courseplatform
```

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;

    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Static Files
    location /static {
        alias /home/<USER>/app/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Main Application
    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # File Upload Limits
    client_max_body_size 100M;
}
```

### 2. تفعيل الموقع
```bash
# تفعيل الموقع
sudo ln -s /etc/nginx/sites-available/courseplatform /etc/nginx/sites-enabled/

# فحص الإعدادات
sudo nginx -t

# إعادة تشغيل Nginx
sudo systemctl restart nginx
```

---

## 🔒 إعداد SSL Certificate

### 1. تثبيت Certbot
```bash
sudo apt install certbot python3-certbot-nginx -y
```

### 2. الحصول على شهادة SSL
```bash
# الحصول على شهادة مجانية من Let's Encrypt
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# إعداد التجديد التلقائي
sudo crontab -e
# إضافة السطر التالي:
0 12 * * * /usr/bin/certbot renew --quiet
```

---

## 🔄 إعداد Supervisor

### 1. إنشاء ملف إعداد التطبيق الرئيسي
```bash
sudo nano /etc/supervisor/conf.d/courseplatform.conf
```

```ini
[program:courseplatform]
command=/home/<USER>/app/venv/bin/python app.py
directory=/home/<USER>/app
user=courseplatform
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/courseplatform/app.log
environment=FLASK_ENV=production
```

### 2. إنشاء ملف إعداد البوت
```bash
sudo nano /etc/supervisor/conf.d/courseplatform-bot.conf
```

```ini
[program:courseplatform-bot]
command=/home/<USER>/app/venv/bin/python bot/main.py
directory=/home/<USER>/app
user=courseplatform
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/courseplatform/bot.log
environment=FLASK_ENV=production
```

### 3. إنشاء مجلد السجلات وتفعيل الخدمات
```bash
# إنشاء مجلد السجلات
sudo mkdir -p /var/log/courseplatform
sudo chown courseplatform:courseplatform /var/log/courseplatform

# تحديث إعدادات Supervisor
sudo supervisorctl reread
sudo supervisorctl update

# بدء الخدمات
sudo supervisorctl start courseplatform
sudo supervisorctl start courseplatform-bot

# فحص الحالة
sudo supervisorctl status
```

---

## 🐳 النشر باستخدام Docker

### 1. إنشاء Dockerfile
```dockerfile
FROM python:3.8-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# Expose port
EXPOSE 5000

# Run application
CMD ["python", "app.py"]
```

### 2. إنشاء docker-compose.yml
```yaml
version: '3.8'

services:
  web:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - CACHE_TYPE=redis
      - CACHE_REDIS_URL=redis://redis:6379/0
    depends_on:
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  bot:
    build: .
    command: python bot/main.py
    environment:
      - FLASK_ENV=production
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl/certs
    depends_on:
      - web
    restart: unless-stopped
```

### 3. بناء وتشغيل الحاويات
```bash
# بناء الصور
docker-compose build

# تشغيل الخدمات
docker-compose up -d

# فحص الحالة
docker-compose ps

# عرض السجلات
docker-compose logs -f
```

---

## ☁️ النشر على AWS

### 1. إعداد EC2 Instance
```bash
# اختيار AMI: Ubuntu 20.04 LTS
# Instance Type: t3.medium (2 vCPU, 4GB RAM)
# Security Groups: HTTP (80), HTTPS (443), SSH (22)
# Storage: 20GB GP2 SSD
```

### 2. إعداد RDS للبيانات (اختياري)
```bash
# إنشاء RDS Instance للنسخ الاحتياطية
# Engine: PostgreSQL أو MySQL
# Instance Class: db.t3.micro
# Storage: 20GB
```

### 3. إعداد S3 للملفات الثابتة
```bash
# إنشاء S3 Bucket
aws s3 mb s3://courseplatform-static

# إعداد CloudFront للتوزيع
aws cloudfront create-distribution
```

---

## 🔧 إعدادات الأمان للإنتاج

### 1. Firewall Configuration
```bash
# تفعيل UFW
sudo ufw enable

# السماح بالمنافذ المطلوبة فقط
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'

# منع المنافذ غير المطلوبة
sudo ufw deny 5000
```

### 2. إعدادات أمان إضافية
```bash
# تعطيل تسجيل الدخول بـ root
sudo sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config

# تفعيل Fail2Ban
sudo apt install fail2ban -y
sudo systemctl enable fail2ban
```

### 3. مراقبة النظام
```bash
# تثبيت htop للمراقبة
sudo apt install htop -y

# إعداد مراقبة السجلات
sudo apt install logwatch -y
```

---

## 📊 مراقبة الأداء

### 1. إعداد مراقبة التطبيق
```python
# إضافة إلى app.py
from flask import request
import time
import logging

@app.before_request
def before_request():
    request.start_time = time.time()

@app.after_request
def after_request(response):
    duration = time.time() - request.start_time
    logging.info(f"Request to {request.path} took {duration:.3f}s")
    return response
```

### 2. إعداد تنبيهات
```bash
# إنشاء سكريبت مراقبة
cat > /home/<USER>/monitor.sh << 'EOF'
#!/bin/bash
# فحص حالة التطبيق
if ! curl -f http://localhost:5000/api/health > /dev/null 2>&1; then
    echo "Application is down!" | mail -s "Alert: App Down" <EMAIL>
    sudo supervisorctl restart courseplatform
fi
EOF

# إضافة إلى crontab
echo "*/5 * * * * /home/<USER>/monitor.sh" | crontab -
```

---

## 🔄 النسخ الاحتياطية

### 1. نسخ احتياطية للبيانات
```bash
# سكريبت النسخ الاحتياطي
cat > /home/<USER>/backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/home/<USER>/backups"

# إنشاء مجلد النسخ الاحتياطية
mkdir -p $BACKUP_DIR

# نسخ احتياطي للتطبيق
tar -czf $BACKUP_DIR/app_$DATE.tar.gz /home/<USER>/app

# نسخ احتياطي لقاعدة البيانات (Firebase)
python3 /home/<USER>/app/backup_firebase.py

# حذف النسخ القديمة (أكثر من 30 يوم)
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
EOF

# جدولة النسخ الاحتياطية
echo "0 2 * * * /home/<USER>/backup.sh" | crontab -
```

---

## 🧪 اختبار النشر

### 1. اختبارات ما بعد النشر
```bash
# فحص حالة الخدمات
sudo systemctl status nginx
sudo supervisorctl status

# فحص الاتصال
curl -I https://yourdomain.com

# فحص API
curl https://yourdomain.com/api/health

# فحص SSL
openssl s_client -connect yourdomain.com:443 -servername yourdomain.com
```

### 2. اختبارات الأداء
```bash
# اختبار الحمولة باستخدام ab
ab -n 1000 -c 10 https://yourdomain.com/

# اختبار الأداء
curl -w "@curl-format.txt" -o /dev/null -s https://yourdomain.com/
```

---

## 📋 قائمة فحص النشر

### قبل النشر
- [ ] اختبار التطبيق محلياً
- [ ] إعداد متغيرات البيئة للإنتاج
- [ ] فحص الأمان والثغرات
- [ ] إعداد النسخ الاحتياطية
- [ ] تحضير خطة الاستعادة

### أثناء النشر
- [ ] إعداد الخادم والبرمجيات
- [ ] نشر التطبيق
- [ ] إعداد قاعدة البيانات
- [ ] إعداد الخدمات المساعدة
- [ ] فحص الاتصالات

### بعد النشر
- [ ] اختبار جميع الوظائف
- [ ] فحص الأداء
- [ ] إعداد المراقبة
- [ ] تدريب المستخدمين
- [ ] توثيق العمليات

---

**آخر تحديث:** 2025-07-03  
**الإصدار:** 1.0.0  
**حالة الدليل:** مكتمل ✅
