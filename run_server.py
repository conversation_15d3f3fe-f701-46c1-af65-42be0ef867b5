"""
تشغيل خادم التطبيق
Run Application Server
"""

from app import create_app

if __name__ == '__main__':
    app = create_app('development')
    print("🚀 بدء تشغيل خادم منصة الكورسات التعليمية...")
    print("🌐 الخادم متاح على: http://localhost:5000")
    print("📝 صفحة تسجيل الدخول: http://localhost:5000/login")
    print("\n👤 بيانات المستخدمين التجريبيين:")
    print("   📧 <EMAIL> / admin123 (مدير النظام)")
    print("   📧 <EMAIL> / instructor123 (مدرس)")
    print("   📧 <EMAIL> / student123 (طالب)")
    print("\n⏹️ اضغط Ctrl+C لإيقاف الخادم")
    print("="*60)
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        use_reloader=False
    )
