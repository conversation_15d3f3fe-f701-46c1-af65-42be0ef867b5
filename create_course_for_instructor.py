#!/usr/bin/env python3
"""
إنشاء كورس تجريبي للمدرس الموجود
"""

import requests
import json
import sys

def create_course_for_instructor():
    """إنشاء كورس تجريبي للمدرس"""
    
    base_url = "http://127.0.0.1:5000"
    
    print("🔍 إنشاء كورس تجريبي للمدرس <EMAIL>...")
    
    # بيانات تسجيل الدخول
    login_data = {
        'email': '<EMAIL>',
        'password': 'instructor123'
    }
    
    # إنشاء session
    session = requests.Session()
    
    try:
        # 1. تسجيل الدخول
        print("🔐 تسجيل الدخول...")
        login_response = session.post(
            f"{base_url}/api/auth/login",
            json=login_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if login_response.status_code != 200:
            print("❌ فشل في تسجيل الدخول")
            return False
        
        login_result = login_response.json()
        token = login_result.get('token')
        if token:
            session.headers.update({'Authorization': f'Bearer {token}'})
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # 2. إنشاء كورسات تجريبية
        courses_to_create = [
            {
                'title': 'كورس التحليل الطبي - المرحلة الثانية',
                'description': 'كورس شامل في التحليل الطبي للمرحلة الثانية يغطي جميع الأساسيات والتقنيات المتقدمة',
                'stage': 2,
                'status': 'draft',
                'is_general': False,
                'specialization_id': 'medical_analysis'
            },
            {
                'title': 'كورس التحليل المتقدم - المرحلة الثالثة',
                'description': 'كورس متقدم في التحليل الطبي للمرحلة الثالثة مع التطبيقات العملية',
                'stage': 3,
                'status': 'published',
                'is_general': False,
                'specialization_id': 'medical_analysis'
            },
            {
                'title': 'كورس عام - أساسيات الطب',
                'description': 'كورس عام في أساسيات الطب لجميع التخصصات والمراحل',
                'stage': 2,
                'status': 'published',
                'is_general': True,
                'specialization_id': None
            }
        ]
        
        print(f"\n📚 إنشاء {len(courses_to_create)} كورسات تجريبية...")
        
        created_courses = []
        
        for i, course_data in enumerate(courses_to_create, 1):
            print(f"\n📝 إنشاء الكورس {i}: {course_data['title']}")
            
            # إرسال طلب إنشاء الكورس
            create_response = session.post(
                f"{base_url}/api/instructor/courses",
                json=course_data,
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"📊 استجابة إنشاء الكورس: {create_response.status_code}")
            
            if create_response.status_code == 201:
                create_result = create_response.json()
                if create_result.get('success'):
                    course_id = create_result.get('course_id')
                    print(f"✅ تم إنشاء الكورس بنجاح (ID: {course_id})")
                    created_courses.append({
                        'id': course_id,
                        'title': course_data['title'],
                        'status': course_data['status']
                    })
                else:
                    print(f"❌ فشل في إنشاء الكورس: {create_result.get('message', 'غير محدد')}")
            else:
                print(f"❌ خطأ في إنشاء الكورس: {create_response.status_code}")
                print(f"   الاستجابة: {create_response.text}")
        
        # 3. التحقق من الكورسات المنشأة
        print(f"\n🔍 التحقق من الكورسات المنشأة...")
        courses_response = session.get(f"{base_url}/api/instructor/courses")
        
        if courses_response.status_code == 200:
            courses_result = courses_response.json()
            if courses_result.get('success'):
                courses = courses_result.get('courses', [])
                print(f"📊 إجمالي عدد الكورسات: {len(courses)}")
                
                if courses:
                    print("\n📚 الكورسات الموجودة:")
                    for i, course in enumerate(courses, 1):
                        title = course.get('title', 'بدون عنوان')
                        status = course.get('status', 'غير محدد')
                        stage = course.get('stage', 'غير محدد')
                        is_general = course.get('is_general', False)
                        
                        status_emoji = "📝" if status == 'draft' else "✅"
                        general_text = " (عام)" if is_general else ""
                        
                        print(f"  {status_emoji} {i}. {title}{general_text}")
                        print(f"     الحالة: {status}")
                        print(f"     المرحلة: {stage}")
                    
                    print(f"\n🎉 تم إنشاء الكورسات بنجاح!")
                    print(f"\n📋 الآن يمكنك:")
                    print(f"1. الذهاب إلى: http://127.0.0.1:5000/login")
                    print(f"2. تسجيل الدخول بـ: <EMAIL> / instructor123")
                    print(f"3. الذهاب إلى: http://127.0.0.1:5000/instructor/courses")
                    print(f"4. يجب أن ترى {len(courses)} كورسات")
                    
                    return True
                else:
                    print("❌ لا توجد كورسات بعد الإنشاء - هناك مشكلة!")
                    return False
            else:
                print("❌ فشل في جلب الكورسات بعد الإنشاء")
                return False
        else:
            print(f"❌ خطأ في جلب الكورسات: {courses_response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في العملية: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = create_course_for_instructor()
    if success:
        print("\n✅ تم إنشاء الكورسات التجريبية بنجاح!")
        sys.exit(0)
    else:
        print("\n❌ فشل في إنشاء الكورسات التجريبية")
        sys.exit(1)
