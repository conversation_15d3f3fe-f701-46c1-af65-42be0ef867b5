/**
 * نظام تتبع تقدم الفيديوهات
 * Video Progress Tracking System
 */

class VideoProgressTracker {
    constructor(videoElement, lessonId, courseId) {
        this.video = videoElement;
        this.lessonId = lessonId;
        this.courseId = courseId;
        this.progressSaveInterval = 10; // حفظ التقدم كل 10 ثوان
        this.lastSavedTime = 0;
        this.progressTimer = null;
        this.isCompleted = false;
        
        this.init();
    }
    
    init() {
        // تحميل التقدم المحفوظ
        this.loadSavedProgress();
        
        // إعداد مستمعي الأحداث
        this.setupEventListeners();
        
        // بدء تتبع التقدم
        this.startProgressTracking();
    }
    
    setupEventListeners() {
        // عند تشغيل الفيديو
        this.video.addEventListener('play', () => {
            this.startProgressTracking();
        });
        
        // عند إيقاف الفيديو
        this.video.addEventListener('pause', () => {
            this.saveProgress();
        });
        
        // عند انتهاء الفيديو
        this.video.addEventListener('ended', () => {
            this.markAsCompleted();
        });
        
        // عند تغيير الوقت يدوياً
        this.video.addEventListener('seeked', () => {
            this.saveProgress();
        });
        
        // عند مغادرة الصفحة
        window.addEventListener('beforeunload', () => {
            this.saveProgress();
        });
    }
    
    async loadSavedProgress() {
        try {
            const response = await fetch(`/api/student/progress/video/${this.lessonId}`);
            const data = await response.json();
            
            if (data.success && data.progress && data.progress.current_time) {
                const savedTime = data.progress.current_time;
                const duration = this.video.duration || data.progress.duration;
                
                // إذا كان الفيديو محفوظ عند نقطة معينة، اسأل المستخدم
                if (savedTime > 30 && savedTime < duration - 30) {
                    this.showResumeDialog(savedTime);
                }
            }
        } catch (error) {
            console.error('خطأ في تحميل التقدم المحفوظ:', error);
        }
    }
    
    showResumeDialog(savedTime) {
        const minutes = Math.floor(savedTime / 60);
        const seconds = Math.floor(savedTime % 60);
        const timeString = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        
        const resumeDialog = `
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <div class="d-flex align-items-center">
                    <i class="fas fa-play-circle me-2"></i>
                    <div class="flex-grow-1">
                        <strong>متابعة المشاهدة</strong><br>
                        <small>آخر نقطة توقف: ${timeString}</small>
                    </div>
                    <div>
                        <button type="button" class="btn btn-primary btn-sm me-2" onclick="videoTracker.resumeFromSaved(${savedTime})">
                            متابعة
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="videoTracker.startFromBeginning()">
                            من البداية
                        </button>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // إضافة الحوار قبل مشغل الفيديو
        $(this.video).before(resumeDialog);
    }
    
    resumeFromSaved(savedTime) {
        this.video.currentTime = savedTime;
        $('.alert').alert('close');
    }
    
    startFromBeginning() {
        this.video.currentTime = 0;
        $('.alert').alert('close');
    }
    
    startProgressTracking() {
        if (this.progressTimer) {
            clearInterval(this.progressTimer);
        }
        
        this.progressTimer = setInterval(() => {
            const currentTime = this.video.currentTime;
            
            // حفظ التقدم كل فترة محددة
            if (currentTime - this.lastSavedTime >= this.progressSaveInterval) {
                this.saveProgress();
                this.lastSavedTime = currentTime;
            }
            
            // تحديث شريط التقدم في الواجهة
            this.updateProgressBar();
            
        }, 1000); // كل ثانية
    }
    
    async saveProgress() {
        if (!this.video.duration || this.video.currentTime === 0) {
            return;
        }
        
        try {
            const progressData = {
                lesson_id: this.lessonId,
                current_time: this.video.currentTime,
                duration: this.video.duration
            };
            
            await fetch('/api/student/progress/video', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(progressData)
            });
            
        } catch (error) {
            console.error('خطأ في حفظ تقدم الفيديو:', error);
        }
    }
    
    async markAsCompleted() {
        if (this.isCompleted) return;
        
        this.isCompleted = true;
        
        try {
            // حفظ التقدم النهائي
            await this.saveProgress();
            
            // تحديث حالة الدرس كمكتمل
            const lessonData = {
                course_id: this.courseId,
                lesson_id: this.lessonId,
                completed: true,
                watch_time: this.video.duration
            };
            
            const response = await fetch('/api/student/progress/lesson', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(lessonData)
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showCompletionMessage();
                this.updateLessonStatus();
            }
            
        } catch (error) {
            console.error('خطأ في تحديث حالة الدرس:', error);
        }
    }
    
    showCompletionMessage() {
        const completionAlert = `
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <div class="d-flex align-items-center">
                    <i class="fas fa-check-circle me-2"></i>
                    <div>
                        <strong>تهانينا!</strong><br>
                        <small>تم إكمال هذا الدرس بنجاح</small>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        $(this.video).after(completionAlert);
        
        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            $('.alert-success').fadeOut();
        }, 5000);
    }
    
    updateProgressBar() {
        if (!this.video.duration) return;
        
        const percentage = (this.video.currentTime / this.video.duration) * 100;
        
        // تحديث شريط التقدم إذا كان موجوداً
        const progressBar = document.querySelector('.lesson-progress-bar');
        if (progressBar) {
            progressBar.style.width = percentage + '%';
            progressBar.setAttribute('aria-valuenow', percentage);
        }
        
        // تحديث النص
        const progressText = document.querySelector('.lesson-progress-text');
        if (progressText) {
            progressText.textContent = Math.round(percentage) + '% مكتمل';
        }
    }
    
    updateLessonStatus() {
        // تحديث حالة الدرس في قائمة الدروس
        const lessonItem = document.querySelector(`[data-lesson-id="${this.lessonId}"]`);
        if (lessonItem) {
            lessonItem.classList.add('completed');
            
            const statusIcon = lessonItem.querySelector('.lesson-status-icon');
            if (statusIcon) {
                statusIcon.innerHTML = '<i class="fas fa-check-circle text-success"></i>';
            }
        }
    }
    
    destroy() {
        if (this.progressTimer) {
            clearInterval(this.progressTimer);
        }
        
        // حفظ التقدم الأخير
        this.saveProgress();
    }
}

// متغير عام لتتبع الفيديو
let videoTracker = null;

// دالة لتهيئة تتبع الفيديو
function initVideoTracker(videoElement, lessonId, courseId) {
    if (videoTracker) {
        videoTracker.destroy();
    }
    
    videoTracker = new VideoProgressTracker(videoElement, lessonId, courseId);
    return videoTracker;
}

// دالة لتحديث تقدم الدرس يدوياً
async function markLessonAsCompleted(courseId, lessonId) {
    try {
        const response = await fetch('/api/student/progress/lesson', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                course_id: courseId,
                lesson_id: lessonId,
                completed: true
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // تحديث الواجهة
            const lessonItem = document.querySelector(`[data-lesson-id="${lessonId}"]`);
            if (lessonItem) {
                lessonItem.classList.add('completed');
                
                const statusIcon = lessonItem.querySelector('.lesson-status-icon');
                if (statusIcon) {
                    statusIcon.innerHTML = '<i class="fas fa-check-circle text-success"></i>';
                }
            }
            
            showAlert('تم تحديث حالة الدرس بنجاح', 'success');
        } else {
            showAlert('فشل في تحديث حالة الدرس: ' + data.message, 'danger');
        }
        
    } catch (error) {
        console.error('خطأ في تحديث حالة الدرس:', error);
        showAlert('حدث خطأ في تحديث حالة الدرس', 'danger');
    }
}

// دالة مساعدة لعرض التنبيهات
function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // إضافة التنبيه في أعلى المحتوى
    $('.container').first().prepend(alertHtml);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}
