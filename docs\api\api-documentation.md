# 🔌 وثائق API
## API Documentation

دليل شامل لواجهات برمجة التطبيقات (APIs) الخاصة بمنصة الكورسات التعليمية.

---

## 🎯 نظرة عامة

### Base URL
```
Development: http://localhost:5000
Production: https://yourdomain.com
```

### Authentication
جميع APIs تتطلب مصادقة باستثناء endpoints العامة. يتم استخدام JWT tokens للمصادقة.

```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

---

## 🔐 Authentication APIs

### 1. تسجيل الدخول
```http
POST /api/auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (Success):**
```json
{
  "success": true,
  "message": "تم تسجيل الدخول بنجاح",
  "data": {
    "user_id": "user123",
    "email": "<EMAIL>",
    "role": "student",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2025-07-04T12:00:00Z"
  }
}
```

### 2. تسجيل الخروج
```http
POST /api/auth/logout
```

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "message": "تم تسجيل الخروج بنجاح"
}
```

### 3. التحقق من صحة Token
```http
GET /api/auth/verify
```

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "user_id": "user123",
    "role": "student",
    "valid": true
  }
}
```

---

## 👥 User Management APIs

### 1. الحصول على معلومات المستخدم
```http
GET /api/users/profile
```

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "user_id": "user123",
    "email": "<EMAIL>",
    "name": "أحمد محمد",
    "role": "student",
    "specialization": "medical_analysis",
    "level": "level_2",
    "created_at": "2025-01-01T00:00:00Z",
    "last_login": "2025-07-03T10:30:00Z"
  }
}
```

### 2. تحديث الملف الشخصي
```http
PUT /api/users/profile
```

**Request Body:**
```json
{
  "name": "أحمد محمد علي",
  "phone": "+201234567890"
}
```

### 3. تغيير كلمة المرور
```http
POST /api/users/change-password
```

**Request Body:**
```json
{
  "current_password": "oldpassword",
  "new_password": "newpassword123"
}
```

---

## 📚 Courses APIs

### 1. الحصول على جميع الكورسات
```http
GET /api/courses
```

**Query Parameters:**
- `specialization`: التخصص (medical_analysis, radiology, anesthesia)
- `level`: المرحلة (level_2, level_3, level_4)
- `instructor_id`: معرف المدرس
- `search`: البحث النصي
- `page`: رقم الصفحة (افتراضي: 1)
- `limit`: عدد النتائج (افتراضي: 10)

**Response:**
```json
{
  "success": true,
  "data": {
    "courses": [
      {
        "course_id": "course123",
        "title": "أساسيات التحليل الطبي",
        "description": "كورس شامل في أساسيات التحليل الطبي",
        "instructor": {
          "id": "instructor123",
          "name": "د. محمد أحمد"
        },
        "specialization": "medical_analysis",
        "level": "level_2",
        "duration": "40 ساعة",
        "students_count": 150,
        "rating": 4.8,
        "thumbnail": "https://example.com/thumb.jpg",
        "created_at": "2025-01-15T00:00:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 5,
      "total_courses": 47,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

### 2. الحصول على تفاصيل كورس
```http
GET /api/courses/{course_id}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "course_id": "course123",
    "title": "أساسيات التحليل الطبي",
    "description": "وصف مفصل للكورس...",
    "instructor": {
      "id": "instructor123",
      "name": "د. محمد أحمد",
      "bio": "خبير في التحليل الطبي..."
    },
    "content": [
      {
        "type": "video",
        "title": "المقدمة",
        "url": "https://youtube.com/watch?v=...",
        "duration": "15:30"
      },
      {
        "type": "file",
        "title": "ملف PDF",
        "url": "https://example.com/file.pdf",
        "size": "2.5 MB"
      }
    ],
    "is_enrolled": true,
    "progress": 65
  }
}
```

### 3. تفعيل كورس بكود
```http
POST /api/courses/activate
```

**Request Body:**
```json
{
  "activation_code": "ABC123XYZ"
}
```

**Response:**
```json
{
  "success": true,
  "message": "تم تفعيل الكورس بنجاح",
  "data": {
    "course_id": "course123",
    "title": "أساسيات التحليل الطبي",
    "activated_at": "2025-07-03T12:00:00Z"
  }
}
```

---

## 🎓 Instructor APIs

### 1. إنشاء كورس جديد (للمدرسين)
```http
POST /api/instructor/courses
```

**Headers:** `Authorization: Bearer <instructor_token>`

**Request Body:**
```json
{
  "title": "كورس جديد",
  "description": "وصف الكورس",
  "specialization": "medical_analysis",
  "level": "level_2",
  "duration": "30 ساعة"
}
```

### 2. إضافة محتوى للكورس
```http
POST /api/instructor/courses/{course_id}/content
```

**Request Body:**
```json
{
  "type": "video",
  "title": "الدرس الأول",
  "url": "https://youtube.com/watch?v=...",
  "description": "وصف الدرس"
}
```

### 3. إنشاء كود تفعيل
```http
POST /api/instructor/courses/{course_id}/activation-codes
```

**Request Body:**
```json
{
  "quantity": 50,
  "expires_at": "2025-12-31T23:59:59Z",
  "max_uses": 1
}
```

### 4. الحصول على إحصائيات الكورس
```http
GET /api/instructor/courses/{course_id}/stats
```

**Response:**
```json
{
  "success": true,
  "data": {
    "total_students": 150,
    "active_students": 120,
    "completion_rate": 78.5,
    "average_progress": 65.2,
    "total_views": 2450,
    "engagement_rate": 85.3
  }
}
```

---

## 👑 Admin APIs

### 1. إحصائيات النظام
```http
GET /api/admin/stats
```

**Headers:** `Authorization: Bearer <admin_token>`

**Response:**
```json
{
  "success": true,
  "data": {
    "users": {
      "total": 1250,
      "students": 1100,
      "instructors": 25,
      "admins": 1,
      "active_today": 340
    },
    "courses": {
      "total": 47,
      "active": 42,
      "inactive": 5,
      "total_enrollments": 5680
    },
    "system": {
      "uptime": "15 days, 6 hours",
      "response_time": "0.002s",
      "error_rate": "0.1%"
    }
  }
}
```

### 2. إدارة المستخدمين
```http
GET /api/admin/users
POST /api/admin/users
PUT /api/admin/users/{user_id}
DELETE /api/admin/users/{user_id}
```

### 3. إدارة التخصصات
```http
GET /api/admin/specializations
POST /api/admin/specializations
PUT /api/admin/specializations/{spec_id}
```

---

## 🔍 Search & Filter APIs

### 1. البحث العام
```http
GET /api/search
```

**Query Parameters:**
- `q`: نص البحث
- `type`: نوع البحث (courses, instructors, all)
- `filters`: فلاتر إضافية

**Response:**
```json
{
  "success": true,
  "data": {
    "courses": [...],
    "instructors": [...],
    "total_results": 25,
    "search_time": "0.045s"
  }
}
```

### 2. الفلترة المتقدمة
```http
POST /api/search/advanced
```

**Request Body:**
```json
{
  "filters": {
    "specialization": ["medical_analysis", "radiology"],
    "level": ["level_2", "level_3"],
    "rating": {"min": 4.0, "max": 5.0},
    "duration": {"min": 10, "max": 50}
  },
  "sort": {
    "field": "rating",
    "order": "desc"
  }
}
```

---

## 📊 Analytics APIs

### 1. تحليلات المستخدم
```http
GET /api/analytics/user
```

**Response:**
```json
{
  "success": true,
  "data": {
    "enrolled_courses": 5,
    "completed_courses": 3,
    "total_watch_time": "45 ساعة",
    "average_progress": 72.5,
    "achievements": [
      {
        "title": "أول كورس مكتمل",
        "earned_at": "2025-02-15T00:00:00Z"
      }
    ]
  }
}
```

### 2. تحليلات النظام (للأدمن)
```http
GET /api/analytics/system
```

---

## 🚨 Error Handling

### رموز الأخطاء الشائعة

| Code | Message | Description |
|------|---------|-------------|
| 400 | Bad Request | طلب غير صحيح |
| 401 | Unauthorized | غير مصرح |
| 403 | Forbidden | ممنوع |
| 404 | Not Found | غير موجود |
| 422 | Validation Error | خطأ في التحقق |
| 500 | Internal Server Error | خطأ في الخادم |

### تنسيق الأخطاء
```json
{
  "success": false,
  "error": {
    "code": 400,
    "message": "البريد الإلكتروني مطلوب",
    "details": {
      "field": "email",
      "type": "required"
    }
  }
}
```

---

## 📝 Rate Limiting

### الحدود المطبقة
- **عام**: 100 طلب/دقيقة لكل IP
- **تسجيل الدخول**: 5 محاولات/دقيقة
- **البحث**: 30 طلب/دقيقة
- **رفع الملفات**: 10 ملف/ساعة

### Headers الاستجابة
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1625097600
```

---

## 🧪 Testing APIs

### استخدام cURL
```bash
# تسجيل الدخول
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'

# الحصول على الكورسات
curl -X GET http://localhost:5000/api/courses \
  -H "Authorization: Bearer <token>"
```

### استخدام Postman
1. استورد collection من `docs/api/postman_collection.json`
2. اعدل متغيرات البيئة
3. شغل الاختبارات

---

## 📋 Changelog

### v1.0.0 (2025-07-03)
- إطلاق أول إصدار من API
- جميع endpoints الأساسية
- نظام المصادقة والتخويل
- وثائق شاملة

---

**آخر تحديث:** 2025-07-03  
**الإصدار:** 1.0.0  
**حالة الوثائق:** مكتمل ✅
