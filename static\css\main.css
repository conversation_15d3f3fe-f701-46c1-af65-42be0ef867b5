/* ===== الخطوط والإعدادات الأساسية ===== */
* {
    font-family: 'Cairo', sans-serif;
}

body {
    padding-top: 76px; /* للتعويض عن الـ navbar الثابت */
    line-height: 1.6;
}

/* ===== الألوان الأساسية ===== */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    
    /* تدرجات مخصصة */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --gradient-info: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    
    /* ظلال */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* ===== Navigation ===== */
.navbar {
    box-shadow: var(--shadow);
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-size: 1.5rem;
}

/* ===== Hero Section ===== */
.hero-section {
    background: var(--light-color);
    color: rgb(0, 0, 0);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-cta .btn {
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
}

.hero-cta .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Hero Visual */
.hero-visual {
    position: relative;
}

.hero-icon {
    font-size: 15rem;
    opacity: 0.1;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.floating-cards {
    position: relative;
    z-index: 2;
}

.floating-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 20px;
    margin: 10px;
    display: inline-block;
    animation: float 6s ease-in-out infinite;
    box-shadow: var(--shadow);
}

.floating-card i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

.floating-card.card-1 {
    animation-delay: 0s;
}

.floating-card.card-2 {
    animation-delay: 2s;
}

.floating-card.card-3 {
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* ===== Stats ===== */
.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    display: block;
}

.stat-label {
    font-size: 1rem;
    margin: 0;
}

/* ===== Feature Cards ===== */
.feature-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.1);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.feature-icon i {
    transition: all 0.3s ease;
}

.feature-card:hover .feature-icon i {
    transform: scale(1.1);
}

/* ===== Specialization Cards ===== */
.specialization-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.1);
}

.specialization-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.specialization-icon i {
    transition: all 0.3s ease;
}

.specialization-card:hover .specialization-icon i {
    transform: scale(1.1);
}

.specialization-stages .badge {
    font-size: 0.8rem;
    padding: 8px 12px;
}

/* ===== Buttons ===== */
.btn {
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
}

.btn-outline-primary:hover {
    background: var(--gradient-primary);
    border-color: transparent;
}

/* ===== Sections ===== */
.section-title {
    position: relative;
    margin-bottom: 3rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

/* ===== Footer ===== */
footer {
    margin-top: auto;
}

.social-links a {
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.social-links a:hover {
    transform: translateY(-2px);
    color: var(--primary-color) !important;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .hero-cta .btn {
        display: block;
        margin-bottom: 10px;
        width: 100%;
    }
    
    .floating-card {
        display: block;
        margin: 10px 0;
    }
    
    .stat-number {
        font-size: 2rem;
    }
}

/* ===== Animations ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* ===== Utilities ===== */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: var(--gradient-primary);
}

.bg-gradient-success {
    background: var(--gradient-success);
}

.bg-gradient-info {
    background: var(--gradient-info);
}
