{% extends "base.html" %}

{% block title %}إدارة أكواد التفعيل - لوحة الأدمن - {{ platform_name }}{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .code-card {
        border: 1px solid #e3e6f0;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        background: white;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
    }
    
    .code-card:hover {
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }
    
    .code-display {
        font-family: 'Courier New', monospace;
        font-size: 1.1rem;
        font-weight: bold;
        color: #5a5c69;
        background: #f8f9fc;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        border: 2px dashed #d1d3e2;
        text-align: center;
        margin: 0.5rem 0;
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .status-active {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .status-expired {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .btn-copy {
        background: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
        border: none;
        color: white;
        padding: 0.4rem 0.8rem;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.875rem;
    }
    
    .btn-copy:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 10px rgba(0,0,0,0.2);
    }
    
    .page-header {
        background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
    }
    
    .filter-section {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }
    
    .instructor-info {
        background: #f8f9fc;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        border-left: 4px solid #5e72e4;
        margin-bottom: 0.5rem;
    }
    
    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .table th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        font-weight: 500;
    }
    
    .table td {
        vertical-align: middle;
        border-color: #e3e6f0;
    }
    
    .search-box {
        position: relative;
    }
    
    .search-box .form-control {
        padding-right: 2.5rem;
    }
    
    .search-box .search-icon {
        position: absolute;
        right: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-0">
                    <i class="fas fa-key me-3"></i>
                    إدارة أكواد التفعيل - لوحة الأدمن
                </h1>
                <p class="mb-0 mt-2 opacity-75">مراقبة وإدارة جميع أكواد التفعيل في المنصة</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-light btn-lg" data-bs-toggle="modal" data-bs-target="#createCodeModal">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء كود جديد
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- الإحصائيات العامة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ stats.total_codes or 0 }}</div>
                <div>إجمالي الأكواد</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ stats.active_codes or 0 }}</div>
                <div>الأكواد النشطة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ stats.used_codes or 0 }}</div>
                <div>الأكواد المستخدمة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ stats.total_uses or 0 }}</div>
                <div>إجمالي الاستخدامات</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center bg-warning">
                <div class="stats-number text-white">{{ stats.expiring_soon_codes or 0 }}</div>
                <div class="text-white">تنتهي قريباً</div>
                <small class="text-white-50">خلال 3 أيام</small>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="filter-section">
        <div class="row">
            <div class="col-md-4">
                <div class="search-box">
                    <input type="text" class="form-control" id="searchInput" placeholder="البحث في الأكواد...">
                    <i class="fas fa-search search-icon"></i>
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="statusFilter">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                    <option value="expired">منتهي الصلاحية</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="courseFilter">
                    <option value="">جميع الكورسات</option>
                    {% for course in courses %}
                        <option value="{{ course.id }}">{{ course.title }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <button class="btn btn-outline-primary w-100" onclick="resetFilters()">
                    <i class="fas fa-undo me-1"></i>
                    إعادة تعيين
                </button>
            </div>
        </div>

        <!-- أزرار إدارة انتهاء الصلاحية -->
        <div class="row mt-3">
            <div class="col-md-3">
                <button class="btn btn-warning w-100" onclick="showExpiringCodes()">
                    <i class="fas fa-clock me-1"></i>
                    الأكواد المنتهية قريباً
                </button>
            </div>
            <div class="col-md-3">
                <button class="btn btn-info w-100" onclick="showArchivedCodes()">
                    <i class="fas fa-archive me-1"></i>
                    الأكواد المؤرشفة
                </button>
            </div>
            <div class="col-md-3">
                <button class="btn btn-danger w-100" onclick="cleanupExpiredCodes()">
                    <i class="fas fa-trash-alt me-1"></i>
                    تنظيف الأكواد المنتهية
                </button>
            </div>
            <div class="col-md-3">
                <button class="btn btn-secondary w-100" onclick="showSchedulerStatus()">
                    <i class="fas fa-cogs me-1"></i>
                    حالة المجدول
                </button>
            </div>
        </div>
    </div>

    <!-- جدول أكواد التفعيل -->
    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-table me-2"></i>
                جميع أكواد التفعيل
            </h5>
        </div>
        <div class="card-body p-0">
            {% if activation_codes %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="codesTable">
                        <thead>
                            <tr>
                                <th>الكود</th>
                                <th>الكورس</th>
                                <th>المدرس</th>
                                <th>الحالة</th>
                                <th>الاستخدامات</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for code in activation_codes %}
                            <tr data-code-id="{{ code.id }}" data-course-id="{{ code.course_id }}" data-status="{% if code.expired %}expired{% elif code.active %}active{% else %}inactive{% endif %}">
                                <td>
                                    <div class="code-display">{{ code.code }}</div>
                                    <button class="btn-copy" onclick="copyCode('{{ code.code }}')">
                                        <i class="fas fa-copy me-1"></i>
                                        نسخ
                                    </button>
                                </td>
                                <td>
                                    <strong>{{ code.course_title or 'كورس غير محدد' }}</strong>
                                </td>
                                <td>
                                    <div class="instructor-info">
                                        <small class="text-muted">{{ code.instructor_name or 'غير محدد' }}</small>
                                    </div>
                                </td>
                                <td>
                                    {% if code.expired %}
                                        <span class="status-badge status-expired">منتهي الصلاحية</span>
                                    {% elif code.active %}
                                        <span class="status-badge status-active">نشط</span>
                                    {% else %}
                                        <span class="status-badge status-inactive">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ code.current_uses or 0 }}</strong> / {{ code.max_uses or 1 }}
                                    {% if code.current_uses and code.current_uses > 0 %}
                                        <br><small class="text-success">تم الاستخدام</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <small>{{ code.created_at[:10] if code.created_at else 'غير محدد' }}</small>
                                    {% if code.expires_at %}
                                        <br><small class="text-warning">ينتهي: {{ code.expires_at[:10] }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group-vertical btn-group-sm">
                                        {% if code.active and not code.expired %}
                                            <button class="btn btn-warning btn-sm" onclick="deactivateCode('{{ code.id }}')">
                                                <i class="fas fa-pause"></i>
                                                تعطيل
                                            </button>
                                        {% endif %}
                                        {% if code.current_uses == 0 %}
                                            <button class="btn btn-danger btn-sm" onclick="deleteCode('{{ code.id }}')">
                                                <i class="fas fa-trash"></i>
                                                حذف
                                            </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-key fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد أكواد تفعيل</h5>
                    <p class="text-muted">لم يتم إنشاء أي أكواد تفعيل بعد</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal إنشاء كود جديد -->
<div class="modal fade" id="createCodeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء كود تفعيل جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="createCodeForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="courseSelect" class="form-label">اختر الكورس</label>
                        <select class="form-select" id="courseSelect" name="course_id" required>
                            <option value="">-- اختر الكورس --</option>
                            {% for course in courses %}
                                <option value="{{ course.id }}">{{ course.title }} - {{ course.instructor_name or 'غير محدد' }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="maxUses" class="form-label">عدد الاستخدامات المسموحة</label>
                        <input type="number" class="form-control" id="maxUses" name="max_uses" value="1" min="1" max="1000" required>
                        <div class="form-text">الحد الأقصى لعدد الطلاب الذين يمكنهم استخدام هذا الكود</div>
                    </div>
                    <div class="mb-3">
                        <label for="expiresInDays" class="form-label">انتهاء الصلاحية (بالأيام)</label>
                        <input type="number" class="form-control" id="expiresInDays" name="expires_in_days" min="1" max="365">
                        <div class="form-text">اتركه فارغاً إذا كنت لا تريد انتهاء صلاحية</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء الكود
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// نسخ الكود
function copyCode(code) {
    navigator.clipboard.writeText(code).then(function() {
        showAlert('تم نسخ الكود بنجاح!', 'success');
    }).catch(function() {
        // Fallback للمتصفحات القديمة
        const textArea = document.createElement('textarea');
        textArea.value = code;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showAlert('تم نسخ الكود بنجاح!', 'success');
    });
}

// فلترة الجدول
function filterTable() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const courseFilter = document.getElementById('courseFilter').value;
    const rows = document.querySelectorAll('#codesTable tbody tr');
    
    rows.forEach(row => {
        const code = row.querySelector('.code-display').textContent.toLowerCase();
        const courseTitle = row.querySelector('td:nth-child(2) strong').textContent.toLowerCase();
        const instructorName = row.querySelector('.instructor-info small').textContent.toLowerCase();
        const status = row.getAttribute('data-status');
        const courseId = row.getAttribute('data-course-id');
        
        const matchesSearch = code.includes(searchTerm) || 
                            courseTitle.includes(searchTerm) || 
                            instructorName.includes(searchTerm);
        const matchesStatus = !statusFilter || status === statusFilter;
        const matchesCourse = !courseFilter || courseId === courseFilter;
        
        if (matchesSearch && matchesStatus && matchesCourse) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// إعادة تعيين الفلاتر
function resetFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('courseFilter').value = '';
    filterTable();
}

// ربط أحداث الفلترة
document.getElementById('searchInput').addEventListener('input', filterTable);
document.getElementById('statusFilter').addEventListener('change', filterTable);
document.getElementById('courseFilter').addEventListener('change', filterTable);

// إنشاء كود جديد
document.getElementById('createCodeForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    // تحويل القيم الرقمية
    if (data.max_uses) data.max_uses = parseInt(data.max_uses);
    if (data.expires_in_days) data.expires_in_days = parseInt(data.expires_in_days);
    
    fetch('/api/activation-codes/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('createCodeModal')).hide();
            location.reload();
        } else {
            showAlert(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في النظام', 'error');
    });
});

// تعطيل كود
function deactivateCode(codeId) {
    if (confirm('هل أنت متأكد من تعطيل هذا الكود؟')) {
        fetch(`/api/activation-codes/${codeId}/deactivate`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                location.reload();
            } else {
                showAlert(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ في النظام', 'error');
        });
    }
}

// حذف كود
function deleteCode(codeId) {
    if (confirm('هل أنت متأكد من حذف هذا الكود؟ لا يمكن التراجع عن هذا الإجراء.')) {
        fetch(`/api/activation-codes/${codeId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                location.reload();
            } else {
                showAlert(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ في النظام', 'error');
        });
    }
}

// عرض التنبيهات
function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const container = document.querySelector('.container');
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

// وظائف إدارة انتهاء الصلاحية
function showExpiringCodes() {
    fetch('/api/activation-codes/expiring?days=7')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayExpiringCodesModal(data.codes);
            } else {
                showAlert(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ في النظام', 'error');
        });
}

function displayExpiringCodesModal(codes) {
    let modalContent = `
        <div class="modal fade" id="expiringCodesModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">الأكواد المنتهية قريباً</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
    `;

    if (codes.length === 0) {
        modalContent += '<p class="text-center">لا توجد أكواد ستنتهي قريباً</p>';
    } else {
        modalContent += `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>الكود</th>
                            <th>الكورس</th>
                            <th>المدرس</th>
                            <th>ينتهي خلال</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        codes.forEach(code => {
            modalContent += `
                <tr>
                    <td><code>${code.code}</code></td>
                    <td>${code.course_title || 'غير محدد'}</td>
                    <td>${code.instructor_name || 'غير محدد'}</td>
                    <td>
                        <span class="badge bg-warning">${code.days_until_expiry} يوم</span>
                    </td>
                </tr>
            `;
        });

        modalContent += '</tbody></table></div>';
    }

    modalContent += `
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إزالة المودال السابق إن وجد
    const existingModal = document.getElementById('expiringCodesModal');
    if (existingModal) {
        existingModal.remove();
    }

    // إضافة المودال الجديد
    document.body.insertAdjacentHTML('beforeend', modalContent);

    // عرض المودال
    const modal = new bootstrap.Modal(document.getElementById('expiringCodesModal'));
    modal.show();
}

function showArchivedCodes() {
    fetch('/api/activation-codes/archived')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayArchivedCodesModal(data.codes);
            } else {
                showAlert(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ في النظام', 'error');
        });
}

function displayArchivedCodesModal(codes) {
    let modalContent = `
        <div class="modal fade" id="archivedCodesModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">الأكواد المؤرشفة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
    `;

    if (codes.length === 0) {
        modalContent += '<p class="text-center">لا توجد أكواد مؤرشفة</p>';
    } else {
        modalContent += `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>الكود</th>
                            <th>الكورس</th>
                            <th>تاريخ الأرشفة</th>
                            <th>مدة الأرشفة</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        codes.forEach(code => {
            modalContent += `
                <tr>
                    <td><code>${code.code}</code></td>
                    <td>${code.course_title || 'غير محدد'}</td>
                    <td>${new Date(code.archived_at).toLocaleDateString('ar-SA')}</td>
                    <td>${code.archived_days_ago || 0} يوم</td>
                </tr>
            `;
        });

        modalContent += '</tbody></table></div>';
    }

    modalContent += `
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إزالة المودال السابق إن وجد
    const existingModal = document.getElementById('archivedCodesModal');
    if (existingModal) {
        existingModal.remove();
    }

    // إضافة المودال الجديد
    document.body.insertAdjacentHTML('beforeend', modalContent);

    // عرض المودال
    const modal = new bootstrap.Modal(document.getElementById('archivedCodesModal'));
    modal.show();
}

function cleanupExpiredCodes() {
    if (!confirm('هل أنت متأكد من تنظيف الأكواد المنتهية الصلاحية؟ سيتم أرشفة الأكواد القديمة وحذفها من القائمة الرئيسية.')) {
        return;
    }

    const daysOld = prompt('كم يوم من انتهاء الصلاحية تريد تنظيف الأكواد؟ (افتراضي: 30)', '30');
    if (!daysOld) return;

    fetch('/api/activation-codes/cleanup', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            days_old: parseInt(daysOld),
            archive: true
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            location.reload();
        } else {
            showAlert(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في النظام', 'error');
    });
}

function showSchedulerStatus() {
    fetch('/api/scheduler/status')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displaySchedulerStatusModal(data.status);
            } else {
                showAlert(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ في النظام', 'error');
        });
}

function displaySchedulerStatusModal(status) {
    let modalContent = `
        <div class="modal fade" id="schedulerStatusModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">حالة مجدول المهام</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>حالة المجدول</h6>
                                <p class="badge ${status.is_running ? 'bg-success' : 'bg-danger'}">
                                    ${status.is_running ? 'يعمل' : 'متوقف'}
                                </p>
                            </div>
                            <div class="col-md-6">
                                <h6>عدد المهام</h6>
                                <p>${status.jobs ? status.jobs.length : 0} مهمة</p>
                            </div>
                        </div>

                        <h6>الإعدادات الحالية</h6>
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between">
                                <span>فترة الفحص</span>
                                <span>${status.config?.check_interval_minutes || 30} دقيقة</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>إشعار قبل انتهاء الصلاحية</span>
                                <span>${status.config?.notification_days_before || 3} أيام</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>تنظيف بعد انتهاء الصلاحية</span>
                                <span>${status.config?.cleanup_days_after || 30} يوم</span>
                            </li>
                        </ul>

                        ${status.jobs && status.jobs.length > 0 ? `
                            <h6 class="mt-3">المهام المجدولة</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>اسم المهمة</th>
                                            <th>التشغيل التالي</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${status.jobs.map(job => `
                                            <tr>
                                                <td>${job.name}</td>
                                                <td>${job.next_run ? new Date(job.next_run).toLocaleString('ar-SA') : 'غير محدد'}</td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        ` : ''}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // إزالة المودال السابق إن وجد
    const existingModal = document.getElementById('schedulerStatusModal');
    if (existingModal) {
        existingModal.remove();
    }

    // إضافة المودال الجديد
    document.body.insertAdjacentHTML('beforeend', modalContent);

    // عرض المودال
    const modal = new bootstrap.Modal(document.getElementById('schedulerStatusModal'));
    modal.show();
}
</script>
{% endblock %}
