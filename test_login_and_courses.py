#!/usr/bin/env python3
"""
اختبار تسجيل الدخول وجلب الكورسات
"""

import requests
import json
import sys

def test_login_and_courses():
    """اختبار تسجيل الدخول وجلب الكورسات"""
    
    base_url = "http://127.0.0.1:5000"
    
    print("🔍 اختبار تسجيل الدخول وجلب الكورسات...")
    
    # بيانات تسجيل الدخول
    login_data = {
        'email': '<EMAIL>',
        'password': 'instructor123'
    }
    
    print(f"📧 محاولة تسجيل الدخول بـ: {login_data['email']}")
    
    # إنشاء session للحفاظ على cookies
    session = requests.Session()
    
    try:
        # 1. اختبار تسجيل الدخول
        print("\n🔐 اختبار تسجيل الدخول...")
        login_response = session.post(
            f"{base_url}/api/auth/login",
            json=login_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📊 استجابة تسجيل الدخول: {login_response.status_code}")
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            print("✅ تم تسجيل الدخول بنجاح")
            print(f"   الرسالة: {login_result.get('message', 'غير محدد')}")
            
            # الحصول على token إذا كان موجوداً
            token = login_result.get('token')
            if token:
                print(f"🔑 تم الحصول على token")
                # إضافة token للـ headers
                session.headers.update({'Authorization': f'Bearer {token}'})
            
        else:
            print("❌ فشل في تسجيل الدخول")
            print(f"   الاستجابة: {login_response.text}")
            return False
        
        # 2. اختبار جلب الكورسات
        print("\n📚 اختبار جلب كورسات المدرس...")
        courses_response = session.get(f"{base_url}/api/instructor/courses")
        
        print(f"📊 استجابة جلب الكورسات: {courses_response.status_code}")
        
        if courses_response.status_code == 200:
            courses_result = courses_response.json()
            print("✅ تم جلب الكورسات بنجاح")
            
            if courses_result.get('success'):
                courses = courses_result.get('courses', [])
                print(f"📊 عدد الكورسات: {len(courses)}")
                
                if courses:
                    print("\n📚 الكورسات الموجودة:")
                    for i, course in enumerate(courses, 1):
                        title = course.get('title', 'بدون عنوان')
                        status = course.get('status', 'غير محدد')
                        stage = course.get('stage', 'غير محدد')
                        instructor_id = course.get('instructor_id', 'غير محدد')
                        is_general = course.get('is_general', False)
                        
                        status_emoji = "📝" if status == 'draft' else "✅"
                        general_text = " (عام)" if is_general else ""
                        
                        print(f"  {status_emoji} {i}. {title}{general_text}")
                        print(f"     الحالة: {status}")
                        print(f"     المرحلة: {stage}")
                        print(f"     معرف المدرس: {instructor_id}")
                        print()
                else:
                    print("⚠️ لا توجد كورسات لهذا المدرس")
                    print("   هذا قد يعني:")
                    print("   - المدرس لم ينشئ أي كورسات بعد")
                    print("   - هناك مشكلة في ربط الكورسات بالمدرس")
                    print("   - مشكلة في قاعدة البيانات")
            else:
                print("❌ فشل في جلب الكورسات")
                print(f"   الرسالة: {courses_result.get('message', 'غير محدد')}")
        
        elif courses_response.status_code == 401:
            print("❌ غير مصرح - مشكلة في المصادقة")
            print("   قد تكون المشكلة في:")
            print("   - انتهاء صلاحية الجلسة")
            print("   - مشكلة في JWT token")
            print("   - مشكلة في middleware المصادقة")
        
        elif courses_response.status_code == 403:
            print("❌ ممنوع - المستخدم ليس مدرساً")
            print("   تحقق من دور المستخدم في قاعدة البيانات")
        
        else:
            print(f"❌ خطأ في جلب الكورسات: {courses_response.status_code}")
            print(f"   الاستجابة: {courses_response.text}")
        
        # 3. اختبار معلومات المستخدم الحالي
        print("\n👤 اختبار معلومات المستخدم الحالي...")
        user_response = session.get(f"{base_url}/api/auth/current-user")
        
        if user_response.status_code == 200:
            user_result = user_response.json()
            if user_result.get('success'):
                user_data = user_result.get('user', {})
                print("✅ معلومات المستخدم:")
                print(f"   الاسم: {user_data.get('full_name', 'غير محدد')}")
                print(f"   البريد: {user_data.get('email', 'غير محدد')}")
                print(f"   الدور: {user_data.get('role', 'غير محدد')}")
                print(f"   معرف المستخدم: {user_data.get('user_id', 'غير محدد')}")
                print(f"   التخصص: {user_data.get('specialization_id', 'غير محدد')}")
            else:
                print("❌ فشل في جلب معلومات المستخدم")
        else:
            print(f"❌ خطأ في جلب معلومات المستخدم: {user_response.status_code}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ فشل في الاتصال بالخادم")
        print("   تأكد من أن الخادم يعمل على http://127.0.0.1:5000")
        return False
    
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_login_and_courses()
    if success:
        print("\n✅ تم اختبار النظام")
    else:
        print("\n❌ فشل في اختبار النظام")
        sys.exit(1)
