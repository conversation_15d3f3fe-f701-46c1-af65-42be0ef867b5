/* ===== إدارة التخصصات - تصميم مخصص ===== */

/* بطاقات التخصصات */
.specialization-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
    position: relative;
}

.specialization-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.specialization-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { background-position: 200% 0; }
    50% { background-position: -200% 0; }
}

.specialization-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.specialization-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
    50% { transform: translate(-50%, -50%) rotate(180deg); }
}

.specialization-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    position: relative;
    z-index: 1;
}

.specialization-body {
    padding: 2rem;
    position: relative;
}

/* شارات المراحل */
.stage-badge {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 25px;
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    font-weight: 600;
    margin: 0.25rem;
    display: inline-block;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
}

.stage-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

/* أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    margin-top: 1.5rem;
    flex-wrap: wrap;
}

.btn-edit {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    color: white;
    border-radius: 12px;
    padding: 0.6rem 1.2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

.btn-edit:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.6);
    color: white;
}

.btn-delete {
    background: linear-gradient(45deg, #dc3545, #fd7e14);
    border: none;
    color: white;
    border-radius: 12px;
    padding: 0.6rem 1.2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
}

.btn-delete:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.6);
    color: white;
}

/* بطاقات الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: slide 3s infinite;
}

@keyframes slide {
    0% { left: -100%; }
    100% { left: 100%; }
}

.stats-number {
    font-size: 3rem;
    font-weight: bold;
    text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    position: relative;
    z-index: 1;
}

.stats-card.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    box-shadow: 0 10px 30px rgba(40, 167, 69, 0.3);
}

.stats-card.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
    box-shadow: 0 10px 30px rgba(23, 162, 184, 0.3);
}

.stats-card.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    box-shadow: 0 10px 30px rgba(255, 193, 7, 0.3);
}

/* منطقة رفع الملفات */
.upload-area {
    border: 3px dashed #dee2e6;
    border-radius: 15px;
    padding: 3rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    position: relative;
    overflow: hidden;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(102, 126, 234, 0.05), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.upload-area:hover {
    border-color: #667eea;
    background: linear-gradient(45deg, #f0f4ff, #e6f0ff);
    transform: scale(1.02);
}

.upload-area:hover::before {
    transform: translateX(100%);
}

.upload-area.dragover {
    border-color: #667eea;
    background: linear-gradient(45deg, #e6f0ff, #d1e7ff);
    transform: scale(1.05);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
}

/* تحسينات للنماذج */
.modal-content {
    border-radius: 20px;
    border: none;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
}

.modal-header {
    border-radius: 20px 20px 0 0;
    border-bottom: none;
    padding: 2rem;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: none;
    padding: 2rem;
    border-radius: 0 0 20px 20px;
}

/* تحسينات للحقول */
.form-control, .form-select {
    border-radius: 12px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-2px);
}

/* معاينة الأيقونة */
#icon-preview {
    transition: all 0.3s ease;
}

#icon-preview.fa-question {
    color: #dee2e6 !important;
}

/* تحسينات للأزرار */
.btn {
    border-radius: 12px;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: none;
}

.btn:hover {
    transform: translateY(-2px);
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
}

.btn-success:hover {
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.6);
}

/* تحسينات للتنبيهات */
.alert {
    border-radius: 15px;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.alert-success {
    background: linear-gradient(45deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(45deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

/* تحسينات للجداول */
.table {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    font-weight: 600;
}

/* تحسينات للبطاقات */
.card {
    border-radius: 20px;
    border: none;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.card-header {
    border-radius: 20px 20px 0 0;
    border-bottom: none;
    font-weight: 600;
}

/* تحسينات للشارات */
.badge {
    border-radius: 12px;
    padding: 0.5rem 1rem;
    font-weight: 600;
}

/* تأثيرات التحميل */
.spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.3rem;
}

/* تحسينات للاستجابة */
@media (max-width: 768px) {
    .specialization-card {
        margin-bottom: 1.5rem;
    }
    
    .stats-card {
        margin-bottom: 1rem;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .action-buttons .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .upload-area {
        padding: 2rem 1rem;
    }
    
    .modal-body, .modal-header, .modal-footer {
        padding: 1.5rem;
    }
}
