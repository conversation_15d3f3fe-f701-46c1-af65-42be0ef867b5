"""
اختبارات الأداء والسرعة
Performance and Speed Tests
"""

import unittest
import time
import threading
import requests
import sys
import os
from concurrent.futures import ThreadPoolExecutor
from unittest.mock import patch, MagicMock

# إضافة مسار المشروع للاستيراد
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from utils.firebase_utils import get_firebase_manager

class PerformanceTests(unittest.TestCase):
    """اختبارات الأداء والسرعة"""
    
    @classmethod
    def setUpClass(cls):
        """إعداد الاختبارات"""
        cls.app = create_app('testing')
        cls.client = cls.app.test_client()
        cls.app_context = cls.app.app_context()
        cls.app_context.push()
    
    @classmethod
    def tearDownClass(cls):
        """تنظيف بعد الاختبارات"""
        cls.app_context.pop()
    
    def test_01_application_startup_time(self):
        """اختبار وقت بدء تشغيل التطبيق"""
        start_time = time.time()
        
        # محاكاة بدء تشغيل التطبيق
        app = create_app('testing')
        
        startup_time = time.time() - start_time
        
        # يجب أن يكون وقت البدء أقل من 3 ثوان
        self.assertLess(startup_time, 3.0)
        print(f"✅ وقت بدء التشغيل: {startup_time:.3f} ثانية")
    
    def test_02_page_load_time(self):
        """اختبار وقت تحميل الصفحات"""
        pages = [
            '/',
            '/login',
            '/courses',
            '/about'
        ]
        
        for page in pages:
            start_time = time.time()
            response = self.client.get(page)
            load_time = time.time() - start_time
            
            # يجب أن يكون وقت التحميل أقل من ثانية واحدة
            self.assertLess(load_time, 1.0)
            self.assertIn(response.status_code, [200, 302, 404])
            
            print(f"✅ {page}: {load_time:.3f} ثانية")
    
    def test_03_database_query_performance(self):
        """اختبار أداء استعلامات قاعدة البيانات"""
        firebase_manager = get_firebase_manager()
        
        # محاكاة استعلامات قاعدة البيانات
        with patch.object(firebase_manager, 'get_all_users') as mock_get_users:
            mock_get_users.return_value = [{'id': i, 'name': f'User {i}'} for i in range(100)]
            
            start_time = time.time()
            users = firebase_manager.get_all_users()
            query_time = time.time() - start_time
            
            # يجب أن يكون وقت الاستعلام أقل من 0.5 ثانية
            self.assertLess(query_time, 0.5)
            self.assertEqual(len(users), 100)
            
            print(f"✅ وقت استعلام قاعدة البيانات: {query_time:.3f} ثانية")
    
    def test_04_concurrent_requests(self):
        """اختبار الطلبات المتزامنة"""
        def make_request():
            response = self.client.get('/')
            return response.status_code
        
        start_time = time.time()
        
        # تشغيل 20 طلب متزامن
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(20)]
            results = [future.result() for future in futures]
        
        total_time = time.time() - start_time
        
        # يجب أن تكتمل جميع الطلبات في أقل من 5 ثوان
        self.assertLess(total_time, 5.0)
        
        # يجب أن تنجح معظم الطلبات
        successful_requests = sum(1 for status in results if status == 200)
        success_rate = successful_requests / len(results)
        self.assertGreaterEqual(success_rate, 0.8)
        
        print(f"✅ 20 طلب متزامن في {total_time:.3f} ثانية - معدل النجاح: {success_rate:.1%}")
    
    def test_05_memory_usage(self):
        """اختبار استخدام الذاكرة"""
        import psutil
        import gc
        
        # قياس الذاكرة قبل العمليات
        gc.collect()
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # تنفيذ عمليات متعددة
        for i in range(100):
            response = self.client.get('/')
            self.assertEqual(response.status_code, 200)
        
        # قياس الذاكرة بعد العمليات
        gc.collect()
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # يجب ألا تزيد الذاكرة بأكثر من 50 MB
        self.assertLess(memory_increase, 50)
        
        print(f"✅ استخدام الذاكرة: {initial_memory:.1f} MB → {final_memory:.1f} MB (+{memory_increase:.1f} MB)")
    
    def test_06_static_file_serving(self):
        """اختبار تقديم الملفات الثابتة"""
        static_files = [
            '/static/css/main.css',
            '/static/js/main.js',
            '/static/css/performance.css',
            '/static/js/performance.js'
        ]
        
        for file_path in static_files:
            start_time = time.time()
            response = self.client.get(file_path)
            serve_time = time.time() - start_time
            
            # يجب أن يكون وقت تقديم الملف أقل من 0.1 ثانية
            self.assertLess(serve_time, 0.1)
            self.assertIn(response.status_code, [200, 404])
            
            if response.status_code == 200:
                print(f"✅ {file_path}: {serve_time:.3f} ثانية")
            else:
                print(f"⚠️ {file_path}: غير موجود")
    
    def test_07_compression_performance(self):
        """اختبار أداء الضغط"""
        # اختبار ضغط الاستجابات
        response = self.client.get('/', headers={'Accept-Encoding': 'gzip'})
        
        # التحقق من وجود ضغط
        content_encoding = response.headers.get('Content-Encoding', '')
        if 'gzip' in content_encoding:
            print("✅ تم تفعيل ضغط gzip")
        else:
            print("⚠️ ضغط gzip غير مفعل")
        
        # قياس حجم الاستجابة
        content_length = len(response.data)
        self.assertGreater(content_length, 0)
        
        print(f"✅ حجم الاستجابة: {content_length} بايت")
    
    def test_08_caching_performance(self):
        """اختبار أداء التخزين المؤقت"""
        # الطلب الأول (بدون تخزين مؤقت)
        start_time = time.time()
        response1 = self.client.get('/static/css/main.css')
        first_request_time = time.time() - start_time
        
        # الطلب الثاني (مع التخزين المؤقت)
        start_time = time.time()
        response2 = self.client.get('/static/css/main.css')
        second_request_time = time.time() - start_time
        
        # يجب أن يكون الطلب الثاني أسرع أو مساوي للأول
        self.assertLessEqual(second_request_time, first_request_time * 1.5)
        
        print(f"✅ الطلب الأول: {first_request_time:.3f}s، الثاني: {second_request_time:.3f}s")
    
    def test_09_lazy_loading_performance(self):
        """اختبار أداء التحميل التدريجي"""
        # محاكاة صفحة بها صور كثيرة
        html_content = """
        <html>
        <body>
            <img data-src="image1.jpg" class="lazy">
            <img data-src="image2.jpg" class="lazy">
            <img data-src="image3.jpg" class="lazy">
        </body>
        </html>
        """
        
        # قياس وقت معالجة HTML
        start_time = time.time()
        
        # محاكاة معالجة الصور التدريجية
        lazy_images = html_content.count('class="lazy"')
        processing_time = time.time() - start_time
        
        self.assertEqual(lazy_images, 3)
        self.assertLess(processing_time, 0.01)  # معالجة سريعة جداً
        
        print(f"✅ معالجة {lazy_images} صور تدريجية في {processing_time:.4f} ثانية")
    
    def test_10_api_response_time(self):
        """اختبار وقت استجابة API"""
        api_endpoints = [
            '/api/courses',
            '/api/users',
            '/api/analytics'
        ]
        
        for endpoint in api_endpoints:
            start_time = time.time()
            response = self.client.get(endpoint)
            response_time = time.time() - start_time
            
            # يجب أن يكون وقت الاستجابة أقل من 0.5 ثانية
            self.assertLess(response_time, 0.5)
            self.assertIn(response.status_code, [200, 401, 403, 404])
            
            print(f"✅ {endpoint}: {response_time:.3f} ثانية")
    
    def test_11_form_processing_performance(self):
        """اختبار أداء معالجة النماذج"""
        form_data = {
            'title': 'كورس تجريبي',
            'description': 'وصف الكورس التجريبي',
            'category': 'برمجة',
            'level': 'مبتدئ'
        }
        
        start_time = time.time()
        response = self.client.post('/instructor/courses/new', data=form_data)
        processing_time = time.time() - start_time
        
        # يجب أن يكون وقت المعالجة أقل من ثانية واحدة
        self.assertLess(processing_time, 1.0)
        self.assertIn(response.status_code, [200, 302, 401, 403])
        
        print(f"✅ معالجة النموذج: {processing_time:.3f} ثانية")
    
    def test_12_search_performance(self):
        """اختبار أداء البحث"""
        search_queries = [
            'برمجة',
            'رياضيات',
            'فيزياء',
            'كيمياء'
        ]
        
        for query in search_queries:
            start_time = time.time()
            response = self.client.get(f'/courses?search={query}')
            search_time = time.time() - start_time
            
            # يجب أن يكون وقت البحث أقل من 0.5 ثانية
            self.assertLess(search_time, 0.5)
            self.assertIn(response.status_code, [200, 302])
            
            print(f"✅ البحث عن '{query}': {search_time:.3f} ثانية")
    
    def test_13_video_player_performance(self):
        """اختبار أداء مشغل الفيديو"""
        # محاكاة تحميل صفحة الفيديو
        start_time = time.time()
        response = self.client.get('/student/course/test/lesson/test')
        load_time = time.time() - start_time
        
        # يجب أن يكون وقت التحميل أقل من ثانية واحدة
        self.assertLess(load_time, 1.0)
        self.assertIn(response.status_code, [200, 302, 404])
        
        print(f"✅ تحميل مشغل الفيديو: {load_time:.3f} ثانية")
    
    def test_14_dashboard_performance(self):
        """اختبار أداء لوحات التحكم"""
        dashboards = [
            ('/admin/dashboard', 'admin'),
            ('/instructor/dashboard', 'instructor'),
            ('/student/dashboard', 'student')
        ]
        
        for dashboard_url, role in dashboards:
            # محاكاة تسجيل الدخول
            with self.client.session_transaction() as sess:
                sess['user_id'] = f'{role}_test_id'
                sess['user_role'] = role
            
            start_time = time.time()
            response = self.client.get(dashboard_url)
            load_time = time.time() - start_time
            
            # يجب أن يكون وقت التحميل أقل من ثانية واحدة
            self.assertLess(load_time, 1.0)
            self.assertIn(response.status_code, [200, 302])
            
            print(f"✅ لوحة تحكم {role}: {load_time:.3f} ثانية")
    
    def test_15_overall_performance_score(self):
        """حساب نقاط الأداء الإجمالية"""
        performance_metrics = []
        
        # اختبار سرعة الاستجابة العامة
        start_time = time.time()
        for i in range(10):
            response = self.client.get('/')
            self.assertEqual(response.status_code, 200)
        avg_response_time = (time.time() - start_time) / 10
        
        performance_metrics.append(('متوسط وقت الاستجابة', avg_response_time, 0.1))
        
        # حساب النقاط
        total_score = 0
        max_score = 0
        
        for metric_name, actual_value, target_value in performance_metrics:
            if actual_value <= target_value:
                score = 100
            else:
                score = max(0, 100 - ((actual_value - target_value) / target_value * 100))
            
            total_score += score
            max_score += 100
            
            print(f"✅ {metric_name}: {actual_value:.3f}s (الهدف: {target_value:.3f}s) - النقاط: {score:.1f}/100")
        
        overall_score = (total_score / max_score) * 100 if max_score > 0 else 0
        
        print(f"\n🏆 نقاط الأداء الإجمالية: {overall_score:.1f}/100")
        
        # يجب أن تكون النقاط أعلى من 70
        self.assertGreaterEqual(overall_score, 70)

if __name__ == '__main__':
    print("⚡ بدء اختبارات الأداء والسرعة...")
    print("=" * 50)
    
    # تشغيل الاختبارات
    unittest.main(verbosity=2)
    
    print("=" * 50)
    print("✅ تم إكمال جميع اختبارات الأداء بنجاح!")
