{% extends "base.html" %}

{% block title %}{{ course.title }} - {{ platform_name }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/advanced-video-player.css') }}">
<style>
.lesson-item {
    transition: all 0.3s ease;
    cursor: pointer;
}

.lesson-item:hover {
    background-color: #f8f9fa;
    transform: translateX(-5px);
}

.lesson-item.active {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.lesson-item.completed {
    background-color: #e8f5e8;
    border-left: 4px solid #4caf50;
}

.lesson-progress-bar {
    height: 4px;
    background-color: #2196f3;
    transition: width 0.3s ease;
}

/* تحسينات إضافية للمشغل */
.advanced-video-player {
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.lesson-header {
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
}

.lesson-body {
    padding-top: 20px;
}

.course-progress {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <!-- قائمة الدروس -->
        <div class="col-lg-4 col-xl-3">
            <div class="card border-0 shadow-sm sticky-top" style="top: 20px;">
                <!-- معلومات الكورس -->
                <div class="card-header course-progress text-white">
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-book me-2"></i>
                        <h6 class="mb-0">{{ course.title }}</h6>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <small>التقدم الإجمالي</small>
                        <small id="overallProgress">0%</small>
                    </div>
                    <div class="progress mt-2" style="height: 6px;">
                        <div class="progress-bar bg-white" id="overallProgressBar" style="width: 0%"></div>
                    </div>
                </div>
                
                <!-- قائمة الدروس -->
                <div class="card-body p-0">
                    <div class="list-group list-group-flush" id="lessonsList">
                        <!-- سيتم تحميل الدروس هنا -->
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- محتوى الدرس -->
        <div class="col-lg-8 col-xl-9">
            <div class="card border-0 shadow-sm">
                <div class="card-body" id="lessonContent">
                    <!-- رسالة ترحيب -->
                    <div class="text-center py-5" id="welcomeMessage">
                        <i class="fas fa-play-circle fa-5x text-primary mb-4"></i>
                        <h3 class="text-primary">مرحباً بك في الكورس</h3>
                        <p class="text-muted">اختر درساً من القائمة الجانبية لبدء المشاهدة</p>
                        <div class="mt-4">
                            <h5 class="text-muted">معلومات الكورس</h5>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-user text-primary me-2"></i>
                                        <span>المدرس: {{ course.instructor_name }}</span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fas fa-layer-group text-primary me-2"></i>
                                        <span>
                                            {% if course.is_general %}
                                                كورس عام
                                            {% else %}
                                                المرحلة {{ course.stage }}
                                            {% endif %}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            {% if course.description %}
                            <div class="mt-3">
                                <p class="text-muted">{{ course.description }}</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/video-progress.js') }}"></script>
<script src="{{ url_for('static', filename='js/advanced-video-player.js') }}"></script>
<script>
let currentCourse = null;
let currentLesson = null;
let lessons = [];

$(document).ready(function() {
    currentCourse = {
        id: '{{ course.id }}',
        title: '{{ course.title }}',
        instructor_name: '{{ course.instructor_name }}'
    };
    
    loadLessons();

    // تدمير المشغل عند مغادرة الصفحة
    window.addEventListener('beforeunload', function() {
        if (advancedPlayer) {
            advancedPlayer.destroy();
        }
    });
});

async function loadLessons() {
    try {
        const response = await fetch(`/api/instructor/courses/${currentCourse.id}/lessons`);
        const data = await response.json();
        
        if (data.success) {
            lessons = data.lessons || [];
            displayLessons(lessons);
            updateOverallProgress();
        } else {
            showAlert('فشل في تحميل الدروس: ' + data.message, 'danger');
        }
    } catch (error) {
        console.error('خطأ في تحميل الدروس:', error);
        showAlert('حدث خطأ في تحميل الدروس', 'danger');
    }
}

function displayLessons(lessons) {
    const container = $('#lessonsList');
    
    if (!lessons || lessons.length === 0) {
        container.html(`
            <div class="text-center py-4">
                <i class="fas fa-book-open fa-3x text-muted mb-3"></i>
                <h6 class="text-muted">لا توجد دروس</h6>
                <p class="text-muted small">لم يتم إضافة دروس لهذا الكورس بعد</p>
            </div>
        `);
        return;
    }
    
    let html = '';
    
    lessons.forEach(function(lesson, index) {
        const isCompleted = false; // سيتم تحديثه لاحقاً من بيانات التقدم
        const isActive = false;
        
        html += `
            <div class="list-group-item lesson-item ${isCompleted ? 'completed' : ''} ${isActive ? 'active' : ''}" 
                 data-lesson-id="${lesson.id}" onclick="loadLesson('${lesson.id}')">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center">
                            <div class="lesson-status-icon me-2">
                                ${isCompleted ? 
                                    '<i class="fas fa-check-circle text-success"></i>' : 
                                    '<i class="fas fa-play-circle text-primary"></i>'
                                }
                            </div>
                            <div>
                                <h6 class="mb-1">${lesson.title}</h6>
                                <small class="text-muted">
                                    ${lesson.content_type === 'video' ? 
                                        '<i class="fas fa-video me-1"></i>فيديو' : 
                                        '<i class="fas fa-file-text me-1"></i>نص'
                                    }
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="lesson-number">
                        <span class="badge bg-light text-dark">${index + 1}</span>
                    </div>
                </div>
                
                <!-- شريط التقدم للدرس -->
                <div class="progress mt-2" style="height: 3px;">
                    <div class="progress-bar lesson-progress-bar" style="width: 0%"></div>
                </div>
            </div>
        `;
    });
    
    container.html(html);
}

async function loadLesson(lessonId) {
    try {
        // تحديث الدرس النشط في القائمة
        $('.lesson-item').removeClass('active');
        $(`.lesson-item[data-lesson-id="${lessonId}"]`).addClass('active');
        
        // البحث عن الدرس
        const lesson = lessons.find(l => l.id === lessonId);
        if (!lesson) {
            showAlert('الدرس غير موجود', 'danger');
            return;
        }
        
        currentLesson = lesson;
        
        // عرض محتوى الدرس
        displayLessonContent(lesson);
        
    } catch (error) {
        console.error('خطأ في تحميل الدرس:', error);
        showAlert('حدث خطأ في تحميل الدرس', 'danger');
    }
}

function displayLessonContent(lesson) {
    const container = $('#lessonContent');
    
    let html = `
        <div class="lesson-header mb-4">
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <h4 class="text-primary">${lesson.title}</h4>
                    <div class="d-flex align-items-center text-muted">
                        <i class="fas fa-${lesson.content_type === 'video' ? 'video' : 'file-text'} me-2"></i>
                        <span>${lesson.content_type === 'video' ? 'درس فيديو' : 'درس نصي'}</span>
                    </div>
                </div>
                <div>
                    <button class="btn btn-outline-success btn-sm" onclick="markLessonAsCompleted('${currentCourse.id}', '${lesson.id}')">
                        <i class="fas fa-check me-1"></i>تم الإكمال
                    </button>
                </div>
            </div>
            
            <!-- شريط التقدم للدرس الحالي -->
            <div class="mt-3">
                <div class="d-flex justify-content-between align-items-center mb-1">
                    <small class="text-muted">تقدم الدرس</small>
                    <small class="text-muted lesson-progress-text">0% مكتمل</small>
                </div>
                <div class="progress" style="height: 6px;">
                    <div class="progress-bar lesson-progress-bar" style="width: 0%"></div>
                </div>
            </div>
        </div>
        
        <div class="lesson-body">
    `;
    
    if (lesson.content_type === 'video' && lesson.content_data && lesson.content_data.youtube_id) {
        html += `
            <div id="advancedVideoPlayer" class="mb-4">
                <!-- سيتم إنشاء المشغل هنا -->
            </div>
        `;
    }
    
    if (lesson.content_data && lesson.content_data.description) {
        html += `
            <div class="lesson-description">
                <h5>وصف الدرس</h5>
                <div class="content-text">
                    ${lesson.content_data.description}
                </div>
            </div>
        `;
    }
    
    html += '</div>';
    
    container.html(html);
    
    // إخفاء رسالة الترحيب
    $('#welcomeMessage').hide();
    
    // تهيئة مشغل الفيديو المتقدم إذا كان موجوداً
    if (lesson.content_type === 'video' && lesson.content_data && lesson.content_data.youtube_id) {
        initAdvancedVideoPlayer(lesson.content_data.youtube_id, lesson.id, currentCourse.id);
    }
}

// متغير لتتبع مشغل الفيديو المتقدم
let advancedPlayer = null;

function initAdvancedVideoPlayer(youtubeId, lessonId, courseId) {
    // تدمير المشغل السابق إذا كان موجوداً
    if (advancedPlayer) {
        advancedPlayer.destroy();
        advancedPlayer = null;
    }

    // إنشاء مشغل فيديو جديد
    advancedPlayer = new AdvancedVideoPlayer('advancedVideoPlayer', youtubeId, {
        autoplay: false,
        showControls: false,
        enableKeyboard: true,

        // أحداث مخصصة للتكامل مع نظام التتبع
        onReady: function(event) {
            console.log('مشغل الفيديو المتقدم جاهز');

            // تحميل التقدم المحفوظ
            loadVideoProgress(lessonId);
        },

        onStateChange: function(event) {
            const state = event.data;

            // حفظ التقدم عند الإيقاف أو الانتهاء
            if (state === YT.PlayerState.PAUSED || state === YT.PlayerState.ENDED) {
                saveVideoProgress(lessonId, advancedPlayer.getCurrentTime(), advancedPlayer.getDuration());
            }

            // تحديث حالة الدرس عند الانتهاء
            if (state === YT.PlayerState.ENDED) {
                markLessonAsCompleted(courseId, lessonId);
            }
        },

        onTimeUpdate: function(data) {
            // تحديث شريط التقدم في قائمة الدروس
            updateLessonProgress(lessonId, data.percentage);

            // حفظ التقدم كل 30 ثانية
            if (Math.floor(data.currentTime) % 30 === 0) {
                saveVideoProgress(lessonId, data.currentTime, data.duration);
            }
        }
    });
}

// دالة لتحميل التقدم المحفوظ
async function loadVideoProgress(lessonId) {
    try {
        const response = await fetch(`/api/student/progress/video/${lessonId}`);
        const data = await response.json();

        if (data.success && data.progress && data.progress.current_time) {
            const savedTime = data.progress.current_time;
            const duration = advancedPlayer.getDuration() || data.progress.duration;

            // إذا كان الفيديو محفوظ عند نقطة معينة، اسأل المستخدم
            if (savedTime > 30 && savedTime < duration - 30) {
                showResumeDialog(savedTime, lessonId);
            }
        }
    } catch (error) {
        console.error('خطأ في تحميل التقدم المحفوظ:', error);
    }
}

// دالة لحفظ تقدم الفيديو
async function saveVideoProgress(lessonId, currentTime, duration) {
    if (!currentTime || !duration || currentTime === 0) {
        return;
    }

    try {
        const progressData = {
            lesson_id: lessonId,
            current_time: currentTime,
            duration: duration
        };

        await fetch('/api/student/progress/video', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(progressData)
        });

    } catch (error) {
        console.error('خطأ في حفظ تقدم الفيديو:', error);
    }
}

// دالة لإظهار حوار استئناف المشاهدة
function showResumeDialog(savedTime, lessonId) {
    const minutes = Math.floor(savedTime / 60);
    const seconds = Math.floor(savedTime % 60);
    const timeString = `${minutes}:${seconds.toString().padStart(2, '0')}`;

    const resumeDialog = `
        <div class="alert alert-info alert-dismissible fade show" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-play-circle me-2"></i>
                <div class="flex-grow-1">
                    <strong>متابعة المشاهدة</strong><br>
                    <small>آخر نقطة توقف: ${timeString}</small>
                </div>
                <div>
                    <button type="button" class="btn btn-primary btn-sm me-2" onclick="resumeFromSaved(${savedTime})">
                        متابعة
                    </button>
                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="startFromBeginning()">
                        من البداية
                    </button>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // إضافة الحوار قبل مشغل الفيديو
    $('#advancedVideoPlayer').before(resumeDialog);
}

// دالة لاستئناف المشاهدة من النقطة المحفوظة
function resumeFromSaved(savedTime) {
    if (advancedPlayer) {
        advancedPlayer.seekTo(savedTime);
    }
    $('.alert').alert('close');
}

// دالة لبدء المشاهدة من البداية
function startFromBeginning() {
    if (advancedPlayer) {
        advancedPlayer.seekTo(0);
    }
    $('.alert').alert('close');
}

// دالة لتحديث تقدم الدرس في القائمة
function updateLessonProgress(lessonId, percentage) {
    const lessonItem = document.querySelector(`[data-lesson-id="${lessonId}"]`);
    if (lessonItem) {
        const progressBar = lessonItem.querySelector('.lesson-progress-bar');
        if (progressBar) {
            progressBar.style.width = percentage + '%';
        }
    }
}

function updateOverallProgress() {
    // حساب التقدم الإجمالي (سيتم تحديثه لاحقاً من بيانات التقدم الفعلية)
    const completedLessons = $('.lesson-item.completed').length;
    const totalLessons = lessons.length;
    const percentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0;
    
    $('#overallProgress').text(Math.round(percentage) + '%');
    $('#overallProgressBar').css('width', percentage + '%');
}

function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('.container-fluid').first().prepend(alertHtml);
    
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
{% endblock %}
