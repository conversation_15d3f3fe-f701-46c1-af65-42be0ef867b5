{% extends "base.html" %}

{% block title %}إحصائيات الطلاب - {{ platform_name }}{% endblock %}

{% block extra_css %}
<style>
.stats-card {
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.progress-ring {
    width: 60px;
    height: 60px;
}

.progress-ring circle {
    transition: stroke-dasharray 0.3s ease;
}

.student-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.course-progress-bar {
    height: 8px;
    border-radius: 4px;
}
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- العنوان والفلاتر -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="text-primary mb-1">إحصائيات الطلاب</h2>
            <p class="text-muted">تتبع تقدم الطلاب في كورساتك</p>
        </div>
        <div class="col-md-4">
            <select class="form-select" id="courseFilter">
                <option value="">جميع الكورسات</option>
            </select>
        </div>
    </div>
    
    <!-- الإحصائيات العامة -->
    <div class="row mb-4" id="overallStats">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm stats-card">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="progress-ring">
                            <svg width="60" height="60">
                                <circle cx="30" cy="30" r="25" fill="none" stroke="#e9ecef" stroke-width="4"/>
                                <circle cx="30" cy="30" r="25" fill="none" stroke="#007bff" stroke-width="4" 
                                        stroke-dasharray="0 157" stroke-linecap="round" id="totalStudentsRing"/>
                            </svg>
                            <div class="position-absolute top-50 start-50 translate-middle">
                                <i class="fas fa-users text-primary"></i>
                            </div>
                        </div>
                    </div>
                    <h4 class="mb-1" id="totalStudents">0</h4>
                    <small class="text-muted">إجمالي الطلاب</small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm stats-card">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="progress-ring">
                            <svg width="60" height="60">
                                <circle cx="30" cy="30" r="25" fill="none" stroke="#e9ecef" stroke-width="4"/>
                                <circle cx="30" cy="30" r="25" fill="none" stroke="#28a745" stroke-width="4" 
                                        stroke-dasharray="0 157" stroke-linecap="round" id="activeStudentsRing"/>
                            </svg>
                            <div class="position-absolute top-50 start-50 translate-middle">
                                <i class="fas fa-user-check text-success"></i>
                            </div>
                        </div>
                    </div>
                    <h4 class="mb-1" id="activeStudents">0</h4>
                    <small class="text-muted">طلاب نشطون</small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm stats-card">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="progress-ring">
                            <svg width="60" height="60">
                                <circle cx="30" cy="30" r="25" fill="none" stroke="#e9ecef" stroke-width="4"/>
                                <circle cx="30" cy="30" r="25" fill="none" stroke="#ffc107" stroke-width="4" 
                                        stroke-dasharray="0 157" stroke-linecap="round" id="avgProgressRing"/>
                            </svg>
                            <div class="position-absolute top-50 start-50 translate-middle">
                                <i class="fas fa-chart-line text-warning"></i>
                            </div>
                        </div>
                    </div>
                    <h4 class="mb-1" id="avgProgress">0%</h4>
                    <small class="text-muted">متوسط التقدم</small>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm stats-card">
                <div class="card-body text-center">
                    <div class="d-flex align-items-center justify-content-center mb-2">
                        <div class="progress-ring">
                            <svg width="60" height="60">
                                <circle cx="30" cy="30" r="25" fill="none" stroke="#e9ecef" stroke-width="4"/>
                                <circle cx="30" cy="30" r="25" fill="none" stroke="#dc3545" stroke-width="4" 
                                        stroke-dasharray="0 157" stroke-linecap="round" id="completionRateRing"/>
                            </svg>
                            <div class="position-absolute top-50 start-50 translate-middle">
                                <i class="fas fa-trophy text-danger"></i>
                            </div>
                        </div>
                    </div>
                    <h4 class="mb-1" id="completionRate">0%</h4>
                    <small class="text-muted">معدل الإكمال</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- تفاصيل الكورسات -->
    <div class="row">
        <!-- إحصائيات الكورسات -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">تقدم الطلاب في الكورسات</h5>
                </div>
                <div class="card-body">
                    <div id="courseProgressContainer">
                        <!-- سيتم تحميل إحصائيات الكورسات هنا -->
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- أفضل الطلاب -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">أفضل الطلاب</h5>
                </div>
                <div class="card-body">
                    <div id="topStudentsContainer">
                        <!-- سيتم تحميل أفضل الطلاب هنا -->
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- تفاصيل الطلاب -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">تفاصيل الطلاب</h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary btn-sm" id="exportBtn">
                            <i class="fas fa-download me-1"></i>تصدير
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" id="refreshBtn">
                            <i class="fas fa-sync me-1"></i>تحديث
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="studentsTable">
                            <thead class="table-light">
                                <tr>
                                    <th>الطالب</th>
                                    <th>الكورس</th>
                                    <th>التقدم</th>
                                    <th>آخر نشاط</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="studentsTableBody">
                                <!-- سيتم تحميل بيانات الطلاب هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let instructorCourses = [];
let studentsData = [];

$(document).ready(function() {
    loadInstructorCourses();
    loadStatistics();
    
    // مستمع تغيير فلتر الكورس
    $('#courseFilter').change(function() {
        loadStatistics();
    });
    
    // تحديث البيانات
    $('#refreshBtn').click(function() {
        loadStatistics();
    });
    
    // تصدير البيانات
    $('#exportBtn').click(function() {
        exportStudentsData();
    });
});

async function loadInstructorCourses() {
    try {
        const response = await fetch('/api/instructor/courses');
        const data = await response.json();
        
        if (data.success) {
            instructorCourses = data.courses || [];
            
            // تحديث فلتر الكورسات
            const select = $('#courseFilter');
            select.empty().append('<option value="">جميع الكورسات</option>');
            
            instructorCourses.forEach(course => {
                select.append(`<option value="${course.id}">${course.title}</option>`);
            });
        }
    } catch (error) {
        console.error('خطأ في تحميل كورسات المدرس:', error);
    }
}

async function loadStatistics() {
    const courseId = $('#courseFilter').val();
    const params = courseId ? `?course_id=${courseId}` : '';
    
    try {
        const response = await fetch(`/api/instructor/statistics${params}`);
        const data = await response.json();
        
        if (data.success) {
            updateOverallStats(data.stats);
            displayCourseProgress(data.course_progress || []);
            displayTopStudents(data.top_students || []);
            displayStudentsTable(data.students || []);
        } else {
            showAlert('فشل في تحميل الإحصائيات: ' + data.message, 'danger');
        }
    } catch (error) {
        console.error('خطأ في تحميل الإحصائيات:', error);
        showAlert('حدث خطأ في تحميل الإحصائيات', 'danger');
    }
}

function updateOverallStats(stats) {
    // تحديث الأرقام
    $('#totalStudents').text(stats.total_students || 0);
    $('#activeStudents').text(stats.active_students || 0);
    $('#avgProgress').text((stats.avg_progress || 0) + '%');
    $('#completionRate').text((stats.completion_rate || 0) + '%');
    
    // تحديث الدوائر
    updateProgressRing('totalStudentsRing', 100); // دائماً ممتلئة
    updateProgressRing('activeStudentsRing', stats.activity_rate || 0);
    updateProgressRing('avgProgressRing', stats.avg_progress || 0);
    updateProgressRing('completionRateRing', stats.completion_rate || 0);
}

function updateProgressRing(ringId, percentage) {
    const circumference = 2 * Math.PI * 25; // r = 25
    const offset = circumference - (percentage / 100) * circumference;
    
    $(`#${ringId}`).css('stroke-dasharray', `${circumference - offset} ${circumference}`);
}

function displayCourseProgress(courseProgress) {
    const container = $('#courseProgressContainer');
    
    if (!courseProgress || courseProgress.length === 0) {
        container.html(`
            <div class="text-center py-4">
                <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                <h6 class="text-muted">لا توجد بيانات</h6>
                <p class="text-muted small">لا توجد إحصائيات متاحة للكورسات</p>
            </div>
        `);
        return;
    }
    
    let html = '';
    
    courseProgress.forEach(course => {
        const progressPercentage = course.avg_progress || 0;
        
        html += `
            <div class="mb-4">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">${course.title}</h6>
                    <div class="text-end">
                        <small class="text-muted">${course.enrolled_students} طالب</small>
                        <div class="fw-bold">${progressPercentage}%</div>
                    </div>
                </div>
                <div class="progress course-progress-bar">
                    <div class="progress-bar ${progressPercentage >= 80 ? 'bg-success' : progressPercentage >= 50 ? 'bg-warning' : 'bg-danger'}" 
                         style="width: ${progressPercentage}%"></div>
                </div>
                <div class="d-flex justify-content-between mt-1">
                    <small class="text-muted">${course.completed_lessons || 0} درس مكتمل</small>
                    <small class="text-muted">${course.total_lessons || 0} إجمالي الدروس</small>
                </div>
            </div>
        `;
    });
    
    container.html(html);
}

function displayTopStudents(topStudents) {
    const container = $('#topStudentsContainer');
    
    if (!topStudents || topStudents.length === 0) {
        container.html(`
            <div class="text-center py-4">
                <i class="fas fa-trophy fa-3x text-muted mb-3"></i>
                <h6 class="text-muted">لا توجد بيانات</h6>
                <p class="text-muted small">لا يوجد طلاب مسجلون</p>
            </div>
        `);
        return;
    }
    
    let html = '';
    
    topStudents.forEach((student, index) => {
        const rankIcon = index === 0 ? 'fas fa-crown text-warning' : 
                        index === 1 ? 'fas fa-medal text-secondary' : 
                        index === 2 ? 'fas fa-award text-warning' : 
                        'fas fa-user';
        
        html += `
            <div class="d-flex align-items-center mb-3">
                <div class="student-avatar me-3">
                    ${student.name.charAt(0).toUpperCase()}
                </div>
                <div class="flex-grow-1">
                    <h6 class="mb-1">${student.name}</h6>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">${student.progress}% مكتمل</small>
                        <i class="${rankIcon}"></i>
                    </div>
                    <div class="progress mt-1" style="height: 4px;">
                        <div class="progress-bar bg-success" style="width: ${student.progress}%"></div>
                    </div>
                </div>
            </div>
        `;
    });
    
    container.html(html);
}

function displayStudentsTable(students) {
    const tbody = $('#studentsTableBody');
    studentsData = students;
    
    if (!students || students.length === 0) {
        tbody.html(`
            <tr>
                <td colspan="6" class="text-center py-4">
                    <i class="fas fa-users fa-2x text-muted mb-2"></i>
                    <div class="text-muted">لا يوجد طلاب مسجلون</div>
                </td>
            </tr>
        `);
        return;
    }
    
    let html = '';
    
    students.forEach(student => {
        const statusClass = student.is_active ? 'success' : 'secondary';
        const statusText = student.is_active ? 'نشط' : 'غير نشط';
        const progressClass = student.progress >= 80 ? 'success' : 
                             student.progress >= 50 ? 'warning' : 'danger';
        
        html += `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="student-avatar me-2">
                            ${student.name.charAt(0).toUpperCase()}
                        </div>
                        <div>
                            <div class="fw-bold">${student.name}</div>
                            <small class="text-muted">${student.email}</small>
                        </div>
                    </div>
                </td>
                <td>${student.course_title}</td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="progress me-2" style="width: 60px; height: 6px;">
                            <div class="progress-bar bg-${progressClass}" style="width: ${student.progress}%"></div>
                        </div>
                        <small>${student.progress}%</small>
                    </div>
                </td>
                <td>
                    <small class="text-muted">${student.last_activity || 'لا يوجد'}</small>
                </td>
                <td>
                    <span class="badge bg-${statusClass}">${statusText}</span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewStudentDetails('${student.id}')">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="sendMessage('${student.id}')">
                            <i class="fas fa-envelope"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });
    
    tbody.html(html);
}

function viewStudentDetails(studentId) {
    // عرض تفاصيل الطالب (سيتم تطويرها لاحقاً)
    showAlert('سيتم تطوير هذه الميزة قريباً', 'info');
}

function sendMessage(studentId) {
    // إرسال رسالة للطالب (سيتم تطويرها لاحقاً)
    showAlert('سيتم تطوير هذه الميزة قريباً', 'info');
}

function exportStudentsData() {
    if (!studentsData || studentsData.length === 0) {
        showAlert('لا توجد بيانات للتصدير', 'warning');
        return;
    }
    
    // تحويل البيانات إلى CSV
    const headers = ['الاسم', 'البريد الإلكتروني', 'الكورس', 'التقدم', 'آخر نشاط', 'الحالة'];
    const csvContent = [
        headers.join(','),
        ...studentsData.map(student => [
            student.name,
            student.email,
            student.course_title,
            student.progress + '%',
            student.last_activity || 'لا يوجد',
            student.is_active ? 'نشط' : 'غير نشط'
        ].join(','))
    ].join('\n');
    
    // تحميل الملف
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `students_statistics_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
    
    showAlert('تم تصدير البيانات بنجاح', 'success');
}

function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('.container').first().prepend(alertHtml);
    
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
{% endblock %}
