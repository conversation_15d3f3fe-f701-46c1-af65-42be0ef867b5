{% extends "base.html" %}

{% block title %}لوحة التحليلات الشاملة{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/analytics-dashboard.css') }}">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css">
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- عنوان الصفحة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        لوحة التحليلات الشاملة
                    </h1>
                    <p class="text-muted">تحليلات مفصلة لأداء المنصة والمستخدمين</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" id="refreshDataBtn">
                        <i class="fas fa-sync-alt me-2"></i>
                        تحديث البيانات
                    </button>
                    <button class="btn btn-outline-success" id="exportReportBtn">
                        <i class="fas fa-download me-2"></i>
                        تصدير التقرير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر التحليلات -->
    <div class="analytics-filters mb-4">
        <div class="row g-3">
            <div class="col-md-3">
                <label class="form-label">الفترة الزمنية</label>
                <select class="form-select" id="timeRangeFilter">
                    <option value="7">آخر 7 أيام</option>
                    <option value="30" selected>آخر 30 يوم</option>
                    <option value="90">آخر 3 أشهر</option>
                    <option value="365">آخر سنة</option>
                    <option value="all">جميع الأوقات</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">نوع المستخدم</label>
                <select class="form-select" id="userTypeFilter">
                    <option value="all">جميع المستخدمين</option>
                    <option value="instructor">المدرسين</option>
                    <option value="student">الطلاب</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">التخصص</label>
                <select class="form-select" id="specializationFilter">
                    <option value="all">جميع التخصصات</option>
                    <!-- سيتم تحميل التخصصات ديناميكياً -->
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">المرحلة</label>
                <select class="form-select" id="stageFilter">
                    <option value="all">جميع المراحل</option>
                    <option value="2">المرحلة الثانية</option>
                    <option value="3">المرحلة الثالثة</option>
                    <option value="4">المرحلة الرابعة</option>
                    <option value="general">عام</option>
                </select>
            </div>
        </div>
    </div>

    <!-- الإحصائيات السريعة -->
    <div class="row mb-4" id="quickStats">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card primary">
                <div class="stats-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-number" id="totalUsers">0</div>
                    <div class="stats-label">إجمالي المستخدمين</div>
                    <div class="stats-change" id="usersChange">+0%</div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card success">
                <div class="stats-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-number" id="totalCourses">0</div>
                    <div class="stats-label">إجمالي الكورسات</div>
                    <div class="stats-change" id="coursesChange">+0%</div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card warning">
                <div class="stats-icon">
                    <i class="fas fa-user-graduate"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-number" id="totalEnrollments">0</div>
                    <div class="stats-label">إجمالي التسجيلات</div>
                    <div class="stats-change" id="enrollmentsChange">+0%</div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stats-card info">
                <div class="stats-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-number" id="avgProgress">0%</div>
                    <div class="stats-label">متوسط التقدم</div>
                    <div class="stats-change" id="progressChange">+0%</div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية الرئيسية -->
    <div class="row mb-4">
        <!-- رسم بياني للنمو -->
        <div class="col-lg-8 mb-4">
            <div class="chart-card">
                <div class="chart-header">
                    <h5>نمو المنصة</h5>
                    <div class="chart-controls">
                        <button class="btn btn-sm btn-outline-primary active" data-chart="users">المستخدمين</button>
                        <button class="btn btn-sm btn-outline-primary" data-chart="courses">الكورسات</button>
                        <button class="btn btn-sm btn-outline-primary" data-chart="enrollments">التسجيلات</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="growthChart"></canvas>
                </div>
            </div>
        </div>

        <!-- توزيع المستخدمين -->
        <div class="col-lg-4 mb-4">
            <div class="chart-card">
                <div class="chart-header">
                    <h5>توزيع المستخدمين</h5>
                </div>
                <div class="chart-container">
                    <canvas id="userDistributionChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- تحليلات التخصصات والمراحل -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-4">
            <div class="chart-card">
                <div class="chart-header">
                    <h5>توزيع التخصصات</h5>
                </div>
                <div class="chart-container">
                    <canvas id="specializationsChart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-4">
            <div class="chart-card">
                <div class="chart-header">
                    <h5>توزيع المراحل</h5>
                </div>
                <div class="chart-container">
                    <canvas id="stagesChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- تحليلات الأداء -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="chart-card">
                <div class="chart-header">
                    <h5>تحليل أداء الكورسات</h5>
                    <div class="chart-controls">
                        <button class="btn btn-sm btn-outline-primary active" data-metric="completion">معدل الإكمال</button>
                        <button class="btn btn-sm btn-outline-primary" data-metric="engagement">مستوى التفاعل</button>
                        <button class="btn btn-sm btn-outline-primary" data-metric="progress">التقدم</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="performanceChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- جداول التفاصيل -->
    <div class="row mb-4">
        <!-- أفضل الكورسات -->
        <div class="col-lg-6 mb-4">
            <div class="table-card">
                <div class="table-header">
                    <h5>أفضل الكورسات أداءً</h5>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover" id="topCoursesTable">
                        <thead>
                            <tr>
                                <th>الكورس</th>
                                <th>المدرس</th>
                                <th>التسجيلات</th>
                                <th>معدل الإكمال</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم تحميل البيانات ديناميكياً -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- أفضل المدرسين -->
        <div class="col-lg-6 mb-4">
            <div class="table-card">
                <div class="table-header">
                    <h5>أفضل المدرسين أداءً</h5>
                </div>
                <div class="table-responsive">
                    <table class="table table-hover" id="topInstructorsTable">
                        <thead>
                            <tr>
                                <th>المدرس</th>
                                <th>التخصص</th>
                                <th>الكورسات</th>
                                <th>الطلاب</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم تحميل البيانات ديناميكياً -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- تحليلات متقدمة -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="advanced-analytics">
                <div class="analytics-tabs">
                    <ul class="nav nav-tabs" id="analyticsTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="activity-tab" data-bs-toggle="tab" data-bs-target="#activity" type="button" role="tab">
                                <i class="fas fa-chart-bar me-2"></i>
                                نشاط المنصة
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="engagement-tab" data-bs-toggle="tab" data-bs-target="#engagement" type="button" role="tab">
                                <i class="fas fa-users me-2"></i>
                                تفاعل المستخدمين
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="content-tab" data-bs-toggle="tab" data-bs-target="#content" type="button" role="tab">
                                <i class="fas fa-book me-2"></i>
                                تحليل المحتوى
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="trends-tab" data-bs-toggle="tab" data-bs-target="#trends" type="button" role="tab">
                                <i class="fas fa-trending-up me-2"></i>
                                الاتجاهات
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content" id="analyticsTabContent">
                        <div class="tab-pane fade show active" id="activity" role="tabpanel">
                            <div class="row">
                                <div class="col-lg-8">
                                    <canvas id="activityChart"></canvas>
                                </div>
                                <div class="col-lg-4">
                                    <div id="activityStats"></div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="engagement" role="tabpanel">
                            <div class="row">
                                <div class="col-lg-6">
                                    <canvas id="engagementChart"></canvas>
                                </div>
                                <div class="col-lg-6">
                                    <canvas id="retentionChart"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="content" role="tabpanel">
                            <div class="row">
                                <div class="col-lg-8">
                                    <canvas id="contentChart"></canvas>
                                </div>
                                <div class="col-lg-4">
                                    <div id="contentInsights"></div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="trends" role="tabpanel">
                            <div class="row">
                                <div class="col-12">
                                    <canvas id="trendsChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay" style="display: none;">
    <div class="loading-spinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
        <p class="mt-3">جاري تحميل البيانات...</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
<script src="{{ url_for('static', filename='js/analytics-dashboard.js') }}"></script>
<script>
$(document).ready(function() {
    // تهيئة لوحة التحليلات
    const analyticsManager = new AnalyticsDashboard({
        apiEndpoint: '/api/analytics',
        refreshInterval: 300000, // 5 دقائق
        autoRefresh: true
    });

    // تحميل البيانات الأولية
    analyticsManager.initialize();

    // ربط الأحداث
    $('#timeRangeFilter, #userTypeFilter, #specializationFilter, #stageFilter').on('change', function() {
        analyticsManager.updateFilters();
    });

    $('#refreshDataBtn').on('click', function() {
        analyticsManager.refreshData();
    });

    $('#exportReportBtn').on('click', function() {
        analyticsManager.exportReport();
    });
});
</script>
{% endblock %}
