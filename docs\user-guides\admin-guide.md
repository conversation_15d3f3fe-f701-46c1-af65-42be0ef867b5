# 👑 دليل مدير النظام (الأدمن)
## Admin User Guide

دليل شامل لمدير النظام لإدارة منصة الكورسات التعليمية بكفاءة وفعالية.

---

## 🎯 نظرة عامة على دور الأدمن

كمدير للنظام، لديك الصلاحيات الكاملة لإدارة جميع جوانب المنصة:
- إدارة المستخدمين (مدرسين وطلاب)
- إدارة التخصصات والمراحل
- مراقبة النظام والإحصائيات
- إعدادات الأمان والحماية
- إدارة المحتوى والكورسات

---

## 🚪 تسجيل الدخول والوصول

### 1. تسجيل الدخول
```
URL: http://localhost:5000/login
البريد الإلكتروني: <EMAIL>
كلمة المرور: [كما تم إعدادها عند التثبيت]
```

### 2. الوصول للوحة التحكم
بعد تسجيل الدخول، ستجد:
- **لوحة التحكم الرئيسية**: `/admin`
- **إدارة المستخدمين**: `/admin/users`
- **إدارة التخصصات**: `/admin/specializations`
- **لوحة التحليلات**: `/analytics`

---

## 👥 إدارة المستخدمين

### إدارة المدرسين

#### 1. عرض قائمة المدرسين
- اذهب إلى **لوحة التحكم > إدارة المدرسين**
- ستجد قائمة بجميع المدرسين مع:
  - الاسم والبريد الإلكتروني
  - التخصصات المخصصة
  - حالة الحساب (نشط/معطل)
  - تاريخ الإنشاء

#### 2. إضافة مدرس جديد
**الطريقة الأولى: عبر بوت التليجرام**
1. اذهب إلى البوت في تليجرام
2. أرسل `/create_teacher`
3. أدخل اسم المدرس
4. اختر التخصص والصلاحيات
5. سيتم إنشاء رابط خاص للمدرس

**الطريقة الثانية: عبر لوحة التحكم**
1. انقر على "إضافة مدرس جديد"
2. املأ البيانات المطلوبة:
   - الاسم الكامل
   - البريد الإلكتروني
   - التخصص
   - نوع الصلاحيات
3. انقر "حفظ"

#### 3. تعديل صلاحيات المدرس
1. انقر على "تعديل" بجانب اسم المدرس
2. اختر نوع الصلاحيات:
   - **جميع المراحل**: الوصول لجميع مراحل التخصص
   - **مراحل محددة**: اختيار مراحل معينة
   - **كورسات عامة**: إنشاء كورسات غير مرتبطة بتخصص
3. احفظ التغييرات

### إدارة الطلاب

#### 1. عرض قائمة الطلاب
- اذهب إلى **لوحة التحكم > إدارة الطلاب**
- ستجد معلومات شاملة عن كل طالب:
  - البيانات الشخصية
  - الكورسات المفعلة
  - تقدم التعلم
  - آخر نشاط

#### 2. إدارة حسابات الطلاب
- **تعديل البيانات**: تغيير الاسم أو البريد الإلكتروني
- **إعادة تعيين كلمة المرور**: إنشاء كلمة مرور جديدة
- **حظر/إلغاء حظر**: تعطيل أو تفعيل الحساب
- **حذف الحساب**: حذف نهائي (احذر!)

---

## 🎓 إدارة التخصصات والمراحل

### 1. عرض التخصصات الحالية
- اذهب إلى **إدارة التخصصات**
- ستجد التخصصات الثلاثة:
  - 🔬 التحليل الطبي
  - 📡 الأشعة
  - 💉 التخدير

### 2. إدارة المراحل
لكل تخصص، يمكنك:
- **عرض المراحل**: المرحلة 2، 3، 4
- **تعديل أوصاف المراحل**
- **إضافة مراحل جديدة** (إذا لزم الأمر)
- **تعيين أيقونات مخصصة**

### 3. إدارة الأيقونات
```bash
# إضافة أيقونة جديدة
1. ارفع الأيقونة إلى مجلد static/icons/
2. اذهب إلى إدارة التخصصات
3. انقر "تعديل الأيقونة"
4. اختر الأيقونة الجديدة
5. احفظ التغييرات
```

---

## 📚 إدارة الكورسات

### 1. مراقبة جميع الكورسات
- **عرض شامل**: جميع الكورسات في النظام
- **التصفية**: حسب التخصص، المدرس، الحالة
- **الإحصائيات**: عدد الطلاب، معدل الإكمال
- **الإدارة**: تعديل، حذف، تعطيل

### 2. إدارة أكواد التفعيل
- **عرض الأكواد النشطة**: جميع أكواد التفعيل
- **مراقبة الاستخدام**: عدد مرات الاستخدام
- **إدارة انتهاء الصلاحية**: تمديد أو إنهاء الأكواد
- **إنشاء أكواد شاملة**: أكواد للوصول لجميع الكورسات

### 3. إدارة المحتوى
- **مراجعة المحتوى**: فحص المحتوى المرفوع
- **الموافقة/الرفض**: الموافقة على المحتوى الجديد
- **إدارة الملفات**: حذف أو تعديل الملفات
- **النسخ الاحتياطية**: إنشاء نسخ احتياطية للمحتوى

---

## 📊 لوحة التحليلات والإحصائيات

### 1. الإحصائيات العامة
- **عدد المستخدمين**: مدرسين، طلاب، إجمالي
- **عدد الكورسات**: نشطة، معطلة، إجمالي
- **النشاط اليومي**: تسجيلات دخول، تفعيلات
- **معدلات الاستخدام**: أكثر الكورسات شعبية

### 2. تحليلات متقدمة
- **رسوم بيانية تفاعلية**: Chart.js مع تحديث مباشر
- **تقارير مخصصة**: حسب الفترة الزمنية
- **تحليل الأداء**: أوقات الاستجابة، الأخطاء
- **تحليل المحتوى**: أكثر الفيديوهات مشاهدة

### 3. التقارير والتصدير
- **تقارير دورية**: يومية، أسبوعية، شهرية
- **تصدير البيانات**: CSV، Excel، PDF
- **إشعارات تلقائية**: تنبيهات للأحداث المهمة
- **لوحة معلومات مباشرة**: تحديث فوري للإحصائيات

---

## 🔐 إدارة الأمان والحماية

### 1. مراقبة النشاط
- **سجل العمليات**: جميع العمليات في النظام
- **النشاط المشبوه**: محاولات دخول غير مصرحة
- **تقارير أمنية**: تقارير دورية عن الأمان
- **تنبيهات فورية**: إشعارات للأحداث الأمنية

### 2. إدارة الجلسات
- **الجلسات النشطة**: عرض جميع الجلسات
- **إنهاء الجلسات**: إنهاء جلسات محددة
- **إعدادات انتهاء الصلاحية**: تحديد مدة الجلسات
- **مراقبة تعدد الجلسات**: منع تسجيل دخول متعدد

### 3. النسخ الاحتياطية
- **نسخ تلقائية**: إعداد نسخ احتياطية دورية
- **استعادة البيانات**: استعادة من النسخ الاحتياطية
- **تشفير النسخ**: حماية النسخ الاحتياطية
- **مراقبة المساحة**: إدارة مساحة التخزين

---

## 🤖 إدارة بوت التليجرام

### 1. مراقبة البوت
- **حالة البوت**: التأكد من عمل البوت
- **سجل العمليات**: عمليات البوت في `bot.log`
- **الإحصائيات**: عدد الرسائل، المستخدمين
- **الأخطاء**: مراقبة وحل الأخطاء

### 2. إدارة الأوامر
- **أوامر الأدمن**: جميع أوامر إدارة النظام
- **إنشاء المدرسين**: `/create_teacher`
- **إدارة التخصصات**: `/manage_specializations`
- **الإحصائيات**: `/stats`

### 3. إعدادات البوت
- **رسائل الترحيب**: تخصيص رسائل البوت
- **القوائم والأزرار**: تعديل واجهة البوت
- **الإشعارات**: إعداد إشعارات تلقائية
- **اللغة والنصوص**: تعديل نصوص البوت

---

## ⚙️ الإعدادات المتقدمة

### 1. إعدادات النظام
- **معلومات المنصة**: الاسم، الوصف، الشعار
- **إعدادات البريد الإلكتروني**: SMTP للإشعارات
- **إعدادات التخزين**: Firebase Storage
- **إعدادات الأداء**: Cache، Compression

### 2. إعدادات المستخدمين
- **سياسات كلمات المرور**: قوة، انتهاء صلاحية
- **إعدادات التسجيل**: متطلبات إنشاء الحسابات
- **حدود الاستخدام**: عدد الكورسات، مدة المشاهدة
- **إعدادات الخصوصية**: مشاركة البيانات

### 3. إعدادات المحتوى
- **أنواع الملفات المسموحة**: فيديو، صور، مستندات
- **حدود الحجم**: حد أقصى لحجم الملفات
- **جودة الفيديو**: إعدادات ضغط وجودة
- **حماية المحتوى**: إعدادات الأمان

---

## 🚨 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. مشاكل تسجيل الدخول
```bash
# فحص حساب الأدمن
python check_user.py <EMAIL>

# إعادة تعيين كلمة مرور الأدمن
python create_admin_simple.py
```

#### 2. مشاكل البوت
```bash
# فحص سجل البوت
tail -f bot.log

# إعادة تشغيل البوت
python bot/main.py
```

#### 3. مشاكل قاعدة البيانات
```bash
# فحص الاتصال
python check_database.py

# فحص Firebase
python test_firebase.py
```

---

## 📋 قائمة مهام الأدمن اليومية

### مهام يومية
- [ ] فحص لوحة التحليلات
- [ ] مراجعة سجل العمليات
- [ ] فحص حالة البوت
- [ ] مراقبة النشاط المشبوه
- [ ] الرد على استفسارات المستخدمين

### مهام أسبوعية
- [ ] مراجعة تقارير الأداء
- [ ] فحص النسخ الاحتياطية
- [ ] تحديث إعدادات الأمان
- [ ] مراجعة المحتوى الجديد
- [ ] تحليل إحصائيات الاستخدام

### مهام شهرية
- [ ] تقييم أداء النظام
- [ ] تحديث الوثائق
- [ ] مراجعة صلاحيات المستخدمين
- [ ] تخطيط التحسينات
- [ ] إعداد التقارير الشهرية

---

## 📞 الحصول على المساعدة

### مصادر الدعم
- **الوثائق التقنية**: راجع `docs/technical/`
- **سجلات النظام**: فحص `logs/` و `bot.log`
- **أدوات التشخيص**: استخدم سكريبتات `check_*.py`
- **اختبارات النظام**: تشغيل `python run_tests.py`

### جهات الاتصال
- **الدعم التقني**: <EMAIL>
- **تطوير النظام**: <EMAIL>
- **الطوارئ**: <EMAIL>

---

**آخر تحديث:** 2025-07-03  
**الإصدار:** 1.0.0  
**حالة الدليل:** مكتمل ✅
