/**
 * إدارة نموذج إنشاء الكورس للمدرسين
 */

class CourseCreator {
    constructor() {
        this.permissions = null;
        this.specializations = [];
        this.form = document.getElementById('createCourseForm');
        this.loadingSpinner = document.getElementById('loadingSpinner');
        this.errorContainer = document.getElementById('errorContainer');
        
        this.init();
    }
    
    async init() {
        try {
            // تحميل البيانات الأولية
            await this.loadPermissions();
            await this.loadSpecializations();
            
            // إعداد الأحداث
            this.setupEventListeners();
            
            // تحديث الواجهة
            this.updateUI();
            
        } catch (error) {
            console.error('خطأ في تهيئة منشئ الكورس:', error);
            this.showError('حدث خطأ في تحميل البيانات');
        }
    }
    
    async loadPermissions() {
        try {
            const response = await fetchWithAuth('/api/instructor/course-options');
            
            if (response.success) {
                this.permissions = response.data;
                this.displayPermissions();
            } else {
                throw new Error(response.message || 'فشل في تحميل الصلاحيات');
            }
            
        } catch (error) {
            console.error('خطأ في تحميل الصلاحيات:', error);
            throw error;
        }
    }
    
    async loadSpecializations() {
        try {
            const response = await fetchWithAuth('/api/specializations');
            
            if (response.success) {
                this.specializations = response.specializations || [];
                this.populateSpecializationsSelect();
            } else {
                throw new Error(response.message || 'فشل في تحميل التخصصات');
            }
            
        } catch (error) {
            console.error('خطأ في تحميل التخصصات:', error);
            throw error;
        }
    }
    
    displayPermissions() {
        const container = document.getElementById('permissionsContent');
        
        if (!this.permissions.can_create) {
            container.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${this.permissions.reason || 'ليس لديك صلاحية إنشاء الكورسات'}
                </div>
            `;
            this.form.style.display = 'none';
            return;
        }
        
        let html = '<ul class="permissions-list">';
        
        // المراحل المسموحة
        if (this.permissions.allowed_stages && this.permissions.allowed_stages.length > 0) {
            html += `<li><strong>المراحل المسموحة:</strong> ${this.permissions.allowed_stages.join(', ')}</li>`;
        }
        
        // التخصص
        if (this.permissions.instructor_specialization_name) {
            html += `<li><strong>تخصصك:</strong> ${this.permissions.instructor_specialization_name}</li>`;
        }
        
        // الكورسات العامة
        if (this.permissions.can_create_general) {
            html += '<li><strong>يمكنك إنشاء كورسات عامة</strong></li>';
        }
        
        html += '</ul>';
        container.innerHTML = html;
    }
    
    populateSpecializationsSelect() {
        const select = document.getElementById('courseSpecialization');
        select.innerHTML = '<option value="">اختر التخصص</option>';
        
        this.specializations.forEach(spec => {
            const option = document.createElement('option');
            option.value = spec.id;
            option.textContent = spec.name;
            
            // تحديد التخصص الافتراضي للمدرس
            if (this.permissions && spec.id === this.permissions.instructor_specialization) {
                option.selected = true;
            }
            
            select.appendChild(option);
        });
    }
    
    updateUI() {
        if (!this.permissions || !this.permissions.can_create) {
            return;
        }
        
        // تحديث خيارات المراحل
        this.updateStagesSelect();
        
        // تحديث خيارات نوع الكورس
        this.updateCourseTypeSelect();
        
        // إعداد التخصص الافتراضي
        this.setupDefaultSpecialization();
    }
    
    updateStagesSelect() {
        const select = document.getElementById('courseStage');
        select.innerHTML = '<option value="">اختر المرحلة</option>';
        
        if (this.permissions.allowed_stages) {
            this.permissions.allowed_stages.forEach(stage => {
                const option = document.createElement('option');
                option.value = stage;
                option.textContent = `المرحلة ${stage}`;
                select.appendChild(option);
            });
        }
    }
    
    updateCourseTypeSelect() {
        const generalOption = document.querySelector('#courseType option[value="general"]');
        
        if (this.permissions.can_create_general) {
            generalOption.style.display = 'block';
        } else {
            generalOption.style.display = 'none';
        }
    }
    
    setupDefaultSpecialization() {
        const typeSelect = document.getElementById('courseType');
        const specializationGroup = document.getElementById('specializationGroup');
        const specializationSelect = document.getElementById('courseSpecialization');
        
        // إعداد التخصص الافتراضي
        if (this.permissions.instructor_specialization) {
            specializationSelect.value = this.permissions.instructor_specialization;
        }
    }
    
    setupEventListeners() {
        // تغيير نوع الكورس
        document.getElementById('courseType').addEventListener('change', (e) => {
            this.handleCourseTypeChange(e.target.value);
        });
        
        // إرسال النموذج
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSubmit();
        });
    }
    
    handleCourseTypeChange(type) {
        const specializationGroup = document.getElementById('specializationGroup');
        const specializationSelect = document.getElementById('courseSpecialization');
        
        if (type === 'general') {
            specializationGroup.style.display = 'none';
            specializationSelect.removeAttribute('required');
            specializationSelect.value = '';
        } else {
            specializationGroup.style.display = 'block';
            specializationSelect.setAttribute('required', 'required');
            
            // إعادة تعيين التخصص الافتراضي
            if (this.permissions.instructor_specialization) {
                specializationSelect.value = this.permissions.instructor_specialization;
            }
        }
    }
    
    async handleSubmit() {
        try {
            this.showLoading(true);
            this.clearErrors();
            
            // جمع بيانات النموذج
            const formData = new FormData(this.form);
            const courseData = {
                title: formData.get('title'),
                description: formData.get('description'),
                stage: parseInt(formData.get('stage')),
                status: formData.get('status'),
                is_general: formData.get('type') === 'general'
            };
            
            // إضافة التخصص إذا لم يكن كورساً عاماً
            if (!courseData.is_general) {
                courseData.specialization_id = formData.get('specialization_id');
            }
            
            // التحقق من صحة البيانات
            const validation = this.validateCourseData(courseData);
            if (!validation.valid) {
                this.showError(validation.message);
                return;
            }
            
            // إرسال البيانات
            const response = await fetchWithAuth('/api/instructor/courses', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(courseData)
            });
            
            if (response.success) {
                showSuccess('تم إنشاء الكورس بنجاح!');
                
                // إعادة توجيه بعد ثانيتين
                setTimeout(() => {
                    window.location.href = '/instructor/courses';
                }, 2000);
            } else {
                this.showError(response.message || 'فشل في إنشاء الكورس');
            }
            
        } catch (error) {
            console.error('خطأ في إنشاء الكورس:', error);
            this.showError('حدث خطأ في إنشاء الكورس');
        } finally {
            this.showLoading(false);
        }
    }
    
    validateCourseData(data) {
        if (!data.title || data.title.trim().length < 3) {
            return { valid: false, message: 'عنوان الكورس يجب أن يكون 3 أحرف على الأقل' };
        }
        
        if (!data.stage || !this.permissions.allowed_stages.includes(data.stage)) {
            return { valid: false, message: 'المرحلة المختارة غير مسموحة' };
        }
        
        if (!data.is_general && !data.specialization_id) {
            return { valid: false, message: 'يجب اختيار التخصص للكورسات غير العامة' };
        }
        
        if (data.is_general && !this.permissions.can_create_general) {
            return { valid: false, message: 'ليس لديك صلاحية إنشاء كورسات عامة' };
        }
        
        return { valid: true };
    }
    
    showLoading(show) {
        if (show) {
            this.form.style.display = 'none';
            this.loadingSpinner.style.display = 'block';
        } else {
            this.form.style.display = 'block';
            this.loadingSpinner.style.display = 'none';
        }
    }
    
    showError(message) {
        this.errorContainer.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `;
    }
    
    clearErrors() {
        this.errorContainer.innerHTML = '';
    }
}

// تهيئة منشئ الكورس عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    new CourseCreator();
});
