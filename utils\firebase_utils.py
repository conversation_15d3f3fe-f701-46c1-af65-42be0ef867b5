"""
وظائف Firebase للتعامل مع قاعدة البيانات
Firebase utilities for database operations
"""

import os
import json
import logging
import time
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timezone
from functools import wraps, lru_cache

try:
    import firebase_admin
    from firebase_admin import credentials, db, auth
except ImportError:
    firebase_admin = None
    print("تحذير: مكتبة firebase-admin غير مثبتة")

logger = logging.getLogger(__name__)

# ديكوريتر للتخزين المؤقت مع انتهاء صلاحية
def cache_with_expiry(expiry_seconds=300):
    """ديكوريتر للتخزين المؤقت مع انتهاء صلاحية"""
    def decorator(func):
        cache = {}

        @wraps(func)
        def wrapper(*args, **kwargs):
            # إنشاء مفتاح التخزين المؤقت
            cache_key = str(args) + str(sorted(kwargs.items()))
            current_time = time.time()

            # التحقق من وجود البيانات في التخزين المؤقت
            if cache_key in cache:
                cached_data, timestamp = cache[cache_key]
                if current_time - timestamp < expiry_seconds:
                    return cached_data
                else:
                    # إزالة البيانات المنتهية الصلاحية
                    del cache[cache_key]

            # تنفيذ الدالة وحفظ النتيجة
            result = func(*args, **kwargs)
            cache[cache_key] = (result, current_time)

            # تنظيف التخزين المؤقت إذا أصبح كبيراً
            if len(cache) > 100:
                # إزالة أقدم 50 عنصر
                oldest_keys = sorted(cache.keys(), key=lambda k: cache[k][1])[:50]
                for key in oldest_keys:
                    del cache[key]

            return result

        # إضافة دالة لمسح التخزين المؤقت
        wrapper.clear_cache = lambda: cache.clear()
        return wrapper
    return decorator

class FirebaseManager:
    """مدير Firebase لإدارة الاتصال وعمليات قاعدة البيانات"""
    
    def __init__(self):
        self.app = None
        self.database = None
        self._initialized = False
    
    def initialize(self, config: Dict[str, Any]) -> bool:
        """
        تهيئة Firebase مع الإعدادات المطلوبة
        Initialize Firebase with required configuration
        """
        try:
            if firebase_admin is None:
                logger.error("مكتبة firebase-admin غير متوفرة")
                return False
            
            # التحقق من وجود التطبيق مسبقاً
            if self._initialized:
                logger.info("Firebase مهيأ مسبقاً")
                return True
            
            # إنشاء بيانات الاعتماد
            firebase_config = config.get('FIREBASE_CONFIG', {})
            database_url = config.get('FIREBASE_DATABASE_URL')
            
            if not firebase_config or not database_url:
                logger.error("إعدادات Firebase غير مكتملة")
                return False
            
            # تهيئة Firebase
            cred = credentials.Certificate(firebase_config)
            self.app = firebase_admin.initialize_app(cred, {
                'databaseURL': database_url
            })
            
            # الحصول على مرجع قاعدة البيانات
            self.database = db.reference()
            self._initialized = True
            
            logger.info("تم تهيئة Firebase بنجاح")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة Firebase: {e}")
            return False
    
    def is_connected(self) -> bool:
        """التحقق من حالة الاتصال"""
        try:
            if not self._initialized or not self.database:
                return False
            
            # اختبار بسيط للاتصال
            self.database.child('health_check').set({
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'status': 'connected'
            })
            return True
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من الاتصال: {e}")
            return False
    
    def create_user(self, user_data: Dict[str, Any]) -> Optional[str]:
        """
        إنشاء مستخدم جديد في قاعدة البيانات
        Create a new user in the database
        """
        try:
            if not self._initialized:
                logger.error("Firebase غير مهيأ")
                return None
            
            # إضافة timestamp
            user_data['created_at'] = datetime.now(timezone.utc).isoformat()
            user_data['updated_at'] = user_data['created_at']
            
            # إنشاء المستخدم
            user_ref = self.database.child('users').push(user_data)
            user_id = user_ref.key
            
            logger.info(f"تم إنشاء مستخدم جديد: {user_id}")
            return user_id
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء المستخدم: {e}")
            return None
    
    def get_user(self, user_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على بيانات مستخدم"""
        try:
            if not self._initialized:
                return None
            
            user_data = self.database.child('users').child(user_id).get()
            return user_data if user_data else None
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على المستخدم: {e}")
            return None
    
    def update_user(self, user_id: str, update_data: Dict[str, Any]) -> bool:
        """تحديث بيانات مستخدم"""
        try:
            if not self._initialized:
                return False
            
            # إضافة timestamp التحديث
            update_data['updated_at'] = datetime.now(timezone.utc).isoformat()
            
            self.database.child('users').child(user_id).update(update_data)
            logger.info(f"تم تحديث المستخدم: {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تحديث المستخدم: {e}")
            return False
    
    def get_user_by_telegram_id(self, telegram_id: str) -> Optional[Dict[str, Any]]:
        """البحث عن مستخدم بواسطة معرف التليجرام"""
        try:
            if not self._initialized:
                return None

            # محاولة البحث باستخدام الفهارس أولاً
            try:
                users = self.database.child('users').order_by_child('telegram_id').equal_to(telegram_id).get()
                if users:
                    for user_id, user_data in users.items():
                        user_data['user_id'] = user_id
                        return user_data
            except Exception as index_error:
                logger.warning(f"فشل البحث بالفهارس، سيتم البحث اليدوي: {index_error}")

                # البحث اليدوي في جميع المستخدمين
                all_users = self.database.child('users').get()
                if all_users:
                    for user_id, user_data in all_users.items():
                        if user_data and user_data.get('telegram_id') == telegram_id:
                            user_data['user_id'] = user_id
                            logger.info(f"تم العثور على المستخدم بالبحث اليدوي: {telegram_id}")
                            return user_data

            return None

        except Exception as e:
            logger.error(f"خطأ في البحث عن المستخدم: {e}")
            return None

    def get_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """البحث عن مستخدم بواسطة البريد الإلكتروني"""
        try:
            if not self._initialized:
                return None

            # محاولة البحث باستخدام الفهارس أولاً
            try:
                users = self.database.child('users').order_by_child('email').equal_to(email).get()
                if users:
                    for user_id, user_data in users.items():
                        user_data['user_id'] = user_id
                        return user_data
            except Exception as index_error:
                logger.warning(f"فشل البحث بالفهارس، سيتم البحث اليدوي: {index_error}")

                # البحث اليدوي في جميع المستخدمين
                all_users = self.database.child('users').get()
                if all_users:
                    for user_id, user_data in all_users.items():
                        if user_data and user_data.get('email') == email:
                            user_data['user_id'] = user_id
                            logger.info(f"تم العثور على المستخدم بالبحث اليدوي: {email}")
                            return user_data

            return None

        except Exception as e:
            logger.error(f"خطأ في البحث عن المستخدم بالإيميل: {e}")
            return None
    
    def create_specialization(self, spec_data: Dict[str, Any]) -> Optional[str]:
        """إنشاء تخصص جديد"""
        try:
            if not self._initialized:
                return None
            
            spec_data['created_at'] = datetime.now(timezone.utc).isoformat()
            spec_ref = self.database.child('specializations').push(spec_data)
            
            logger.info(f"تم إنشاء تخصص جديد: {spec_ref.key}")
            return spec_ref.key
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء التخصص: {e}")
            return None
    
    def get_all_specializations(self) -> List[Dict[str, Any]]:
        """الحصول على جميع التخصصات"""
        try:
            if not self._initialized:
                return []

            specs = self.database.child('specializations').get()
            if not specs:
                return []

            result = []
            for spec_id, spec_data in specs.items():
                spec_data['id'] = spec_id
                result.append(spec_data)

            return result

        except Exception as e:
            logger.error(f"خطأ في الحصول على التخصصات: {e}")
            return []

    def get_specialization(self, specialization_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على تخصص محدد بالمعرف"""
        try:
            if not self._initialized:
                return None

            if not specialization_id:
                return None

            spec_data = self.database.child('specializations').child(specialization_id).get()
            if spec_data:
                spec_data['id'] = specialization_id
                return spec_data

            return None

        except Exception as e:
            logger.error(f"خطأ في الحصول على التخصص {specialization_id}: {e}")
            return None
    
    def create_course(self, course_data: Dict[str, Any]) -> Optional[str]:
        """إنشاء كورس جديد"""
        try:
            if not self._initialized:
                return None
            
            course_data['created_at'] = datetime.now(timezone.utc).isoformat()
            course_data['updated_at'] = course_data['created_at']
            
            course_ref = self.database.child('courses').push(course_data)
            
            logger.info(f"تم إنشاء كورس جديد: {course_ref.key}")
            return course_ref.key
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء الكورس: {e}")
            return None
    
    @cache_with_expiry(expiry_seconds=300)  # تخزين مؤقت لمدة 5 دقائق
    def get_courses_by_instructor(self, instructor_id: str) -> List[Dict[str, Any]]:
        """الحصول على كورسات مدرس معين"""
        try:
            if not self._initialized:
                return []

            # جلب جميع الكورسات وفلترتها محلياً لتجنب مشكلة الفهرس
            all_courses = self.database.child('courses').get()

            if not all_courses:
                return []

            result = []
            for course_id, course_data in all_courses.items():
                # فلترة الكورسات حسب معرف المدرس
                if course_data.get('instructor_id') == instructor_id:
                    course_data['id'] = course_id
                    result.append(course_data)

            return result

        except Exception as e:
            logger.error(f"خطأ في الحصول على كورسات المدرس: {e}")
            return []

    def get_all_instructors(self) -> List[Dict[str, Any]]:
        """الحصول على جميع المدرسين مع تفاصيلهم وصلاحياتهم"""
        try:
            if not self._initialized:
                return []

            # جلب جميع المستخدمين وفلترة المدرسين محلياً لتجنب مشكلة الفهرس
            all_users = self.get_all_users()

            if not all_users:
                return []

            instructors = []
            for user_data in all_users:
                # فلترة المدرسين فقط
                if user_data.get('role') != 'instructor':
                    continue

                # الحصول على تفاصيل التخصص
                specialization_id = user_data.get('specialization_id')
                if specialization_id:
                    specialization = self.get_specialization(specialization_id)
                    user_data['specialization_name'] = specialization.get('name', 'غير محدد') if specialization else 'غير محدد'
                else:
                    user_data['specialization_name'] = 'غير محدد'

                # الحصول على عدد الكورسات
                user_id = user_data.get('id')
                if user_id:
                    courses = self.get_courses_by_instructor(user_id)
                    user_data['courses_count'] = len(courses)

                    # الحصول على عدد الطلاب
                    students = self.get_students_by_instructor(user_id)
                    user_data['students_count'] = len(students)
                else:
                    user_data['courses_count'] = 0
                    user_data['students_count'] = 0

                # التأكد من وجود صلاحيات افتراضية
                if 'permissions' not in user_data:
                    user_data['permissions'] = {
                        'can_create_courses': True,
                        'can_manage_students': True,
                        'allowed_stages': [2, 3, 4],
                        'can_create_general_courses': False
                    }

                instructors.append(user_data)

            # ترتيب المدرسين حسب تاريخ الإنشاء
            instructors.sort(key=lambda x: x.get('created_at', ''), reverse=True)

            return instructors

        except Exception as e:
            logger.error(f"خطأ في الحصول على المدرسين: {e}")
            return []

    def get_students_by_instructor(self, instructor_id: str) -> List[Dict[str, Any]]:
        """الحصول على الطلاب التابعين لمدرس معين"""
        try:
            if not self._initialized:
                return []

            users = self.database.child('users').order_by_child('role').equal_to('student').get()

            if not users:
                return []

            students = []
            for user_id, user_data in users.items():
                if user_data.get('assigned_instructor') == instructor_id:
                    user_data['id'] = user_id

                    # إضافة معلومات التخصص
                    if user_data.get('specialization_id'):
                        specialization = self.get_specialization(user_data['specialization_id'])
                        user_data['specialization_name'] = specialization.get('name', 'غير محدد') if specialization else 'غير محدد'
                    else:
                        user_data['specialization_name'] = 'غير محدد'

                    # إضافة إحصائيات الطالب
                    enrollments = self.get_student_enrollments(user_id)
                    user_data['enrollments_count'] = len(enrollments)
                    user_data['active_courses'] = len([e for e in enrollments if e.get('status') == 'active'])

                    students.append(user_data)

            # ترتيب الطلاب حسب تاريخ الإنشاء
            students.sort(key=lambda x: x.get('created_at', ''), reverse=True)
            return students

        except Exception as e:
            logger.error(f"خطأ في الحصول على طلاب المدرس: {e}")
            return []

    def get_all_students(self) -> List[Dict[str, Any]]:
        """الحصول على جميع الطلاب مع معلومات المدرسين المسؤولين عنهم"""
        try:
            if not self._initialized:
                return []

            # جلب جميع المستخدمين ثم فلترة الطلاب محلياً
            # لتجنب مشكلة Firebase index
            all_users = self.database.child('users').get()

            if not all_users:
                return []

            students = []
            for user_id, user_data in all_users.items():
                # فلترة الطلاب فقط
                if user_data.get('role') != 'student':
                    continue

                user_data['id'] = user_id

                # إضافة معلومات التخصص
                if user_data.get('specialization_id'):
                    specialization = self.get_specialization(user_data['specialization_id'])
                    user_data['specialization_name'] = specialization.get('name', 'غير محدد') if specialization else 'غير محدد'
                else:
                    user_data['specialization_name'] = 'غير محدد'

                # إضافة معلومات المدرس المسؤول
                if user_data.get('assigned_instructor'):
                    instructor = self.get_user(user_data['assigned_instructor'])
                    if instructor:
                        user_data['instructor_name'] = instructor.get('full_name', 'غير محدد')
                        user_data['instructor_email'] = instructor.get('email', '')
                    else:
                        user_data['instructor_name'] = 'مدرس محذوف'
                        user_data['instructor_email'] = ''
                else:
                    user_data['instructor_name'] = 'غير مرتبط'
                    user_data['instructor_email'] = ''

                # إضافة إحصائيات الطالب
                enrollments = self.get_student_enrollments(user_id)
                user_data['enrollments_count'] = len(enrollments)
                user_data['active_courses'] = len([e for e in enrollments if e.get('status') == 'active'])

                students.append(user_data)

            # ترتيب الطلاب حسب تاريخ الإنشاء
            students.sort(key=lambda x: x.get('created_at', ''), reverse=True)
            return students

        except Exception as e:
            logger.error(f"خطأ في الحصول على جميع الطلاب: {e}")
            return []

    def get_student_enrollments(self, student_id: str) -> List[Dict[str, Any]]:
        """الحصول على تسجيلات طالب معين"""
        try:
            if not self._initialized:
                return []

            enrollments = self.database.child('enrollments').order_by_child('student_id').equal_to(student_id).get()

            if not enrollments:
                return []

            result = []
            for enrollment_id, enrollment_data in enrollments.items():
                enrollment_data['id'] = enrollment_id

                # إضافة معلومات الكورس
                if enrollment_data.get('course_id'):
                    course = self.get_course(enrollment_data['course_id'])
                    if course:
                        enrollment_data['course_name'] = course.get('title', 'غير محدد')
                        enrollment_data['course_instructor'] = course.get('instructor_name', 'غير محدد')

                result.append(enrollment_data)

            return result

        except Exception as e:
            logger.error(f"خطأ في الحصول على تسجيلات الطالب: {e}")
            return []

    def update_lesson_progress(self, student_id: str, course_id: str, lesson_id: str, progress_data: Dict[str, Any]) -> bool:
        """
        تحديث تقدم الطالب في درس معين
        Update student progress in a specific lesson
        """
        try:
            if not self._initialized:
                logger.error("Firebase غير مهيأ")
                return False

            # البحث عن التسجيل
            enrollments = self.database.child('enrollments').order_by_child('student_id').equal_to(student_id).get()

            if not enrollments:
                return False

            enrollment_id = None
            enrollment_data = None

            for eid, edata in enrollments.items():
                if edata.get('course_id') == course_id:
                    enrollment_id = eid
                    enrollment_data = edata
                    break

            if not enrollment_id:
                return False

            # تحديث بيانات التقدم
            current_progress = enrollment_data.get('progress', {})
            completed_lessons = current_progress.get('completed_lessons', [])

            # إضافة الدرس للدروس المكتملة إذا لم يكن موجوداً
            if lesson_id not in completed_lessons and progress_data.get('completed', False):
                completed_lessons.append(lesson_id)

            # حساب نسبة الإكمال
            course_lessons = self.get_course_lessons(course_id)
            total_lessons = len(course_lessons) if course_lessons else 1
            completion_percentage = (len(completed_lessons) / total_lessons) * 100

            # تحديث البيانات
            updated_progress = {
                'completed_lessons': completed_lessons,
                'current_lesson': lesson_id,
                'completion_percentage': round(completion_percentage, 1),
                'total_watch_time': current_progress.get('total_watch_time', 0) + progress_data.get('watch_time', 0),
                'last_accessed': datetime.now(timezone.utc).isoformat()
            }

            # حفظ التحديث
            enrollment_ref = self.database.child('enrollments').child(enrollment_id)
            enrollment_ref.update({
                'progress': updated_progress,
                'updated_at': datetime.now(timezone.utc).isoformat()
            })

            # تحديث حالة التسجيل إذا اكتمل الكورس
            if completion_percentage >= 100:
                enrollment_ref.update({
                    'status': 'completed',
                    'completed_at': datetime.now(timezone.utc).isoformat()
                })

            logger.info(f"تم تحديث تقدم الطالب {student_id} في الدرس {lesson_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في تحديث تقدم الدرس: {e}")
            return False

    def save_video_progress(self, student_id: str, lesson_id: str, current_time: float, duration: float) -> bool:
        """
        حفظ نقطة التوقف في الفيديو
        Save video progress/bookmark
        """
        try:
            if not self._initialized:
                logger.error("Firebase غير مهيأ")
                return False

            # إنشاء مرجع لتقدم الفيديو
            video_progress_ref = self.database.child('video_progress').child(student_id).child(lesson_id)

            progress_data = {
                'current_time': current_time,
                'duration': duration,
                'percentage': (current_time / duration) * 100 if duration > 0 else 0,
                'updated_at': datetime.now(timezone.utc).isoformat()
            }

            video_progress_ref.set(progress_data)
            logger.info(f"تم حفظ تقدم الفيديو للطالب {student_id} في الدرس {lesson_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في حفظ تقدم الفيديو: {e}")
            return False

    def get_video_progress(self, student_id: str, lesson_id: str) -> Dict[str, Any]:
        """
        الحصول على نقطة التوقف في الفيديو
        Get video progress/bookmark
        """
        try:
            if not self._initialized:
                logger.error("Firebase غير مهيأ")
                return {}

            video_progress_ref = self.database.child('video_progress').child(student_id).child(lesson_id)
            progress_data = video_progress_ref.get()

            return progress_data or {}

        except Exception as e:
            logger.error(f"خطأ في الحصول على تقدم الفيديو: {e}")
            return {}

    def update_student_instructor(self, student_id: str, instructor_id: str = None) -> bool:
        """تحديث المدرس المسؤول عن طالب معين"""
        try:
            if not self._initialized:
                return False

            update_data = {
                'assigned_instructor': instructor_id,
                'updated_at': datetime.now(timezone.utc).isoformat()
            }

            # إزالة الحقل إذا كان instructor_id فارغ
            if instructor_id is None:
                update_data['assigned_instructor'] = None

            self.database.child('users').child(student_id).update(update_data)
            logger.info(f"تم تحديث المدرس المسؤول عن الطالب {student_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في تحديث المدرس المسؤول عن الطالب: {e}")
            return False

    def get_students_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات الطلاب"""
        try:
            if not self._initialized:
                return {}

            students = self.get_all_students()
            instructors = self.get_all_instructors()

            # إحصائيات أساسية
            total_students = len(students)
            assigned_students = len([s for s in students if s.get('assigned_instructor')])
            unassigned_students = total_students - assigned_students

            # توزيع الطلاب حسب المدرسين
            instructor_distribution = {}
            for instructor in instructors:
                instructor_id = instructor.get('id')
                instructor_students = [s for s in students if s.get('assigned_instructor') == instructor_id]
                instructor_distribution[instructor.get('full_name', 'غير محدد')] = len(instructor_students)

            # توزيع الطلاب حسب التخصصات
            specialization_distribution = {}
            for student in students:
                spec_name = student.get('specialization_name', 'غير محدد')
                specialization_distribution[spec_name] = specialization_distribution.get(spec_name, 0) + 1

            return {
                'total_students': total_students,
                'assigned_students': assigned_students,
                'unassigned_students': unassigned_students,
                'instructor_distribution': instructor_distribution,
                'specialization_distribution': specialization_distribution
            }

        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات الطلاب: {e}")
            return {}

    def get_course(self, course_id: str) -> Dict[str, Any]:
        """الحصول على معلومات كورس معين"""
        try:
            if not self._initialized:
                return {}

            course = self.database.child('courses').child(course_id).get()

            if course:
                course_data = course
                course_data['id'] = course_id
                return course_data

            return {}

        except Exception as e:
            logger.error(f"خطأ في الحصول على الكورس: {e}")
            return {}

    def update_instructor_permissions(self, instructor_id: str, permissions: Dict[str, Any]) -> bool:
        """تحديث صلاحيات مدرس معين"""
        try:
            if not self._initialized:
                return False

            # التحقق من وجود المدرس
            user_data = self.database.child('users').child(instructor_id).get()
            if not user_data or user_data.get('role') != 'instructor':
                logger.error(f"المدرس غير موجود: {instructor_id}")
                return False

            # تحديث الصلاحيات
            update_data = {
                'permissions': permissions,
                'updated_at': datetime.now(timezone.utc).isoformat()
            }

            self.database.child('users').child(instructor_id).update(update_data)
            logger.info(f"تم تحديث صلاحيات المدرس: {instructor_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في تحديث صلاحيات المدرس: {e}")
            return False

    def can_instructor_create_course(self, instructor_id: str, specialization_id: str = None, stage: int = None, is_general: bool = False) -> Dict[str, Any]:
        """التحقق من صلاحية المدرس لإنشاء كورس معين"""
        try:
            if not self._initialized:
                return {'allowed': False, 'reason': 'قاعدة البيانات غير متاحة'}

            # الحصول على بيانات المدرس
            instructor = self.get_user(instructor_id)
            if not instructor or instructor.get('role') != 'instructor':
                return {'allowed': False, 'reason': 'المدرس غير موجود'}

            permissions = instructor.get('permissions', {})

            # التحقق من صلاحية إنشاء الكورسات
            if not permissions.get('can_create_courses', False):
                return {'allowed': False, 'reason': 'ليس لديك صلاحية إنشاء الكورسات'}

            # التحقق من الكورسات العامة
            if is_general:
                if not permissions.get('can_create_general_courses', False):
                    return {'allowed': False, 'reason': 'ليس لديك صلاحية إنشاء الكورسات العامة'}
                return {'allowed': True, 'reason': 'مسموح - كورس عام'}

            # التحقق من المرحلة
            if stage is not None:
                allowed_stages = permissions.get('allowed_stages', [])
                if stage not in allowed_stages:
                    return {'allowed': False, 'reason': f'ليس لديك صلاحية للمرحلة {stage}'}

            # التحقق من التخصص
            if specialization_id:
                instructor_specialization = instructor.get('specialization_id')
                if instructor_specialization != specialization_id:
                    return {'allowed': False, 'reason': 'يمكنك إنشاء كورسات في تخصصك فقط'}

            return {'allowed': True, 'reason': 'مسموح'}

        except Exception as e:
            logger.error(f"خطأ في التحقق من صلاحيات إنشاء الكورس: {e}")
            return {'allowed': False, 'reason': 'خطأ في النظام'}

    def get_instructor_course_options(self, instructor_id: str) -> Dict[str, Any]:
        """الحصول على خيارات إنشاء الكورسات المتاحة للمدرس"""
        try:
            if not self._initialized:
                return {}

            # الحصول على بيانات المدرس
            instructor = self.get_user(instructor_id)
            if not instructor or instructor.get('role') != 'instructor':
                return {}

            permissions = instructor.get('permissions', {})

            # التحقق من صلاحية إنشاء الكورسات
            if not permissions.get('can_create_courses', False):
                return {'can_create': False, 'reason': 'ليس لديك صلاحية إنشاء الكورسات'}

            result = {
                'can_create': True,
                'allowed_stages': permissions.get('allowed_stages', []),
                'can_create_general': permissions.get('can_create_general_courses', False),
                'instructor_specialization': instructor.get('specialization_id'),
                'instructor_specialization_name': None
            }

            # الحصول على اسم التخصص
            if result['instructor_specialization']:
                specialization = self.get_specialization(result['instructor_specialization'])
                if specialization:
                    result['instructor_specialization_name'] = specialization.get('name', 'غير محدد')

            return result

        except Exception as e:
            logger.error(f"خطأ في الحصول على خيارات إنشاء الكورسات: {e}")
            return {}

    def delete_course(self, course_id: str) -> bool:
        """حذف كورس"""
        try:
            if not self._initialized:
                return False

            # التحقق من وجود الكورس
            course_data = self.get_course(course_id)
            if not course_data:
                logger.error(f"الكورس غير موجود: {course_id}")
                return False

            # حذف الكورس
            self.database.child('courses').child(course_id).remove()

            # TODO: حذف الدروس والمحتوى المرتبط بالكورس
            # TODO: إلغاء تسجيل الطلاب من الكورس

            logger.info(f"تم حذف الكورس: {course_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في حذف الكورس: {e}")
            return False

    def update_course(self, course_id: str, update_data: Dict[str, Any]) -> bool:
        """تحديث بيانات كورس"""
        try:
            if not self._initialized:
                return False

            # التحقق من وجود الكورس
            course_data = self.get_course(course_id)
            if not course_data:
                logger.error(f"الكورس غير موجود: {course_id}")
                return False

            # إضافة تاريخ التحديث
            update_data['updated_at'] = datetime.now(timezone.utc).isoformat()

            # تحديث البيانات
            self.database.child('courses').child(course_id).update(update_data)

            logger.info(f"تم تحديث الكورس: {course_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في تحديث الكورس: {e}")
            return False

    def get_instructors_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات المدرسين"""
        try:
            if not self._initialized:
                return {}

            instructors = self.get_all_instructors()

            # إحصائيات عامة
            total_instructors = len(instructors)
            active_instructors = len([i for i in instructors if i.get('active', True)])

            # إحصائيات الصلاحيات
            can_create_courses = len([i for i in instructors if i.get('permissions', {}).get('can_create_courses', False)])
            can_manage_students = len([i for i in instructors if i.get('permissions', {}).get('can_manage_students', False)])
            can_create_general = len([i for i in instructors if i.get('permissions', {}).get('can_create_general_courses', False)])

            # إحصائيات التخصصات
            specializations_count = {}
            for instructor in instructors:
                spec_name = instructor.get('specialization_name', 'غير محدد')
                specializations_count[spec_name] = specializations_count.get(spec_name, 0) + 1

            # إحصائيات المراحل
            stages_count = {}
            for instructor in instructors:
                allowed_stages = instructor.get('permissions', {}).get('allowed_stages', [])
                for stage in allowed_stages:
                    stages_count[f"المرحلة {stage}"] = stages_count.get(f"المرحلة {stage}", 0) + 1

            return {
                'total_instructors': total_instructors,
                'active_instructors': active_instructors,
                'inactive_instructors': total_instructors - active_instructors,
                'permissions_stats': {
                    'can_create_courses': can_create_courses,
                    'can_manage_students': can_manage_students,
                    'can_create_general_courses': can_create_general
                },
                'specializations_distribution': specializations_count,
                'stages_distribution': stages_count
            }

        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات المدرسين: {e}")
            return {}

    def initialize_database_structure(self) -> bool:
        """تهيئة هيكل قاعدة البيانات الأساسي"""
        try:
            if not self._initialized:
                return False
            
            # إنشاء التخصصات الأساسية
            default_specializations = [
                {
                    'name': 'التحليل الطبي',
                    'name_en': 'Medical Laboratory',
                    'icon': 'fas fa-microscope',
                    'description': 'تخصص التحليل الطبي للمراحل 2، 3، 4',
                    'stages': [2, 3, 4],
                    'active': True
                },
                {
                    'name': 'الأشعة',
                    'name_en': 'Radiology',
                    'icon': 'fas fa-x-ray',
                    'description': 'تخصص الأشعة للمراحل 2، 3، 4',
                    'stages': [2, 3, 4],
                    'active': True
                },
                {
                    'name': 'التخدير',
                    'name_en': 'Anesthesia',
                    'icon': 'fas fa-syringe',
                    'description': 'تخصص التخدير للمراحل 2، 3، 4',
                    'stages': [2, 3, 4],
                    'active': True
                }
            ]
            
            # التحقق من وجود التخصصات
            existing_specs = self.get_all_specializations()
            if not existing_specs:
                for spec in default_specializations:
                    self.create_specialization(spec)
                logger.info("تم إنشاء التخصصات الأساسية")
            
            # إنشاء هيكل قاعدة البيانات الأساسي
            initial_structure = {
                'system_info': {
                    'initialized_at': datetime.now(timezone.utc).isoformat(),
                    'version': '1.0.0',
                    'platform_name': 'منصة الكورسات التعليمية'
                }
            }
            
            self.database.update(initial_structure)
            logger.info("تم تهيئة هيكل قاعدة البيانات")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تهيئة هيكل قاعدة البيانات: {e}")
            return False

    def update_specialization(self, specialization_id: str, update_data: Dict[str, Any]) -> bool:
        """تحديث تخصص موجود"""
        try:
            if not self._initialized:
                return False

            specializations_ref = self.database.child('specializations').child(specialization_id)
            specializations_ref.update(update_data)
            logger.info(f"تم تحديث التخصص: {specialization_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في تحديث التخصص: {e}")
            return False

    def delete_specialization(self, specialization_id: str) -> bool:
        """حذف تخصص"""
        try:
            if not self._initialized:
                return False

            specializations_ref = self.database.child('specializations').child(specialization_id)
            specializations_ref.delete()
            logger.info(f"تم حذف التخصص: {specialization_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في حذف التخصص: {e}")
            return False

    def get_users_by_specialization(self, specialization_id: str) -> List[Dict[str, Any]]:
        """الحصول على المستخدمين حسب التخصص"""
        try:
            if not self._initialized:
                return []

            users_ref = self.database.child('users')
            users_data = users_ref.order_by_child('specialization_id').equal_to(specialization_id).get()

            if not users_data:
                return []

            users = []
            for user_id, user_data in users_data.items():
                user_data['id'] = user_id
                users.append(user_data)

            return users

        except Exception as e:
            logger.error(f"خطأ في الحصول على المستخدمين حسب التخصص: {e}")
            return []

    def get_courses_by_specialization(self, specialization_id: str) -> List[Dict[str, Any]]:
        """الحصول على الكورسات حسب التخصص"""
        try:
            if not self._initialized:
                return []

            courses_ref = self.database.child('courses')
            courses_data = courses_ref.order_by_child('specialization_id').equal_to(specialization_id).get()

            if not courses_data:
                return []

            courses = []
            for course_id, course_data in courses_data.items():
                course_data['id'] = course_id
                courses.append(course_data)

            return courses

        except Exception as e:
            logger.error(f"خطأ في الحصول على الكورسات حسب التخصص: {e}")
            return []

    def get_course_lessons(self, course_id: str) -> List[Dict[str, Any]]:
        """الحصول على دروس الكورس"""
        try:
            if not self._initialized:
                return []

            lessons_ref = self.database.child('lessons')
            lessons_data = lessons_ref.order_by_child('course_id').equal_to(course_id).get()

            if not lessons_data:
                return []

            lessons = []
            for lesson_id, lesson_data in lessons_data.items():
                lesson_data['id'] = lesson_id
                lessons.append(lesson_data)

            # ترتيب الدروس حسب الترتيب
            lessons.sort(key=lambda x: x.get('order', 0))

            return lessons

        except Exception as e:
            logger.error(f"خطأ في الحصول على دروس الكورس: {e}")
            return []

    def create_lesson(self, lesson_data: Dict[str, Any]) -> Optional[str]:
        """إنشاء درس جديد"""
        try:
            if not self._initialized:
                return None

            lesson_data['created_at'] = datetime.now(timezone.utc).isoformat()
            lesson_data['updated_at'] = lesson_data['created_at']

            lesson_ref = self.database.child('lessons').push(lesson_data)

            logger.info(f"تم إنشاء درس جديد: {lesson_ref.key}")
            return lesson_ref.key

        except Exception as e:
            logger.error(f"خطأ في إنشاء الدرس: {e}")
            return None

    def get_lesson(self, lesson_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على درس معين"""
        try:
            if not self._initialized:
                return None

            lesson_data = self.database.child('lessons').child(lesson_id).get()

            if lesson_data:
                lesson_data['id'] = lesson_id
                return lesson_data

            return None

        except Exception as e:
            logger.error(f"خطأ في الحصول على الدرس: {e}")
            return None

    def update_lesson(self, lesson_id: str, update_data: Dict[str, Any]) -> bool:
        """تحديث درس"""
        try:
            if not self._initialized:
                return False

            # التحقق من وجود الدرس
            lesson = self.get_lesson(lesson_id)
            if not lesson:
                logger.error(f"الدرس غير موجود: {lesson_id}")
                return False

            # إضافة تاريخ التحديث
            update_data['updated_at'] = datetime.now(timezone.utc).isoformat()

            # تحديث البيانات
            self.database.child('lessons').child(lesson_id).update(update_data)

            logger.info(f"تم تحديث الدرس: {lesson_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في تحديث الدرس: {e}")
            return False

    def delete_lesson(self, lesson_id: str) -> bool:
        """حذف درس"""
        try:
            if not self._initialized:
                return False

            # التحقق من وجود الدرس
            lesson = self.get_lesson(lesson_id)
            if not lesson:
                logger.error(f"الدرس غير موجود: {lesson_id}")
                return False

            # حذف الدرس
            self.database.child('lessons').child(lesson_id).remove()

            logger.info(f"تم حذف الدرس: {lesson_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في حذف الدرس: {e}")
            return False

    def reorder_lessons(self, course_id: str, lessons_order: List[Dict[str, Any]]) -> bool:
        """إعادة ترتيب دروس الكورس"""
        try:
            if not self._initialized:
                return False

            # تحديث ترتيب كل درس
            for lesson_order in lessons_order:
                lesson_id = lesson_order.get('id')
                new_order = lesson_order.get('order')

                if lesson_id and new_order is not None:
                    self.database.child('lessons').child(lesson_id).update({
                        'order': new_order,
                        'updated_at': datetime.now(timezone.utc).isoformat()
                    })

            logger.info(f"تم تحديث ترتيب دروس الكورس: {course_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في إعادة ترتيب الدروس: {e}")
            return False

    def update_course_lesson_count(self, course_id: str) -> bool:
        """تحديث عدد الدروس في الكورس"""
        try:
            if not self._initialized:
                return False

            # الحصول على عدد الدروس
            lessons = self.get_course_lessons(course_id)
            lesson_count = len(lessons)

            # تحديث عدد الدروس في الكورس
            self.database.child('courses').child(course_id).update({
                'lesson_count': lesson_count,
                'updated_at': datetime.now(timezone.utc).isoformat()
            })

            logger.info(f"تم تحديث عدد دروس الكورس {course_id}: {lesson_count}")
            return True

        except Exception as e:
            logger.error(f"خطأ في تحديث عدد دروس الكورس: {e}")
            return False

    def upload_lesson_image(self, file, course_id: str, user_id: str) -> Optional[str]:
        """رفع صورة درس إلى Firebase Storage"""
        try:
            if not self._initialized or not self.storage:
                logger.error("Firebase Storage غير مهيأ")
                return None

            # إنتاج اسم ملف فريد
            import uuid
            from datetime import datetime

            file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else 'jpg'
            unique_filename = f"lessons/{course_id}/{user_id}_{uuid.uuid4().hex}_{int(datetime.now().timestamp())}.{file_extension}"

            # رفع الملف
            blob = self.storage.blob(unique_filename)
            blob.upload_from_file(file, content_type=f'image/{file_extension}')

            # جعل الملف قابل للوصول العام
            blob.make_public()

            # الحصول على رابط الملف
            image_url = blob.public_url

            logger.info(f"تم رفع صورة الدرس: {unique_filename}")
            return image_url

        except Exception as e:
            logger.error(f"خطأ في رفع صورة الدرس: {e}")
            return None

    def delete_lesson_image(self, image_url: str) -> bool:
        """حذف صورة درس من Firebase Storage"""
        try:
            if not self._initialized or not self.storage:
                return False

            # استخراج مسار الملف من الرابط
            import urllib.parse
            parsed_url = urllib.parse.urlparse(image_url)

            # استخراج اسم الملف من المسار
            file_path = parsed_url.path.split('/')[-1]

            # البحث عن الملف وحذفه
            blob = self.storage.blob(file_path)
            if blob.exists():
                blob.delete()
                logger.info(f"تم حذف صورة الدرس: {file_path}")
                return True
            else:
                logger.warning(f"صورة الدرس غير موجودة: {file_path}")
                return False

        except Exception as e:
            logger.error(f"خطأ في حذف صورة الدرس: {e}")
            return False

    @cache_with_expiry(expiry_seconds=180)  # تخزين مؤقت لمدة 3 دقائق
    def get_all_users(self) -> List[Dict[str, Any]]:
        """الحصول على جميع المستخدمين"""
        try:
            if not self._initialized:
                return []

            users_ref = self.database.child('users')
            users_data = users_ref.get()

            if not users_data:
                return []

            users = []
            for user_id, user_data in users_data.items():
                user_data['id'] = user_id
                users.append(user_data)

            return users

        except Exception as e:
            logger.error(f"خطأ في الحصول على جميع المستخدمين: {e}")
            return []

    def get_all_courses(self) -> List[Dict[str, Any]]:
        """الحصول على جميع الكورسات"""
        try:
            if not self._initialized:
                return []

            courses_ref = self.database.child('courses')
            courses_data = courses_ref.get()

            if not courses_data:
                return []

            courses = []
            for course_id, course_data in courses_data.items():
                course_data['id'] = course_id
                courses.append(course_data)

            return courses

        except Exception as e:
            logger.error(f"خطأ في الحصول على جميع الكورسات: {e}")
            return []

    # ==================== وظائف أيقونات التخصصات ====================

    def create_specialization_icon(self, icon_data: Dict[str, Any]) -> Optional[str]:
        """إنشاء أيقونة تخصص جديدة"""
        try:
            if not self._initialized:
                return None

            icon_data['created_at'] = datetime.now(timezone.utc).isoformat()
            icon_data['updated_at'] = icon_data['created_at']

            icon_ref = self.database.child('specialization_icons').push(icon_data)

            logger.info(f"تم إنشاء أيقونة تخصص جديدة: {icon_ref.key}")
            return icon_ref.key

        except Exception as e:
            logger.error(f"خطأ في إنشاء أيقونة التخصص: {e}")
            return None

    def get_all_specialization_icons(self) -> List[Dict[str, Any]]:
        """الحصول على جميع أيقونات التخصصات"""
        try:
            if not self._initialized:
                return []

            icons = self.database.child('specialization_icons').get()
            if not icons:
                return []

            result = []
            for icon_id, icon_data in icons.items():
                icon_data['id'] = icon_id
                result.append(icon_data)

            return result

        except Exception as e:
            logger.error(f"خطأ في الحصول على أيقونات التخصصات: {e}")
            return []

    def get_specialization_icon(self, icon_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على أيقونة تخصص محددة"""
        try:
            if not self._initialized:
                return None

            if not icon_id:
                return None

            icon_data = self.database.child('specialization_icons').child(icon_id).get()
            if icon_data:
                icon_data['id'] = icon_id
                return icon_data

            return None

        except Exception as e:
            logger.error(f"خطأ في الحصول على أيقونة التخصص {icon_id}: {e}")
            return None

    def update_specialization_icon(self, icon_id: str, update_data: Dict[str, Any]) -> bool:
        """تحديث أيقونة تخصص"""
        try:
            if not self._initialized:
                return False

            update_data['updated_at'] = datetime.now(timezone.utc).isoformat()

            icon_ref = self.database.child('specialization_icons').child(icon_id)
            icon_ref.update(update_data)
            logger.info(f"تم تحديث أيقونة التخصص: {icon_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في تحديث أيقونة التخصص: {e}")
            return False

    def delete_specialization_icon(self, icon_id: str) -> bool:
        """حذف أيقونة تخصص"""
        try:
            if not self._initialized:
                return False

            icon_ref = self.database.child('specialization_icons').child(icon_id)
            icon_ref.delete()
            logger.info(f"تم حذف أيقونة التخصص: {icon_id}")
            return True

        except Exception as e:
            logger.error(f"خطأ في حذف أيقونة التخصص: {e}")
            return False

    # ==================== وظائف أكواد التفعيل ====================

    def create_activation_code(self, course_id: str, created_by: str, max_uses: int = 1, expires_at: datetime = None) -> Optional[str]:
        """إنشاء كود تفعيل جديد"""
        try:
            if not self._initialized:
                return None

            # التحقق من وجود الكورس
            course = self.get_course(course_id)
            if not course:
                logger.error(f"الكورس غير موجود: {course_id}")
                return None

            # التحقق من صلاحيات المنشئ
            creator = self.get_user(created_by)
            if not creator:
                logger.error(f"المستخدم غير موجود: {created_by}")
                return None

            # التحقق من الصلاحيات
            creator_role = creator.get('role')
            if creator_role == 'instructor':
                # المدرس يمكنه إنشاء أكواد للكورسات التي أنشأها فقط
                if course.get('instructor_id') != created_by:
                    logger.error(f"المدرس {created_by} لا يملك صلاحية إنشاء كود للكورس {course_id}")
                    return None
            elif creator_role != 'admin':
                logger.error(f"المستخدم {created_by} لا يملك صلاحية إنشاء أكواد التفعيل")
                return None

            # إنشاء كود عشوائي فريد
            import secrets
            import string

            code = None
            attempts = 0
            max_attempts = 10

            while attempts < max_attempts:
                # إنشاء كود من 22 حرف (أحرف كبيرة وأرقام)
                characters = string.ascii_uppercase + string.digits
                potential_code = ''.join(secrets.choice(characters) for _ in range(22))

                # التحقق من عدم وجود الكود مسبقاً
                existing_code = self.get_activation_code_by_code(potential_code)
                if not existing_code:
                    code = potential_code
                    break

                attempts += 1

            if not code:
                logger.error("فشل في إنشاء كود فريد بعد عدة محاولات")
                return None

            # إعداد بيانات الكود
            code_data = {
                'code': code,
                'course_id': course_id,
                'created_by': created_by,
                'max_uses': max_uses,
                'current_uses': 0,
                'active': True,
                'created_at': datetime.now(timezone.utc).isoformat(),
                'expires_at': expires_at.isoformat() if expires_at else None,
                'used_by': []  # قائمة معرفات المستخدمين الذين استخدموا الكود
            }

            # حفظ الكود في قاعدة البيانات
            code_ref = self.database.child('activation_codes').push(code_data)

            if code_ref and code_ref.get('name'):
                logger.info(f"تم إنشاء كود تفعيل جديد: {code} للكورس {course_id}")
                return code
            else:
                logger.error("فشل في حفظ كود التفعيل في قاعدة البيانات")
                return None

        except Exception as e:
            logger.error(f"خطأ في إنشاء كود التفعيل: {e}")
            return None

    def get_activation_codes(self, user_id: str = None, course_id: str = None, active_only: bool = True) -> List[Dict[str, Any]]:
        """الحصول على أكواد التفعيل"""
        try:
            if not self._initialized:
                return []

            # الحصول على جميع أكواد التفعيل
            codes_data = self.database.child('activation_codes').get()

            if not codes_data:
                return []

            result = []
            current_time = datetime.now(timezone.utc)

            for code_id, code_data in codes_data.items():
                if not isinstance(code_data, dict):
                    continue

                code_data['id'] = code_id

                # تصفية حسب المستخدم
                if user_id and code_data.get('created_by') != user_id:
                    continue

                # تصفية حسب الكورس
                if course_id and code_data.get('course_id') != course_id:
                    continue

                # تصفية الأكواد النشطة فقط
                if active_only and not code_data.get('active', True):
                    continue

                # التحقق من انتهاء الصلاحية
                expires_at = code_data.get('expires_at')
                if expires_at:
                    try:
                        expiry_date = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                        if current_time > expiry_date:
                            code_data['expired'] = True
                            if active_only:
                                continue
                        else:
                            code_data['expired'] = False
                    except:
                        code_data['expired'] = False
                else:
                    code_data['expired'] = False

                # إضافة معلومات الكورس
                if code_data.get('course_id'):
                    course = self.get_course(code_data['course_id'])
                    if course:
                        code_data['course_title'] = course.get('title', 'غير محدد')
                        code_data['course_instructor'] = course.get('instructor_name', 'غير محدد')

                # إضافة معلومات المنشئ
                if code_data.get('created_by'):
                    creator = self.get_user(code_data['created_by'])
                    if creator:
                        code_data['creator_name'] = f"{creator.get('first_name', '')} {creator.get('last_name', '')}".strip()
                        code_data['creator_role'] = creator.get('role', 'غير محدد')

                result.append(code_data)

            # ترتيب النتائج حسب تاريخ الإنشاء (الأحدث أولاً)
            result.sort(key=lambda x: x.get('created_at', ''), reverse=True)

            return result

        except Exception as e:
            logger.error(f"خطأ في الحصول على أكواد التفعيل: {e}")
            return []

    def get_activation_code_by_code(self, code: str) -> Optional[Dict[str, Any]]:
        """البحث عن كود تفعيل بالرمز"""
        try:
            if not self._initialized:
                return None

            # البحث عن الكود
            codes_data = self.database.child('activation_codes').order_by_child('code').equal_to(code).get()

            if not codes_data:
                return None

            # الحصول على أول نتيجة (يجب أن يكون الكود فريد)
            for code_id, code_data in codes_data.items():
                if isinstance(code_data, dict):
                    code_data['id'] = code_id
                    return code_data

            return None

        except Exception as e:
            logger.error(f"خطأ في البحث عن كود التفعيل: {e}")
            return None

    def use_activation_code(self, code: str, student_id: str) -> Tuple[bool, str]:
        """استخدام كود تفعيل"""
        try:
            if not self._initialized:
                return False, "خطأ في الاتصال بقاعدة البيانات"

            # البحث عن الكود
            code_data = self.get_activation_code_by_code(code)
            if not code_data:
                return False, "كود التفعيل غير صحيح"

            # التحقق من حالة الكود
            if not code_data.get('active', True):
                return False, "كود التفعيل غير نشط"

            # التحقق من انتهاء الصلاحية
            expires_at = code_data.get('expires_at')
            if expires_at:
                try:
                    expiry_date = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                    if datetime.now(timezone.utc) > expiry_date:
                        return False, "انتهت صلاحية كود التفعيل"
                except:
                    pass

            # التحقق من عدد الاستخدامات
            current_uses = code_data.get('current_uses', 0)
            max_uses = code_data.get('max_uses', 1)

            if current_uses >= max_uses:
                return False, "تم استنفاد عدد استخدامات هذا الكود"

            # التحقق من عدم استخدام الطالب للكود مسبقاً
            used_by = code_data.get('used_by', [])
            if student_id in used_by:
                return False, "لقد استخدمت هذا الكود مسبقاً"

            # التحقق من عدم تسجيل الطالب في الكورس مسبقاً
            course_id = code_data.get('course_id')
            existing_enrollment = self.get_student_enrollment(student_id, course_id)
            if existing_enrollment:
                return False, "أنت مسجل في هذا الكورس مسبقاً"

            # تسجيل الطالب في الكورس
            enrollment_data = {
                'student_id': student_id,
                'course_id': course_id,
                'status': 'active',
                'enrolled_at': datetime.now(timezone.utc).isoformat(),
                'updated_at': datetime.now(timezone.utc).isoformat(),
                'activation_code_used': code,
                'progress': {
                    'completed_lessons': [],
                    'current_lesson': None,
                    'completion_percentage': 0
                }
            }

            # حفظ التسجيل
            enrollment_ref = self.database.child('enrollments').push(enrollment_data)

            if not enrollment_ref or not enrollment_ref.get('name'):
                return False, "فشل في تسجيلك في الكورس"

            # تحديث بيانات الكود
            updated_used_by = used_by + [student_id]
            code_updates = {
                'current_uses': current_uses + 1,
                'used_by': updated_used_by,
                'last_used_at': datetime.now(timezone.utc).isoformat()
            }

            # إذا وصل الكود للحد الأقصى من الاستخدامات، قم بتعطيله
            if current_uses + 1 >= max_uses:
                code_updates['active'] = False

            # تحديث الكود
            self.database.child('activation_codes').child(code_data['id']).update(code_updates)

            # تحديث إحصائيات الكورس
            self.update_course_stats(course_id)

            logger.info(f"تم استخدام كود التفعيل {code} بواسطة الطالب {student_id}")
            return True, "تم تفعيل الكورس بنجاح"

        except Exception as e:
            logger.error(f"خطأ في استخدام كود التفعيل: {e}")
            return False, "حدث خطأ في النظام"

    def deactivate_activation_code(self, code_id: str, user_id: str) -> Tuple[bool, str]:
        """تعطيل كود تفعيل"""
        try:
            if not self._initialized:
                return False, "خطأ في الاتصال بقاعدة البيانات"

            # الحصول على بيانات الكود
            code_data = self.database.child('activation_codes').child(code_id).get()
            if not code_data:
                return False, "كود التفعيل غير موجود"

            # التحقق من الصلاحيات
            user = self.get_user(user_id)
            if not user:
                return False, "المستخدم غير موجود"

            user_role = user.get('role')
            if user_role == 'instructor':
                # المدرس يمكنه تعطيل أكواده فقط
                if code_data.get('created_by') != user_id:
                    return False, "لا تملك صلاحية تعطيل هذا الكود"
            elif user_role != 'admin':
                return False, "لا تملك صلاحية تعطيل أكواد التفعيل"

            # تعطيل الكود
            self.database.child('activation_codes').child(code_id).update({
                'active': False,
                'deactivated_at': datetime.now(timezone.utc).isoformat(),
                'deactivated_by': user_id
            })

            logger.info(f"تم تعطيل كود التفعيل {code_id} بواسطة {user_id}")
            return True, "تم تعطيل كود التفعيل بنجاح"

        except Exception as e:
            logger.error(f"خطأ في تعطيل كود التفعيل: {e}")
            return False, "حدث خطأ في النظام"

    def delete_activation_code(self, code_id: str, user_id: str) -> Tuple[bool, str]:
        """حذف كود تفعيل"""
        try:
            if not self._initialized:
                return False, "خطأ في الاتصال بقاعدة البيانات"

            # الحصول على بيانات الكود
            code_data = self.database.child('activation_codes').child(code_id).get()
            if not code_data:
                return False, "كود التفعيل غير موجود"

            # التحقق من الصلاحيات
            user = self.get_user(user_id)
            if not user:
                return False, "المستخدم غير موجود"

            user_role = user.get('role')
            if user_role == 'instructor':
                # المدرس يمكنه حذف أكواده فقط
                if code_data.get('created_by') != user_id:
                    return False, "لا تملك صلاحية حذف هذا الكود"
            elif user_role != 'admin':
                return False, "لا تملك صلاحية حذف أكواد التفعيل"

            # التحقق من عدم استخدام الكود
            current_uses = code_data.get('current_uses', 0)
            if current_uses > 0:
                return False, "لا يمكن حذف كود تم استخدامه، يمكنك تعطيله فقط"

            # حذف الكود
            self.database.child('activation_codes').child(code_id).delete()

            logger.info(f"تم حذف كود التفعيل {code_id} بواسطة {user_id}")
            return True, "تم حذف كود التفعيل بنجاح"

        except Exception as e:
            logger.error(f"خطأ في حذف كود التفعيل: {e}")
            return False, "حدث خطأ في النظام"

    def get_student_enrollment(self, student_id: str, course_id: str) -> Optional[Dict[str, Any]]:
        """التحقق من تسجيل طالب في كورس معين"""
        try:
            if not self._initialized:
                return None

            # البحث عن التسجيل
            enrollments = self.database.child('enrollments').order_by_child('student_id').equal_to(student_id).get()

            if not enrollments:
                return None

            for enrollment_id, enrollment_data in enrollments.items():
                if isinstance(enrollment_data, dict) and enrollment_data.get('course_id') == course_id:
                    enrollment_data['id'] = enrollment_id
                    return enrollment_data

            return None

        except Exception as e:
            logger.error(f"خطأ في البحث عن تسجيل الطالب: {e}")
            return None

    def update_course_stats(self, course_id: str) -> bool:
        """تحديث إحصائيات الكورس"""
        try:
            if not self._initialized:
                return False

            # حساب عدد المسجلين
            enrollments = self.database.child('enrollments').order_by_child('course_id').equal_to(course_id).get()
            enrollment_count = len(enrollments) if enrollments else 0

            # تحديث إحصائيات الكورس
            self.database.child('courses').child(course_id).update({
                'enrollment_count': enrollment_count,
                'updated_at': datetime.now(timezone.utc).isoformat()
            })

            return True

        except Exception as e:
            logger.error(f"خطأ في تحديث إحصائيات الكورس: {e}")
            return False

    def get_activation_code_stats(self, user_id: str = None) -> Dict[str, Any]:
        """الحصول على إحصائيات أكواد التفعيل"""
        try:
            if not self._initialized:
                return {}

            codes = self.get_activation_codes(user_id=user_id, active_only=False)
            current_time = datetime.now(timezone.utc)

            # حساب الأكواد المنتهية الصلاحية
            expired_codes = []
            expiring_soon_codes = []

            for code in codes:
                expires_at = code.get('expires_at')
                if expires_at:
                    try:
                        expiry_date = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                        if current_time > expiry_date:
                            expired_codes.append(code)
                        elif (expiry_date - current_time).days <= 3:  # ينتهي خلال 3 أيام
                            expiring_soon_codes.append(code)
                    except:
                        pass

            stats = {
                'total_codes': len(codes),
                'active_codes': len([c for c in codes if c.get('active', True) and not c.get('expired', False)]),
                'used_codes': len([c for c in codes if c.get('current_uses', 0) > 0]),
                'expired_codes': len(expired_codes),
                'expiring_soon_codes': len(expiring_soon_codes),
                'total_uses': sum(c.get('current_uses', 0) for c in codes),
                'unused_codes': len([c for c in codes if c.get('current_uses', 0) == 0 and c.get('active', True)])
            }

            return stats

        except Exception as e:
            logger.error(f"خطأ في الحصول على إحصائيات أكواد التفعيل: {e}")
            return {}

    def get_expiring_codes(self, days_ahead: int = 3, user_id: str = None) -> List[Dict[str, Any]]:
        """الحصول على الأكواد التي ستنتهي خلال فترة محددة"""
        try:
            if not self._initialized:
                return []

            codes = self.get_activation_codes(user_id=user_id, active_only=True)
            expiring_codes = []

            current_time = datetime.now(timezone.utc)
            threshold_time = current_time + timedelta(days=days_ahead)

            for code in codes:
                expires_at = code.get('expires_at')
                if not expires_at:
                    continue

                try:
                    expiry_date = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))

                    # التحقق من اقتراب انتهاء الصلاحية
                    if current_time < expiry_date <= threshold_time:
                        # إضافة معلومات إضافية
                        code['days_until_expiry'] = (expiry_date - current_time).days
                        code['hours_until_expiry'] = (expiry_date - current_time).total_seconds() / 3600
                        expiring_codes.append(code)

                except Exception as e:
                    logger.error(f"خطأ في معالجة تاريخ انتهاء الكود: {e}")
                    continue

            # ترتيب حسب تاريخ الانتهاء
            expiring_codes.sort(key=lambda x: x.get('expires_at', ''))

            return expiring_codes

        except Exception as e:
            logger.error(f"خطأ في الحصول على الأكواد المنتهية قريباً: {e}")
            return []

    def cleanup_expired_codes(self, days_old: int = 30, archive: bool = True) -> Tuple[int, int]:
        """تنظيف الأكواد المنتهية الصلاحية القديمة"""
        try:
            if not self._initialized:
                return 0, 0

            # الحصول على جميع الأكواد
            all_codes = self.get_activation_codes(active_only=False)

            current_time = datetime.now(timezone.utc)
            cleanup_threshold = current_time - timedelta(days=days_old)

            codes_to_cleanup = []

            for code in all_codes:
                # التحقق من انتهاء الصلاحية
                expires_at = code.get('expires_at')
                expired_at = code.get('expired_at')

                # استخدام تاريخ الانتهاء الفعلي أو تاريخ انتهاء الصلاحية
                reference_date = None
                if expired_at:
                    try:
                        reference_date = datetime.fromisoformat(expired_at.replace('Z', '+00:00'))
                    except:
                        pass
                elif expires_at:
                    try:
                        expiry_date = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                        if current_time > expiry_date:
                            reference_date = expiry_date
                    except:
                        pass

                # التحقق من قدم الكود
                if reference_date and reference_date < cleanup_threshold:
                    codes_to_cleanup.append(code)

            archived_count = 0
            deleted_count = 0

            for code in codes_to_cleanup:
                code_id = code.get('id')
                if not code_id:
                    continue

                try:
                    if archive:
                        # أرشفة الكود
                        archive_data = code.copy()
                        archive_data.update({
                            'archived_at': current_time.isoformat(),
                            'archived_by_system': True
                        })

                        self.database.child('archived_activation_codes').child(code_id).set(archive_data)
                        archived_count += 1

                    # حذف من المجموعة الأصلية
                    self.database.child('activation_codes').child(code_id).remove()
                    deleted_count += 1

                except Exception as e:
                    logger.error(f"خطأ في تنظيف الكود {code_id}: {e}")
                    continue

            logger.info(f"تم تنظيف {deleted_count} كود، تم أرشفة {archived_count} كود")
            return archived_count, deleted_count

        except Exception as e:
            logger.error(f"خطأ في تنظيف الأكواد المنتهية الصلاحية: {e}")
            return 0, 0

    def get_archived_codes(self, user_id: str = None) -> List[Dict[str, Any]]:
        """الحصول على الأكواد المؤرشفة"""
        try:
            if not self._initialized:
                return []

            archived_codes_data = self.database.child('archived_activation_codes').get()

            if not archived_codes_data:
                return []

            archived_codes = []

            for code_id, code_data in archived_codes_data.items():
                if not isinstance(code_data, dict):
                    continue

                # تصفية حسب المستخدم إذا تم تحديده
                if user_id and code_data.get('created_by') != user_id:
                    continue

                code_data['id'] = code_id

                # إضافة معلومات إضافية
                archived_at = code_data.get('archived_at')
                if archived_at:
                    try:
                        archive_date = datetime.fromisoformat(archived_at.replace('Z', '+00:00'))
                        code_data['archived_days_ago'] = (datetime.now(timezone.utc) - archive_date).days
                    except:
                        pass

                archived_codes.append(code_data)

            # ترتيب حسب تاريخ الأرشفة (الأحدث أولاً)
            archived_codes.sort(key=lambda x: x.get('archived_at', ''), reverse=True)

            return archived_codes

        except Exception as e:
            logger.error(f"خطأ في الحصول على الأكواد المؤرشفة: {e}")
            return []

# إنشاء مثيل عام من FirebaseManager
firebase_manager = FirebaseManager()

def get_firebase_manager() -> FirebaseManager:
    """الحصول على مثيل FirebaseManager"""
    return firebase_manager
