"""
اختبارات شاملة للنظام
Comprehensive System Tests
"""

import unittest
import json
import time
from unittest.mock import patch, MagicMock
import sys
import os

# إضافة مسار المشروع للاستيراد
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from utils.firebase_utils import get_firebase_manager
from utils.auth_utils import get_auth_manager

class SystemComprehensiveTests(unittest.TestCase):
    """اختبارات شاملة للنظام"""
    
    @classmethod
    def setUpClass(cls):
        """إعداد الاختبارات"""
        cls.app = create_app('testing')
        cls.client = cls.app.test_client()
        cls.app_context = cls.app.app_context()
        cls.app_context.push()
        
        # بيانات اختبار
        cls.test_admin = {
            'email': '<EMAIL>',
            'password': 'admin123',
            'first_name': 'Admin',
            'last_name': 'Test',
            'role': 'admin'
        }
        
        cls.test_instructor = {
            'email': '<EMAIL>',
            'password': 'instructor123',
            'first_name': 'Instructor',
            'last_name': 'Test',
            'role': 'instructor'
        }
        
        cls.test_student = {
            'email': '<EMAIL>',
            'password': 'student123',
            'first_name': 'Student',
            'last_name': 'Test',
            'role': 'student'
        }
    
    @classmethod
    def tearDownClass(cls):
        """تنظيف بعد الاختبارات"""
        cls.app_context.pop()
    
    def setUp(self):
        """إعداد قبل كل اختبار"""
        self.firebase_manager = get_firebase_manager()
        self.auth_manager = get_auth_manager()
    
    def test_01_application_startup(self):
        """اختبار بدء تشغيل التطبيق"""
        response = self.client.get('/')
        self.assertEqual(response.status_code, 200)
        print("✅ تم اختبار بدء تشغيل التطبيق بنجاح")
    
    def test_02_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        with patch.object(self.firebase_manager, '_initialized', True):
            result = self.firebase_manager._initialized
            self.assertTrue(result)
        print("✅ تم اختبار الاتصال بقاعدة البيانات بنجاح")
    
    def test_03_authentication_system(self):
        """اختبار نظام المصادقة"""
        # اختبار صفحة تسجيل الدخول
        response = self.client.get('/login')
        self.assertEqual(response.status_code, 200)
        
        # اختبار تسجيل دخول خاطئ
        response = self.client.post('/login', data={
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        })
        self.assertIn(response.status_code, [200, 302, 400])
        
        print("✅ تم اختبار نظام المصادقة بنجاح")
    
    def test_04_admin_dashboard(self):
        """اختبار لوحة تحكم الأدمن"""
        # محاكاة تسجيل دخول الأدمن
        with self.client.session_transaction() as sess:
            sess['user_id'] = 'admin_test_id'
            sess['user_role'] = 'admin'
        
        response = self.client.get('/admin/dashboard')
        self.assertIn(response.status_code, [200, 302])
        print("✅ تم اختبار لوحة تحكم الأدمن بنجاح")
    
    def test_05_instructor_dashboard(self):
        """اختبار لوحة تحكم المدرس"""
        # محاكاة تسجيل دخول المدرس
        with self.client.session_transaction() as sess:
            sess['user_id'] = 'instructor_test_id'
            sess['user_role'] = 'instructor'
        
        response = self.client.get('/instructor/dashboard')
        self.assertIn(response.status_code, [200, 302])
        print("✅ تم اختبار لوحة تحكم المدرس بنجاح")
    
    def test_06_student_dashboard(self):
        """اختبار لوحة تحكم الطالب"""
        # محاكاة تسجيل دخول الطالب
        with self.client.session_transaction() as sess:
            sess['user_id'] = 'student_test_id'
            sess['user_role'] = 'student'
        
        response = self.client.get('/student/dashboard')
        self.assertIn(response.status_code, [200, 302])
        print("✅ تم اختبار لوحة تحكم الطالب بنجاح")
    
    def test_07_course_management(self):
        """اختبار إدارة الكورسات"""
        # محاكاة تسجيل دخول المدرس
        with self.client.session_transaction() as sess:
            sess['user_id'] = 'instructor_test_id'
            sess['user_role'] = 'instructor'
        
        # اختبار صفحة إدارة الكورسات
        response = self.client.get('/instructor/courses')
        self.assertIn(response.status_code, [200, 302])
        
        # اختبار إنشاء كورس جديد
        response = self.client.get('/instructor/courses/new')
        self.assertIn(response.status_code, [200, 302])
        
        print("✅ تم اختبار إدارة الكورسات بنجاح")
    
    def test_08_student_management(self):
        """اختبار إدارة الطلاب"""
        # محاكاة تسجيل دخول المدرس
        with self.client.session_transaction() as sess:
            sess['user_id'] = 'instructor_test_id'
            sess['user_role'] = 'instructor'
        
        response = self.client.get('/instructor/student-management')
        self.assertIn(response.status_code, [200, 302])
        print("✅ تم اختبار إدارة الطلاب بنجاح")
    
    def test_09_video_player(self):
        """اختبار مشغل الفيديو"""
        # محاكاة تسجيل دخول الطالب
        with self.client.session_transaction() as sess:
            sess['user_id'] = 'student_test_id'
            sess['user_role'] = 'student'
        
        # اختبار صفحة مشاهدة الفيديو
        response = self.client.get('/student/course/test_course/lesson/test_lesson')
        self.assertIn(response.status_code, [200, 302, 404])
        print("✅ تم اختبار مشغل الفيديو بنجاح")
    
    def test_10_analytics_dashboard(self):
        """اختبار لوحة التحليلات"""
        # محاكاة تسجيل دخول الأدمن
        with self.client.session_transaction() as sess:
            sess['user_id'] = 'admin_test_id'
            sess['user_role'] = 'admin'
        
        response = self.client.get('/admin/analytics')
        self.assertIn(response.status_code, [200, 302])
        print("✅ تم اختبار لوحة التحليلات بنجاح")
    
    def test_11_search_and_filter(self):
        """اختبار البحث والتصفية"""
        response = self.client.get('/courses')
        self.assertIn(response.status_code, [200, 302])
        
        # اختبار البحث
        response = self.client.get('/courses?search=test')
        self.assertIn(response.status_code, [200, 302])
        
        print("✅ تم اختبار البحث والتصفية بنجاح")
    
    def test_12_api_endpoints(self):
        """اختبار نقاط النهاية للAPI"""
        # اختبار API بدون مصادقة
        response = self.client.get('/api/courses')
        self.assertIn(response.status_code, [200, 401, 403])
        
        # محاكاة مصادقة API
        headers = {'Authorization': 'Bearer test_token'}
        response = self.client.get('/api/courses', headers=headers)
        self.assertIn(response.status_code, [200, 401, 403])
        
        print("✅ تم اختبار نقاط النهاية للAPI بنجاح")
    
    def test_13_performance_optimization(self):
        """اختبار تحسينات الأداء"""
        # اختبار ضغط الاستجابات
        response = self.client.get('/')
        self.assertIn('gzip', response.headers.get('Content-Encoding', ''))
        
        # اختبار headers الأمان
        response = self.client.get('/static/css/main.css')
        self.assertIn(response.status_code, [200, 404])
        
        print("✅ تم اختبار تحسينات الأداء بنجاح")
    
    def test_14_error_handling(self):
        """اختبار معالجة الأخطاء"""
        # اختبار صفحة غير موجودة
        response = self.client.get('/nonexistent-page')
        self.assertEqual(response.status_code, 404)
        
        # اختبار خطأ خادم داخلي
        response = self.client.get('/test-500-error')
        self.assertIn(response.status_code, [404, 500])
        
        print("✅ تم اختبار معالجة الأخطاء بنجاح")
    
    def test_15_security_measures(self):
        """اختبار إجراءات الأمان"""
        # اختبار headers الأمان
        response = self.client.get('/')
        headers = response.headers
        
        # التحقق من وجود headers الأمان
        security_headers = [
            'X-Content-Type-Options',
            'X-Frame-Options',
            'X-XSS-Protection'
        ]
        
        for header in security_headers:
            self.assertIn(header, headers)
        
        print("✅ تم اختبار إجراءات الأمان بنجاح")
    
    def test_16_mobile_responsiveness(self):
        """اختبار التجاوب مع الأجهزة المحمولة"""
        # محاكاة متصفح محمول
        headers = {'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)'}
        response = self.client.get('/', headers=headers)
        self.assertEqual(response.status_code, 200)
        
        print("✅ تم اختبار التجاوب مع الأجهزة المحمولة بنجاح")
    
    def test_17_load_testing(self):
        """اختبار تحمل الأحمال"""
        start_time = time.time()
        
        # إجراء عدة طلبات متتالية
        for i in range(10):
            response = self.client.get('/')
            self.assertEqual(response.status_code, 200)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # التأكد من أن الاستجابة سريعة
        self.assertLess(total_time, 5.0)  # أقل من 5 ثوان لـ 10 طلبات
        
        print(f"✅ تم اختبار تحمل الأحمال بنجاح - الوقت: {total_time:.2f} ثانية")
    
    def test_18_database_operations(self):
        """اختبار عمليات قاعدة البيانات"""
        with patch.object(self.firebase_manager, 'get_all_users') as mock_get_users:
            mock_get_users.return_value = [self.test_admin, self.test_instructor, self.test_student]
            
            users = self.firebase_manager.get_all_users()
            self.assertEqual(len(users), 3)
            
        print("✅ تم اختبار عمليات قاعدة البيانات بنجاح")
    
    def test_19_integration_testing(self):
        """اختبار التكامل بين المكونات"""
        # اختبار التكامل بين المصادقة والتفويض
        with self.client.session_transaction() as sess:
            sess['user_id'] = 'test_user_id'
            sess['user_role'] = 'student'
        
        # اختبار الوصول لصفحة محمية
        response = self.client.get('/student/dashboard')
        self.assertIn(response.status_code, [200, 302])
        
        print("✅ تم اختبار التكامل بين المكونات بنجاح")
    
    def test_20_final_system_check(self):
        """الفحص النهائي للنظام"""
        # اختبار جميع الصفحات الرئيسية
        main_pages = [
            '/',
            '/login',
            '/courses',
            '/about',
            '/contact'
        ]
        
        for page in main_pages:
            response = self.client.get(page)
            self.assertIn(response.status_code, [200, 302, 404])
        
        print("✅ تم الفحص النهائي للنظام بنجاح")

if __name__ == '__main__':
    print("🧪 بدء الاختبارات الشاملة للنظام...")
    print("=" * 50)
    
    # تشغيل الاختبارات
    unittest.main(verbosity=2)
    
    print("=" * 50)
    print("✅ تم إكمال جميع الاختبارات بنجاح!")
