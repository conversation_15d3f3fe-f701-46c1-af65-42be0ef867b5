#!/usr/bin/env python3
"""
تشخيص مشكلة عدم ظهور الكورسات
"""

import firebase_admin
from firebase_admin import credentials, db
import requests
import json

def initialize_firebase():
    """تهيئة Firebase"""
    try:
        try:
            app = firebase_admin.get_app()
        except ValueError:
            from config import Config
            config = Config()
            cred = credentials.Certificate(config.FIREBASE_CONFIG)
            app = firebase_admin.initialize_app(cred, {
                'databaseURL': config.FIREBASE_DATABASE_URL
            })
        return db.reference()
    except Exception as e:
        print(f"❌ فشل في تهيئة Firebase: {e}")
        return None

def debug_course_issue():
    """تشخيص مشكلة عدم ظهور الكورسات"""
    
    print("🔍 تشخيص مشكلة عدم ظهور الكورسات...")
    
    # تهيئة Firebase
    database = initialize_firebase()
    if not database:
        return False
    
    try:
        # 1. البحث عن المدرس
        print("👤 البحث عن المدرس...")
        users = database.child('users').get()
        instructor_id = None
        
        if users:
            for user_id, user_data in users.items():
                if user_data.get('email') == '<EMAIL>':
                    instructor_id = user_id
                    print(f"✅ تم العثور على المدرس (ID: {instructor_id})")
                    print(f"   user_id في JWT: {user_data.get('user_id', 'غير موجود')}")
                    break
        
        if not instructor_id:
            print("❌ لم يتم العثور على المدرس")
            return False
        
        # 2. فحص الكورسات في قاعدة البيانات
        print(f"\n📚 فحص الكورسات في قاعدة البيانات...")
        courses = database.child('courses').get()
        
        if courses:
            instructor_courses = []
            print(f"📊 إجمالي الكورسات في قاعدة البيانات: {len(courses)}")
            
            for course_id, course_data in courses.items():
                course_instructor_id = course_data.get('instructor_id')
                print(f"\n📝 كورس: {course_data.get('title', 'بدون عنوان')}")
                print(f"   معرف الكورس: {course_id}")
                print(f"   معرف المدرس في الكورس: {course_instructor_id}")
                print(f"   هل يطابق المدرس؟ {course_instructor_id == instructor_id}")
                
                if course_instructor_id == instructor_id:
                    instructor_courses.append(course_data)
            
            print(f"\n📊 كورسات هذا المدرس: {len(instructor_courses)}")
            
            if instructor_courses:
                print("✅ الكورسات موجودة في قاعدة البيانات:")
                for i, course in enumerate(instructor_courses, 1):
                    print(f"  {i}. {course.get('title', 'بدون عنوان')} - {course.get('status', 'غير محدد')}")
            else:
                print("❌ لا توجد كورسات لهذا المدرس في قاعدة البيانات")
        else:
            print("❌ لا توجد كورسات في قاعدة البيانات")
        
        # 3. اختبار API مع تفاصيل أكثر
        print(f"\n🔍 اختبار API بتفاصيل أكثر...")
        
        base_url = "http://127.0.0.1:5000"
        session = requests.Session()
        
        # تسجيل الدخول
        login_data = {
            'email': '<EMAIL>',
            'password': 'instructor123'
        }
        
        login_response = session.post(
            f"{base_url}/api/auth/login",
            json=login_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            token = login_result.get('token')
            if token:
                session.headers.update({'Authorization': f'Bearer {token}'})
                
                # فك تشفير JWT token لرؤية محتواه
                import jwt
                try:
                    # فك تشفير بدون التحقق من التوقيع لرؤية المحتوى فقط
                    decoded_token = jwt.decode(token, options={"verify_signature": False})
                    print(f"\n🔑 محتوى JWT Token:")
                    print(f"   user_id: {decoded_token.get('user_id', 'غير موجود')}")
                    print(f"   email: {decoded_token.get('email', 'غير موجود')}")
                    print(f"   role: {decoded_token.get('role', 'غير موجود')}")
                    
                    token_user_id = decoded_token.get('user_id')
                    print(f"\n🔍 مقارنة المعرفات:")
                    print(f"   معرف المدرس في قاعدة البيانات: {instructor_id}")
                    print(f"   معرف المستخدم في JWT: {token_user_id}")
                    print(f"   هل يتطابقان؟ {instructor_id == token_user_id}")
                    
                except Exception as e:
                    print(f"❌ فشل في فك تشفير JWT: {e}")
            
            # اختبار جلب الكورسات
            courses_response = session.get(f"{base_url}/api/instructor/courses")
            print(f"\n📚 استجابة API للكورسات:")
            print(f"   كود الاستجابة: {courses_response.status_code}")
            
            if courses_response.status_code == 200:
                courses_result = courses_response.json()
                print(f"   نجح الطلب: {courses_result.get('success', False)}")
                courses_list = courses_result.get('courses', [])
                print(f"   عدد الكورسات المرجعة: {len(courses_list)}")
                
                if courses_list:
                    print("   الكورسات المرجعة:")
                    for i, course in enumerate(courses_list, 1):
                        print(f"     {i}. {course.get('title', 'بدون عنوان')}")
                else:
                    print("   ❌ لا توجد كورسات في الاستجابة")
            else:
                print(f"   ❌ فشل الطلب: {courses_response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_course_issue()
