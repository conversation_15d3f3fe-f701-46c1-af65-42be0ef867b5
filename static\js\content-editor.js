/**
 * محرر المحتوى الغني للكورسات
 * Rich Content Editor for Courses
 */

// ملاحظة: دالة fetchWithAuth متاحة من main.js

class ContentEditor {
    constructor() {
        this.courseId = this.getCourseIdFromUrl();
        this.currentLessonId = null;
        this.currentContentType = null;
        this.lessons = [];
        this.quillEditor = null;
        this.sortable = null;
        this.currentPage = 1;
        this.totalPages = 1;
        this.bulkActionsMode = false;
        this.selectedLessons = [];

        this.init();
    }
    
    async init() {
        try {
            // تهيئة محرر النصوص الغني
            this.initQuillEditor();
            
            // تهيئة الأحداث
            this.setupEventListeners();
            
            // تهيئة السحب والإفلات
            this.initSortable();
            
            // تحميل الدروس الموجودة
            await this.loadLessons();
            
        } catch (error) {
            console.error('خطأ في تهيئة محرر المحتوى:', error);
            this.showError('حدث خطأ في تحميل المحرر');
        }
    }
    
    getCourseIdFromUrl() {
        const pathParts = window.location.pathname.split('/');
        return pathParts[pathParts.length - 2]; // افتراض أن الرابط /instructor/courses/{id}/edit
    }
    
    initQuillEditor() {
        this.quillEditor = new Quill('#textContent', {
            theme: 'snow',
            modules: {
                toolbar: [
                    [{ 'header': [1, 2, 3, false] }],
                    ['bold', 'italic', 'underline', 'strike'],
                    [{ 'color': [] }, { 'background': [] }],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    [{ 'align': [] }],
                    ['link', 'blockquote', 'code-block'],
                    ['clean']
                ]
            },
            placeholder: 'اكتب محتوى الدرس هنا...'
        });
    }
    
    setupEventListeners() {
        // أزرار أنواع المحتوى
        document.querySelectorAll('.content-type-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const contentType = e.target.closest('.content-type-btn').dataset.type;
                this.showContentEditor(contentType);
            });
        });
        
        // نماذج المحتوى
        document.getElementById('textForm').addEventListener('submit', (e) => this.saveTextLesson(e));
        document.getElementById('imageForm').addEventListener('submit', (e) => this.saveImageLesson(e));
        document.getElementById('videoForm').addEventListener('submit', (e) => this.saveVideoLesson(e));
        document.getElementById('mcqForm').addEventListener('submit', (e) => this.saveMcqLesson(e));
        document.getElementById('essayForm').addEventListener('submit', (e) => this.saveEssayLesson(e));
        
        // رفع الصور
        this.setupImageUpload();
    }
    
    setupImageUpload() {
        const uploadArea = document.getElementById('imageUploadArea');
        const fileInput = document.getElementById('imageUpload');
        const preview = document.getElementById('imagePreview');
        const previewImg = document.getElementById('previewImg');
        
        // النقر على منطقة الرفع
        uploadArea.addEventListener('click', () => fileInput.click());
        
        // تغيير الملف
        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                this.previewImage(file, previewImg, preview);
            }
        });
        
        // السحب والإفلات
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const file = e.dataTransfer.files[0];
            if (file && file.type.startsWith('image/')) {
                fileInput.files = e.dataTransfer.files;
                this.previewImage(file, previewImg, preview);
            }
        });
    }
    
    previewImage(file, imgElement, containerElement) {
        const reader = new FileReader();
        reader.onload = (e) => {
            imgElement.src = e.target.result;
            containerElement.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
    
    initSortable() {
        const lessonsList = document.getElementById('lessonsList');
        this.sortable = Sortable.create(lessonsList, {
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            handle: '.drag-handle',
            onEnd: (evt) => {
                this.updateLessonsOrder();
            }
        });
    }
    
    showContentEditor(contentType) {
        // إخفاء جميع المحررات
        document.querySelectorAll('.content-editor-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        
        // إزالة التفعيل من جميع الأزرار
        document.querySelectorAll('.content-type-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // تفعيل المحرر المطلوب
        const editorId = contentType + 'Editor';
        const editor = document.getElementById(editorId);
        if (editor) {
            editor.classList.add('active');
        }
        
        // تفعيل الزر المطلوب
        const activeBtn = document.querySelector(`[data-type="${contentType}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }
        
        this.currentContentType = contentType;
        this.currentLessonId = null; // إنشاء درس جديد
        
        // مسح النماذج
        this.clearForms();
    }
    
    clearForms() {
        // مسح نموذج النص
        document.getElementById('textTitle').value = '';
        if (this.quillEditor) {
            this.quillEditor.setContents([]);
        }
        
        // مسح نموذج الصورة
        document.getElementById('imageTitle').value = '';
        document.getElementById('imageCaption').value = '';
        document.getElementById('imageUpload').value = '';
        document.getElementById('imagePreview').style.display = 'none';
        
        // مسح نموذج الفيديو
        document.getElementById('videoTitle').value = '';
        document.getElementById('videoUrl').value = '';
        document.getElementById('videoDescription').value = '';
        document.getElementById('videoDuration').value = '';
        
        // مسح نموذج الأسئلة متعددة الخيارات
        document.getElementById('mcqTitle').value = '';
        document.getElementById('mcqQuestion').value = '';
        document.getElementById('mcqFeedback').value = '';
        document.getElementById('mcqPoints').value = '1';
        this.resetMcqOptions();
        
        // مسح نموذج الأسئلة المقالية
        document.getElementById('essayTitle').value = '';
        document.getElementById('essayQuestion').value = '';
        document.getElementById('essayMaxWords').value = '500';
        document.getElementById('essayRubric').value = '';
        document.getElementById('essayPoints').value = '10';
    }
    
    resetMcqOptions() {
        const optionsContainer = document.getElementById('mcqOptions');
        optionsContainer.innerHTML = `
            <div class="option-input">
                <input type="text" class="form-control" placeholder="الخيار الأول" required>
                <input type="checkbox" class="form-check-input" title="إجابة صحيحة">
                <button type="button" class="remove-option-btn" onclick="removeOption(this)" style="display: none;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="option-input">
                <input type="text" class="form-control" placeholder="الخيار الثاني" required>
                <input type="checkbox" class="form-check-input" title="إجابة صحيحة">
                <button type="button" class="remove-option-btn" onclick="removeOption(this)" style="display: none;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        this.updateRemoveButtons();
    }
    
    async loadLessons() {
        try {
            this.showLoading(true);
            
            const response = await fetchWithAuth(`/api/instructor/courses/${this.courseId}/lessons`);
            
            if (response.success) {
                this.lessons = response.lessons || [];
                this.renderLessons();
            } else {
                throw new Error(response.message || 'فشل في تحميل الدروس');
            }
            
        } catch (error) {
            console.error('خطأ في تحميل الدروس:', error);
            this.showError('حدث خطأ في تحميل الدروس');
        } finally {
            this.showLoading(false);
        }
    }
    
    renderLessons() {
        const lessonsList = document.getElementById('lessonsList');

        if (this.lessons.length === 0) {
            lessonsList.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-book-open fa-3x mb-3"></i>
                    <p>لا توجد دروس بعد</p>
                    <p class="small">ابدأ بإضافة محتوى جديد</p>
                </div>
            `;
            this.updateLessonsCount();
            return;
        }

        // فلترة الدروس حسب الصفحة الحالية
        const lessonsPerPage = 10;
        const startIndex = (this.currentPage - 1) * lessonsPerPage;
        const endIndex = startIndex + lessonsPerPage;
        const currentPageLessons = this.lessons.slice(startIndex, endIndex);

        lessonsList.innerHTML = currentPageLessons.map((lesson, index) => `
            <div class="lesson-item ${this.selectedLessons.includes(lesson.id) ? 'selected' : ''}" data-lesson-id="${lesson.id}">
                <div class="lesson-header">
                    ${this.bulkActionsMode ? `<input type="checkbox" class="lesson-checkbox me-2" ${this.selectedLessons.includes(lesson.id) ? 'checked' : ''} onchange="contentEditor.toggleLessonSelection('${lesson.id}')">` : ''}
                    <i class="fas fa-grip-vertical drag-handle"></i>
                    <h6 class="lesson-title">${lesson.title}</h6>
                    <span class="lesson-type-badge">${this.getContentTypeLabel(lesson.content_type)}</span>
                    ${lesson.page ? `<span class="lesson-page-badge">صفحة ${lesson.page}</span>` : ''}
                </div>
                <div class="lesson-meta">
                    <small class="text-muted">
                        الترتيب: ${lesson.order} |
                        ${lesson.duration ? `المدة: ${Math.floor(lesson.duration / 60)} دقيقة` : 'بدون مدة'}
                    </small>
                </div>
                <div class="lesson-actions">
                    <button class="lesson-btn" onclick="contentEditor.editLesson('${lesson.id}')">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="lesson-btn" onclick="contentEditor.duplicateLesson('${lesson.id}')">
                        <i class="fas fa-copy"></i> نسخ
                    </button>
                    <button class="lesson-btn" onclick="contentEditor.moveToPage('${lesson.id}')">
                        <i class="fas fa-arrows-alt"></i> نقل
                    </button>
                    <button class="lesson-btn danger" onclick="contentEditor.deleteLesson('${lesson.id}')">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        `).join('');

        this.updateLessonsCount();
        this.updatePagesSelect();
    }
    
    getContentTypeLabel(contentType) {
        const labels = {
            'text': 'نص',
            'image': 'صورة',
            'video': 'فيديو',
            'mcq': 'أسئلة متعددة',
            'essay': 'سؤال مقالي'
        };
        return labels[contentType] || contentType;
    }

    updateLessonsCount() {
        const countElement = document.getElementById('lessonsCount');
        if (countElement) {
            const count = this.lessons.length;
            countElement.textContent = `${count} ${count === 1 ? 'درس' : 'دروس'}`;
        }
    }

    updatePagesSelect() {
        const select = document.getElementById('currentPageSelect');
        if (!select) return;

        const lessonsPerPage = 10;
        this.totalPages = Math.ceil(this.lessons.length / lessonsPerPage);

        let options = '';
        for (let i = 1; i <= Math.max(1, this.totalPages); i++) {
            options += `<option value="${i}" ${i === this.currentPage ? 'selected' : ''}>الصفحة ${i}</option>`;
        }

        select.innerHTML = options;
        select.onchange = (e) => {
            this.currentPage = parseInt(e.target.value);
            this.renderLessons();
        };
    }

    addNewPage() {
        this.totalPages++;
        this.currentPage = this.totalPages;
        this.updatePagesSelect();
        this.renderLessons();
        this.showAlert('تم إضافة صفحة جديدة', 'success');
    }

    showBulkActions() {
        this.bulkActionsMode = !this.bulkActionsMode;
        this.selectedLessons = [];

        if (this.bulkActionsMode) {
            this.showBulkActionsPanel();
        } else {
            this.hideBulkActionsPanel();
        }

        this.renderLessons();
    }

    showBulkActionsPanel() {
        const panel = document.createElement('div');
        panel.className = 'bulk-actions-panel active';
        panel.id = 'bulkActionsPanel';
        panel.innerHTML = `
            <h6><i class="fas fa-tasks me-2"></i>الإجراءات المتعددة</h6>
            <div class="d-flex gap-2 flex-wrap">
                <button class="btn btn-sm btn-outline-primary" onclick="contentEditor.selectAllLessons()">
                    <i class="fas fa-check-square me-1"></i>تحديد الكل
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="contentEditor.clearSelection()">
                    <i class="fas fa-square me-1"></i>إلغاء التحديد
                </button>
                <button class="btn btn-sm btn-outline-info" onclick="contentEditor.bulkMoveToPage()">
                    <i class="fas fa-arrows-alt me-1"></i>نقل المحدد
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="contentEditor.bulkDelete()">
                    <i class="fas fa-trash me-1"></i>حذف المحدد
                </button>
                <button class="btn btn-sm btn-secondary" onclick="contentEditor.showBulkActions()">
                    <i class="fas fa-times me-1"></i>إنهاء
                </button>
            </div>
            <div class="mt-2">
                <small class="text-muted">محدد: <span id="selectedCount">0</span> دروس</small>
            </div>
        `;

        const container = document.querySelector('.lessons-container');
        container.insertBefore(panel, document.getElementById('lessonsList'));
    }

    hideBulkActionsPanel() {
        const panel = document.getElementById('bulkActionsPanel');
        if (panel) {
            panel.remove();
        }
    }

    toggleLessonSelection(lessonId) {
        const index = this.selectedLessons.indexOf(lessonId);
        if (index > -1) {
            this.selectedLessons.splice(index, 1);
        } else {
            this.selectedLessons.push(lessonId);
        }

        this.updateSelectedCount();
    }

    selectAllLessons() {
        this.selectedLessons = this.lessons.map(lesson => lesson.id);
        this.renderLessons();
        this.updateSelectedCount();
    }

    clearSelection() {
        this.selectedLessons = [];
        this.renderLessons();
        this.updateSelectedCount();
    }

    updateSelectedCount() {
        const countElement = document.getElementById('selectedCount');
        if (countElement) {
            countElement.textContent = this.selectedLessons.length;
        }
    }

    async moveToPage(lessonId) {
        const lesson = this.lessons.find(l => l.id === lessonId);
        if (!lesson) return;

        const targetPage = prompt(`نقل الدرس "${lesson.title}" إلى صفحة رقم:`, this.currentPage);
        if (!targetPage || isNaN(targetPage) || targetPage < 1) return;

        try {
            const response = await fetchWithAuth(`/api/instructor/courses/${this.courseId}/lessons/${lessonId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    page: parseInt(targetPage)
                })
            });

            if (response.success) {
                this.showAlert('تم نقل الدرس بنجاح', 'success');
                await this.loadLessons();
            } else {
                this.showAlert('فشل في نقل الدرس: ' + response.message, 'danger');
            }
        } catch (error) {
            console.error('خطأ في نقل الدرس:', error);
            this.showAlert('حدث خطأ في نقل الدرس', 'danger');
        }
    }

    async bulkMoveToPage() {
        if (this.selectedLessons.length === 0) {
            this.showAlert('يرجى تحديد دروس للنقل', 'warning');
            return;
        }

        const targetPage = prompt(`نقل ${this.selectedLessons.length} دروس إلى صفحة رقم:`, this.currentPage);
        if (!targetPage || isNaN(targetPage) || targetPage < 1) return;

        try {
            const promises = this.selectedLessons.map(lessonId =>
                fetchWithAuth(`/api/instructor/courses/${this.courseId}/lessons/${lessonId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        page: parseInt(targetPage)
                    })
                })
            );

            await Promise.all(promises);
            this.showAlert('تم نقل الدروس بنجاح', 'success');
            this.clearSelection();
            await this.loadLessons();
        } catch (error) {
            console.error('خطأ في نقل الدروس:', error);
            this.showAlert('حدث خطأ في نقل الدروس', 'danger');
        }
    }

    async bulkDelete() {
        if (this.selectedLessons.length === 0) {
            this.showAlert('يرجى تحديد دروس للحذف', 'warning');
            return;
        }

        if (!confirm(`هل أنت متأكد من حذف ${this.selectedLessons.length} دروس؟ هذا الإجراء لا يمكن التراجع عنه.`)) {
            return;
        }

        try {
            const promises = this.selectedLessons.map(lessonId =>
                fetchWithAuth(`/api/instructor/courses/${this.courseId}/lessons/${lessonId}`, {
                    method: 'DELETE'
                })
            );

            await Promise.all(promises);
            this.showAlert('تم حذف الدروس بنجاح', 'success');
            this.clearSelection();
            await this.loadLessons();
        } catch (error) {
            console.error('خطأ في حذف الدروس:', error);
            this.showAlert('حدث خطأ في حذف الدروس', 'danger');
        }
    }
    
    async saveTextLesson(e) {
        e.preventDefault();
        
        const title = document.getElementById('textTitle').value.trim();
        const content = this.quillEditor.getContents();
        
        if (!title) {
            this.showError('عنوان الدرس مطلوب');
            return;
        }
        
        const lessonData = {
            title: title,
            content_type: 'text',
            content_data: {
                content: content,
                html: this.quillEditor.root.innerHTML
            }
        };
        
        await this.saveLesson(lessonData);
    }
    
    async saveImageLesson(e) {
        e.preventDefault();
        
        const title = document.getElementById('imageTitle').value.trim();
        const caption = document.getElementById('imageCaption').value.trim();
        const fileInput = document.getElementById('imageUpload');
        
        if (!title) {
            this.showError('عنوان الدرس مطلوب');
            return;
        }
        
        if (!fileInput.files[0]) {
            this.showError('يجب اختيار صورة');
            return;
        }
        
        try {
            this.showLoading(true);
            
            // رفع الصورة أولاً
            const imageUrl = await this.uploadImage(fileInput.files[0]);
            
            const lessonData = {
                title: title,
                content_type: 'image',
                content_data: {
                    image_url: imageUrl,
                    caption: caption,
                    alt_text: title
                }
            };
            
            await this.saveLesson(lessonData);
            
        } catch (error) {
            console.error('خطأ في حفظ درس الصورة:', error);
            this.showError('حدث خطأ في رفع الصورة');
        } finally {
            this.showLoading(false);
        }
    }
    
    async saveVideoLesson(e) {
        e.preventDefault();
        
        const title = document.getElementById('videoTitle').value.trim();
        const videoUrl = document.getElementById('videoUrl').value.trim();
        const description = document.getElementById('videoDescription').value.trim();
        const duration = parseInt(document.getElementById('videoDuration').value) || 0;
        
        if (!title || !videoUrl) {
            this.showError('عنوان الدرس ورابط الفيديو مطلوبان');
            return;
        }
        
        // استخراج معرف الفيديو من رابط YouTube
        const videoId = this.extractYouTubeId(videoUrl);
        if (!videoId) {
            this.showError('رابط YouTube غير صحيح');
            return;
        }
        
        const lessonData = {
            title: title,
            content_type: 'video',
            content_data: {
                youtube_id: videoId,
                description: description,
                duration: duration * 60 // تحويل إلى ثواني
            }
        };
        
        await this.saveLesson(lessonData);
    }
    
    extractYouTubeId(url) {
        const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
        const match = url.match(regExp);
        return (match && match[2].length === 11) ? match[2] : null;
    }
    
    showLoading(show) {
        const spinner = document.getElementById('loadingSpinner');
        spinner.style.display = show ? 'block' : 'none';
    }
    
    showError(message) {
        this.showMessage(message, 'error');
    }
    
    showSuccess(message) {
        this.showMessage(message, 'success');
    }
    
    showMessage(message, type) {
        const container = document.getElementById('messageContainer');
        const alertClass = type === 'error' ? 'error-message' : 'success-message';

        container.innerHTML = `
            <div class="${alertClass}">
                <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : 'check-circle'} me-2"></i>
                ${message}
            </div>
        `;

        // إخفاء الرسالة بعد 5 ثوان
        setTimeout(() => {
            container.innerHTML = '';
        }, 5000);
    }

    async saveMcqLesson(e) {
        e.preventDefault();

        const title = document.getElementById('mcqTitle').value.trim();
        const question = document.getElementById('mcqQuestion').value.trim();
        const feedback = document.getElementById('mcqFeedback').value.trim();
        const points = parseInt(document.getElementById('mcqPoints').value) || 1;

        if (!title || !question) {
            this.showError('عنوان الدرس ونص السؤال مطلوبان');
            return;
        }

        // جمع الخيارات
        const options = [];
        const correctAnswers = [];
        const optionInputs = document.querySelectorAll('#mcqOptions .option-input');

        optionInputs.forEach((optionDiv, index) => {
            const textInput = optionDiv.querySelector('input[type="text"]');
            const checkbox = optionDiv.querySelector('input[type="checkbox"]');

            if (textInput.value.trim()) {
                options.push(textInput.value.trim());
                if (checkbox.checked) {
                    correctAnswers.push(index);
                }
            }
        });

        if (options.length < 2) {
            this.showError('يجب إضافة خيارين على الأقل');
            return;
        }

        if (correctAnswers.length === 0) {
            this.showError('يجب تحديد إجابة صحيحة واحدة على الأقل');
            return;
        }

        const lessonData = {
            title: title,
            content_type: 'mcq',
            content_data: {
                question: question,
                options: options,
                correct_answers: correctAnswers,
                feedback: feedback,
                points: points
            }
        };

        await this.saveLesson(lessonData);
    }

    async saveEssayLesson(e) {
        e.preventDefault();

        const title = document.getElementById('essayTitle').value.trim();
        const question = document.getElementById('essayQuestion').value.trim();
        const maxWords = parseInt(document.getElementById('essayMaxWords').value) || 500;
        const rubric = document.getElementById('essayRubric').value.trim();
        const points = parseInt(document.getElementById('essayPoints').value) || 10;

        if (!title || !question) {
            this.showError('عنوان الدرس ونص السؤال مطلوبان');
            return;
        }

        const lessonData = {
            title: title,
            content_type: 'essay',
            content_data: {
                question: question,
                max_words: maxWords,
                rubric: rubric,
                points: points
            }
        };

        await this.saveLesson(lessonData);
    }

    async saveLesson(lessonData) {
        try {
            this.showLoading(true);

            const url = this.currentLessonId ?
                `/api/instructor/courses/${this.courseId}/lessons/${this.currentLessonId}` :
                `/api/instructor/courses/${this.courseId}/lessons`;

            const method = this.currentLessonId ? 'PUT' : 'POST';

            // إضافة ترتيب الدرس
            if (!this.currentLessonId) {
                lessonData.order = this.lessons.length + 1;
            }

            const response = await fetchWithAuth(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(lessonData)
            });

            if (response.success) {
                this.showSuccess(this.currentLessonId ? 'تم تحديث الدرس بنجاح' : 'تم إضافة الدرس بنجاح');

                // إعادة تحميل الدروس
                await this.loadLessons();

                // إخفاء المحرر
                cancelEdit();

            } else {
                throw new Error(response.message || 'فشل في حفظ الدرس');
            }

        } catch (error) {
            console.error('خطأ في حفظ الدرس:', error);
            this.showError('حدث خطأ في حفظ الدرس');
        } finally {
            this.showLoading(false);
        }
    }

    async uploadImage(file) {
        const formData = new FormData();
        formData.append('image', file);
        formData.append('course_id', this.courseId);

        const response = await fetchWithAuth('/api/instructor/upload-image', {
            method: 'POST',
            body: formData
        });

        if (response.success) {
            return response.image_url;
        } else {
            throw new Error(response.message || 'فشل في رفع الصورة');
        }
    }

    async editLesson(lessonId) {
        const lesson = this.lessons.find(l => l.id === lessonId);
        if (!lesson) return;

        this.currentLessonId = lessonId;
        this.showContentEditor(lesson.content_type);

        // ملء النموذج بالبيانات الموجودة
        this.fillFormWithLessonData(lesson);
    }

    fillFormWithLessonData(lesson) {
        const contentData = lesson.content_data;

        switch (lesson.content_type) {
            case 'text':
                document.getElementById('textTitle').value = lesson.title;
                if (this.quillEditor && contentData.content) {
                    this.quillEditor.setContents(contentData.content);
                }
                break;

            case 'image':
                document.getElementById('imageTitle').value = lesson.title;
                document.getElementById('imageCaption').value = contentData.caption || '';
                if (contentData.image_url) {
                    document.getElementById('previewImg').src = contentData.image_url;
                    document.getElementById('imagePreview').style.display = 'block';
                }
                break;

            case 'video':
                document.getElementById('videoTitle').value = lesson.title;
                document.getElementById('videoUrl').value = `https://www.youtube.com/watch?v=${contentData.youtube_id}`;
                document.getElementById('videoDescription').value = contentData.description || '';
                document.getElementById('videoDuration').value = Math.floor((contentData.duration || 0) / 60);
                break;

            case 'mcq':
                document.getElementById('mcqTitle').value = lesson.title;
                document.getElementById('mcqQuestion').value = contentData.question;
                document.getElementById('mcqFeedback').value = contentData.feedback || '';
                document.getElementById('mcqPoints').value = contentData.points || 1;
                this.fillMcqOptions(contentData.options, contentData.correct_answers);
                break;

            case 'essay':
                document.getElementById('essayTitle').value = lesson.title;
                document.getElementById('essayQuestion').value = contentData.question;
                document.getElementById('essayMaxWords').value = contentData.max_words || 500;
                document.getElementById('essayRubric').value = contentData.rubric || '';
                document.getElementById('essayPoints').value = contentData.points || 10;
                break;
        }
    }

    fillMcqOptions(options, correctAnswers) {
        const optionsContainer = document.getElementById('mcqOptions');
        optionsContainer.innerHTML = '';

        options.forEach((option, index) => {
            const optionDiv = document.createElement('div');
            optionDiv.className = 'option-input';
            optionDiv.innerHTML = `
                <input type="text" class="form-control" value="${option}" required>
                <input type="checkbox" class="form-check-input" title="إجابة صحيحة" ${correctAnswers.includes(index) ? 'checked' : ''}>
                <button type="button" class="remove-option-btn" onclick="removeOption(this)">
                    <i class="fas fa-times"></i>
                </button>
            `;
            optionsContainer.appendChild(optionDiv);
        });

        this.updateRemoveButtons();
    }

    updateRemoveButtons() {
        const optionInputs = document.querySelectorAll('#mcqOptions .option-input');
        optionInputs.forEach((optionDiv, index) => {
            const removeBtn = optionDiv.querySelector('.remove-option-btn');
            removeBtn.style.display = optionInputs.length > 2 ? 'inline-block' : 'none';
        });
    }

    async duplicateLesson(lessonId) {
        const lesson = this.lessons.find(l => l.id === lessonId);
        if (!lesson) return;

        const duplicatedLesson = {
            ...lesson,
            title: lesson.title + ' (نسخة)',
            order: this.lessons.length + 1
        };

        delete duplicatedLesson.id;

        await this.saveLesson(duplicatedLesson);
    }

    async deleteLesson(lessonId) {
        if (!confirm('هل أنت متأكد من حذف هذا الدرس؟')) {
            return;
        }

        try {
            this.showLoading(true);

            const response = await fetchWithAuth(`/api/instructor/courses/${this.courseId}/lessons/${lessonId}`, {
                method: 'DELETE'
            });

            if (response.success) {
                this.showSuccess('تم حذف الدرس بنجاح');
                await this.loadLessons();
            } else {
                throw new Error(response.message || 'فشل في حذف الدرس');
            }

        } catch (error) {
            console.error('خطأ في حذف الدرس:', error);
            this.showError('حدث خطأ في حذف الدرس');
        } finally {
            this.showLoading(false);
        }
    }

    async updateLessonsOrder() {
        const lessonItems = document.querySelectorAll('.lesson-item');
        const newOrder = Array.from(lessonItems).map((item, index) => ({
            id: item.dataset.lessonId,
            order: index + 1
        }));

        try {
            const response = await fetchWithAuth(`/api/instructor/courses/${this.courseId}/lessons/reorder`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ lessons: newOrder })
            });

            if (response.success) {
                // تحديث الترتيب محلياً
                this.lessons.forEach(lesson => {
                    const newOrderItem = newOrder.find(item => item.id === lesson.id);
                    if (newOrderItem) {
                        lesson.order = newOrderItem.order;
                    }
                });

                this.lessons.sort((a, b) => a.order - b.order);
            }

        } catch (error) {
            console.error('خطأ في تحديث ترتيب الدروس:', error);
            // إعادة تحميل الدروس في حالة الخطأ
            await this.loadLessons();
        }
    }

    showAlert(message, type = 'info') {
        // إنشاء رسالة تنبيه
        const alertClass = type === 'danger' ? 'alert-danger' :
                          type === 'success' ? 'alert-success' :
                          type === 'warning' ? 'alert-warning' : 'alert-info';

        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // إضافة الرسالة في حاوية الرسائل
        const messageContainer = document.getElementById('messageContainer');
        if (messageContainer) {
            messageContainer.innerHTML = alertHtml;

            // إزالة الرسالة بعد 5 ثوان
            setTimeout(() => {
                const alert = messageContainer.querySelector('.alert');
                if (alert) {
                    alert.classList.remove('show');
                    setTimeout(() => {
                        alert.remove();
                    }, 150);
                }
            }, 5000);
        }
    }
}

// متغير عام للوصول من HTML
let contentEditor;

// تهيئة المحرر عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    contentEditor = new ContentEditor();
});

// وظائف عامة يمكن استدعاؤها من HTML
function cancelEdit() {
    // إخفاء جميع المحررات
    document.querySelectorAll('.content-editor-panel').forEach(panel => {
        panel.classList.remove('active');
    });
    
    // إزالة التفعيل من جميع الأزرار
    document.querySelectorAll('.content-type-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    contentEditor.currentContentType = null;
    contentEditor.currentLessonId = null;
}

function addMcqOption() {
    const optionsContainer = document.getElementById('mcqOptions');
    const optionDiv = document.createElement('div');
    optionDiv.className = 'option-input';
    optionDiv.innerHTML = `
        <input type="text" class="form-control" placeholder="خيار جديد" required>
        <input type="checkbox" class="form-check-input" title="إجابة صحيحة">
        <button type="button" class="remove-option-btn" onclick="removeOption(this)">
            <i class="fas fa-times"></i>
        </button>
    `;
    optionsContainer.appendChild(optionDiv);
    contentEditor.updateRemoveButtons();
}

function removeOption(button) {
    button.closest('.option-input').remove();
    contentEditor.updateRemoveButtons();
}

function saveDraft() {
    // حفظ الكورس كمسودة
    console.log('حفظ مسودة');
}

function publishCourse() {
    // نشر الكورس
    console.log('نشر الكورس');
}
