/* لوحة التحليلات الشاملة */

/* الفلاتر */
.analytics-filters {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.analytics-filters .form-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.analytics-filters .form-select {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.analytics-filters .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* بطاقات الإحصائيات السريعة */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 2rem;
    color: white;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.stats-card.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-card.success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.stats-card.warning {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.stats-card.info {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
}

.stats-card .stats-icon {
    position: absolute;
    top: 1.5rem;
    left: 1.5rem;
    font-size: 2.5rem;
    opacity: 0.3;
}

.stats-card .stats-content {
    position: relative;
    z-index: 2;
}

.stats-card .stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stats-card .stats-label {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

.stats-card .stats-change {
    font-size: 0.875rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    display: inline-block;
}

/* بطاقات الرسوم البيانية */
.chart-card, .table-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.chart-card:hover, .table-card:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.chart-header, .table-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f8f9fa;
}

.chart-header h5, .table-header h5 {
    margin: 0;
    color: #2c3e50;
    font-weight: 600;
}

.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-controls .btn {
    border-radius: 20px;
    padding: 0.375rem 1rem;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.chart-controls .btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: transparent;
    color: white;
}

.chart-container {
    position: relative;
    height: 400px;
    width: 100%;
}

/* التحليلات المتقدمة */
.advanced-analytics {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.analytics-tabs .nav-tabs {
    border-bottom: 2px solid #f8f9fa;
    margin-bottom: 2rem;
}

.analytics-tabs .nav-link {
    border: none;
    border-radius: 15px 15px 0 0;
    padding: 1rem 1.5rem;
    color: #6c757d;
    font-weight: 600;
    transition: all 0.3s ease;
}

.analytics-tabs .nav-link:hover {
    background: #f8f9fa;
    color: #495057;
}

.analytics-tabs .nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
}

.tab-content {
    min-height: 400px;
}

/* الجداول */
.table-responsive {
    border-radius: 15px;
    overflow: hidden;
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    font-weight: 600;
    color: #495057;
    padding: 1rem;
}

.table tbody td {
    padding: 1rem;
    border-top: 1px solid #f8f9fa;
    vertical-align: middle;
}

.table tbody tr:hover {
    background: #f8f9fa;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
}

.loading-spinner .spinner-border {
    width: 3rem;
    height: 3rem;
}

.loading-spinner p {
    color: #6c757d;
    font-weight: 600;
    margin: 0;
}

/* الرسوم البيانية المخصصة */
.chart-legend {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

/* إحصائيات النشاط */
#activityStats {
    padding: 1rem;
}

.activity-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.activity-stat:last-child {
    border-bottom: none;
}

.activity-stat-label {
    font-weight: 600;
    color: #495057;
}

.activity-stat-value {
    font-weight: 700;
    color: #667eea;
}

/* رؤى المحتوى */
#contentInsights {
    padding: 1rem;
}

.insight-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.insight-title {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.insight-description {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.insight-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #667eea;
}

/* التجاوب */
@media (max-width: 768px) {
    .analytics-filters {
        padding: 1rem;
    }
    
    .stats-card {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .stats-card .stats-number {
        font-size: 2rem;
    }
    
    .chart-card, .table-card {
        padding: 1.5rem;
    }
    
    .chart-container {
        height: 300px;
    }
    
    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .chart-controls {
        width: 100%;
        justify-content: center;
    }
    
    .analytics-tabs .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding: 1rem;
    }
    
    .stats-card .stats-icon {
        display: none;
    }
    
    .chart-controls .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
    }
}

/* تحسينات الأداء */
.chart-card {
    contain: layout style paint;
}

.stats-card {
    will-change: transform;
}

/* تأثيرات الحركة */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stats-card, .chart-card, .table-card {
    animation: fadeInUp 0.6s ease-out;
}

.stats-card:nth-child(1) { animation-delay: 0.1s; }
.stats-card:nth-child(2) { animation-delay: 0.2s; }
.stats-card:nth-child(3) { animation-delay: 0.3s; }
.stats-card:nth-child(4) { animation-delay: 0.4s; }
