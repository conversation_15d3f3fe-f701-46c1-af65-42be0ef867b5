/**
 * Student Management System
 * إدارة الطلاب للمدرسين
 */

class StudentManager {
    constructor(options = {}) {
        this.options = {
            apiEndpoint: '/api/instructor/students',
            pageSize: 10,
            ...options
        };

        this.students = [];
        this.filteredStudents = [];
        this.currentPage = 1;
        this.totalPages = 1;
        this.selectedStudents = new Set();
        this.courses = [];

        this.initializeEventListeners();
    }

    /**
     * تهيئة مستمعي الأحداث
     */
    initializeEventListeners() {
        // البحث والفلاتر
        $('#searchInput').on('input', () => this.applyFilters());
        $('#statusFilter, #courseFilter, #progressFilter').on('change', () => this.applyFilters());
        $('#clearFiltersBtn').on('click', () => this.clearFilters());

        // أزرار التحكم
        $('#refreshStudentsBtn').on('click', () => this.loadStudents());
        $('#exportStudentsBtn').on('click', () => this.exportStudents());

        // تحديد الطلاب
        $('#selectAllCheckbox').on('change', (e) => this.toggleSelectAll(e.target.checked));
        $('#selectAllBtn').on('click', () => this.selectAll());

        // الإجراءات الجماعية
        $('#bulkActivateBtn').on('click', () => this.bulkAction('activate'));
        $('#bulkDeactivateBtn').on('click', () => this.bulkAction('deactivate'));
        $('#bulkDeleteBtn').on('click', () => this.bulkAction('delete'));

        // تعديل الطالب
        $('#editStudentBtn').on('click', () => this.showEditModal());
        $('#editStudentForm').on('submit', (e) => this.saveStudentChanges(e));
        $('#generatePasswordBtn').on('click', () => this.generatePassword());
        $('#togglePasswordBtn').on('click', () => this.togglePasswordVisibility());

        // أحداث الجدول
        $(document).on('click', '.view-student-btn', (e) => {
            const studentId = $(e.target).closest('button').data('student-id');
            this.showStudentDetails(studentId);
        });

        $(document).on('click', '.edit-student-btn', (e) => {
            const studentId = $(e.target).closest('button').data('student-id');
            this.editStudent(studentId);
        });

        $(document).on('click', '.toggle-status-btn', (e) => {
            const studentId = $(e.target).closest('button').data('student-id');
            this.toggleStudentStatus(studentId);
        });

        $(document).on('click', '.reset-password-btn', (e) => {
            const studentId = $(e.target).closest('button').data('student-id');
            this.resetStudentPassword(studentId);
        });

        $(document).on('change', '.student-checkbox', () => this.updateSelectedCount());
    }

    /**
     * تهيئة النظام
     */
    async initialize() {
        try {
            this.showLoading(true);
            await this.loadCourses();
            await this.loadStudents();
            this.updateStatistics();
        } catch (error) {
            console.error('خطأ في تهيئة النظام:', error);
            this.showError('حدث خطأ في تحميل البيانات');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * تحميل قائمة الكورسات
     */
    async loadCourses() {
        try {
            const response = await fetch('/api/instructor/courses');
            const data = await response.json();

            if (data.success) {
                this.courses = data.courses;
                this.populateCourseFilter();
            }
        } catch (error) {
            console.error('خطأ في تحميل الكورسات:', error);
        }
    }

    /**
     * ملء فلتر الكورسات
     */
    populateCourseFilter() {
        const courseFilter = $('#courseFilter');
        courseFilter.find('option:not(:first)').remove();

        this.courses.forEach(course => {
            courseFilter.append(`
                <option value="${course.id}">${course.title}</option>
            `);
        });
    }

    /**
     * تحميل قائمة الطلاب
     */
    async loadStudents() {
        try {
            this.showLoading(true);

            const response = await fetch(this.options.apiEndpoint);
            const data = await response.json();

            if (data.success) {
                this.students = data.students;
                this.filteredStudents = [...this.students];
                this.renderStudents();
                this.updateStatistics();
                this.updatePagination();
            } else {
                this.showError(data.message || 'فشل في تحميل البيانات');
            }
        } catch (error) {
            console.error('خطأ في تحميل الطلاب:', error);
            this.showError('حدث خطأ في الاتصال بالخادم');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * تطبيق الفلاتر
     */
    applyFilters() {
        const searchTerm = $('#searchInput').val().toLowerCase();
        const statusFilter = $('#statusFilter').val();
        const courseFilter = $('#courseFilter').val();
        const progressFilter = $('#progressFilter').val();

        this.filteredStudents = this.students.filter(student => {
            // فلتر البحث
            const matchesSearch = !searchTerm ||
                student.full_name.toLowerCase().includes(searchTerm) ||
                student.email.toLowerCase().includes(searchTerm);

            // فلتر الحالة
            const matchesStatus = statusFilter === 'all' ||
                (statusFilter === 'active' && student.active) ||
                (statusFilter === 'inactive' && !student.active) ||
                (statusFilter === 'blocked' && student.blocked);

            // فلتر الكورس
            const matchesCourse = courseFilter === 'all' ||
                (student.enrollments && student.enrollments.some(e => e.course_id === courseFilter));

            // فلتر التقدم
            const matchesProgress = progressFilter === 'all' || this.matchesProgressFilter(student, progressFilter);

            return matchesSearch && matchesStatus && matchesCourse && matchesProgress;
        });

        this.currentPage = 1;
        this.renderStudents();
        this.updatePagination();
        this.updateStatistics();
    }

    /**
     * فحص فلتر التقدم
     */
    matchesProgressFilter(student, progressFilter) {
        const avgProgress = this.calculateAverageProgress(student);

        switch (progressFilter) {
            case '0-25':
                return avgProgress >= 0 && avgProgress <= 25;
            case '26-50':
                return avgProgress >= 26 && avgProgress <= 50;
            case '51-75':
                return avgProgress >= 51 && avgProgress <= 75;
            case '76-100':
                return avgProgress >= 76 && avgProgress <= 100;
            default:
                return true;
        }
    }

    /**
     * حساب متوسط التقدم للطالب
     */
    calculateAverageProgress(student) {
        if (!student.enrollments || student.enrollments.length === 0) {
            return 0;
        }

        const totalProgress = student.enrollments.reduce((sum, enrollment) => {
            return sum + (enrollment.progress?.completion_percentage || 0);
        }, 0);

        return Math.round(totalProgress / student.enrollments.length);
    }

    /**
     * مسح الفلاتر
     */
    clearFilters() {
        $('#searchInput').val('');
        $('#statusFilter').val('all');
        $('#courseFilter').val('all');
        $('#progressFilter').val('all');
        this.applyFilters();
    }

    /**
     * عرض الطلاب في الجدول
     */
    renderStudents() {
        const tbody = $('#studentsTable tbody');
        tbody.empty();

        if (this.filteredStudents.length === 0) {
            tbody.append(`
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد بيانات طلاب</p>
                    </td>
                </tr>
            `);
            return;
        }

        const startIndex = (this.currentPage - 1) * this.options.pageSize;
        const endIndex = startIndex + this.options.pageSize;
        const pageStudents = this.filteredStudents.slice(startIndex, endIndex);

        pageStudents.forEach(student => {
            tbody.append(this.createStudentRow(student));
        });

        // تحديث عداد العرض
        $('#showingCount').text(pageStudents.length);
        $('#totalCount').text(this.filteredStudents.length);
    }

    /**
     * إنشاء صف طالب في الجدول
     */
    createStudentRow(student) {
        const avgProgress = this.calculateAverageProgress(student);
        const statusClass = student.active ? 'active' : (student.blocked ? 'blocked' : 'inactive');
        const statusText = student.active ? 'نشط' : (student.blocked ? 'محظور' : 'غير نشط');

        const initials = student.full_name.split(' ').map(name => name.charAt(0)).join('').substring(0, 2);

        return `
            <tr data-student-id="${student.id}" class="fade-in-up">
                <td>
                    <input type="checkbox" class="form-check-input student-checkbox" value="${student.id}">
                </td>
                <td>
                    <div class="student-info">
                        <div class="student-avatar">${initials}</div>
                        <div class="student-details">
                            <h6>${student.full_name}</h6>
                            <small>انضم في ${this.formatDate(student.created_at)}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="text-muted">${student.email}</span>
                </td>
                <td>
                    <span class="badge bg-primary">${student.enrollments ? student.enrollments.length : 0} كورس</span>
                </td>
                <td>
                    <div class="progress-container">
                        <div class="progress">
                            <div class="progress-bar" style="width: ${avgProgress}%"></div>
                        </div>
                        <div class="progress-text">${avgProgress}%</div>
                    </div>
                </td>
                <td>
                    <span class="status-badge ${statusClass}">${statusText}</span>
                </td>
                <td>
                    <small class="text-muted">${this.formatDate(student.last_login || student.updated_at)}</small>
                </td>
                <td>
                    <div class="d-flex">
                        <button class="action-btn btn-info view-student-btn" data-student-id="${student.id}" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="action-btn btn-warning edit-student-btn" data-student-id="${student.id}" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn ${student.active ? 'btn-danger' : 'btn-success'} toggle-status-btn"
                                data-student-id="${student.id}" title="${student.active ? 'إلغاء التفعيل' : 'تفعيل'}">
                            <i class="fas ${student.active ? 'fa-user-times' : 'fa-user-check'}"></i>
                        </button>
                        <button class="action-btn btn-secondary reset-password-btn" data-student-id="${student.id}" title="إعادة تعيين كلمة المرور">
                            <i class="fas fa-key"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * تحديث الإحصائيات
     */
    updateStatistics() {
        const total = this.students.length;
        const active = this.students.filter(s => s.active && !s.blocked).length;
        const inactive = this.students.filter(s => !s.active || s.blocked).length;

        const totalProgress = this.students.reduce((sum, student) => {
            return sum + this.calculateAverageProgress(student);
        }, 0);
        const avgProgress = total > 0 ? Math.round(totalProgress / total) : 0;

        $('#totalStudents').text(total);
        $('#activeStudents').text(active);
        $('#inactiveStudents').text(inactive);
        $('#avgProgress').text(`${avgProgress}%`);
    }

    /**
     * تحديث الترقيم
     */
    updatePagination() {
        this.totalPages = Math.ceil(this.filteredStudents.length / this.options.pageSize);
        const pagination = $('#pagination');
        pagination.empty();

        if (this.totalPages <= 1) return;

        // زر السابق
        pagination.append(`
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${this.currentPage - 1}">السابق</a>
            </li>
        `);

        // أرقام الصفحات
        for (let i = 1; i <= this.totalPages; i++) {
            if (i === 1 || i === this.totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                pagination.append(`
                    <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" data-page="${i}">${i}</a>
                    </li>
                `);
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                pagination.append(`<li class="page-item disabled"><span class="page-link">...</span></li>`);
            }
        }

        // زر التالي
        pagination.append(`
            <li class="page-item ${this.currentPage === this.totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" data-page="${this.currentPage + 1}">التالي</a>
            </li>
        `);

        // ربط الأحداث
        pagination.find('a.page-link').on('click', (e) => {
            e.preventDefault();
            const page = parseInt($(e.target).data('page'));
            if (page && page !== this.currentPage) {
                this.currentPage = page;
                this.renderStudents();
                this.updatePagination();
            }
        });
    }

    /**
     * عرض تفاصيل الطالب
     */
    async showStudentDetails(studentId) {
        const student = this.students.find(s => s.id === studentId);
        if (!student) return;

        // ملء البيانات الأساسية
        $('#modalStudentName').text(student.full_name);
        $('#modalStudentEmail').text(student.email);
        $('#modalStudentJoinDate').text(this.formatDate(student.created_at));
        $('#modalStudentLastActivity').text(this.formatDate(student.last_login || student.updated_at));

        const statusText = student.active ? 'نشط' : (student.blocked ? 'محظور' : 'غير نشط');
        const statusClass = student.active ? 'text-success' : (student.blocked ? 'text-danger' : 'text-warning');
        $('#modalStudentStatus').html(`<span class="${statusClass}">${statusText}</span>`);

        // ملء بيانات الكورسات
        const coursesContainer = $('#modalStudentCourses');
        coursesContainer.empty();

        if (student.enrollments && student.enrollments.length > 0) {
            student.enrollments.forEach(enrollment => {
                const course = this.courses.find(c => c.id === enrollment.course_id);
                const courseName = course ? course.title : 'كورس غير معروف';
                const progress = enrollment.progress?.completion_percentage || 0;

                coursesContainer.append(`
                    <div class="course-progress-item">
                        <div class="course-title">${courseName}</div>
                        <div class="course-progress">
                            <div class="progress">
                                <div class="progress-bar" style="width: ${progress}%"></div>
                            </div>
                            <span class="course-progress-text">${progress}%</span>
                        </div>
                    </div>
                `);
            });
        } else {
            coursesContainer.append(`
                <div class="text-center text-muted py-3">
                    <i class="fas fa-book-open fa-2x mb-2"></i>
                    <p>لم يسجل في أي كورس بعد</p>
                </div>
            `);
        }

        // حفظ معرف الطالب للتعديل
        $('#editStudentBtn').data('student-id', studentId);

        // عرض النافذة
        $('#studentDetailsModal').modal('show');
    }

    /**
     * عرض نافذة التعديل
     */
    showEditModal() {
        const studentId = $('#editStudentBtn').data('student-id');
        this.editStudent(studentId);
    }

    /**
     * تعديل بيانات الطالب
     */
    editStudent(studentId) {
        const student = this.students.find(s => s.id === studentId);
        if (!student) return;

        // ملء النموذج
        $('#editStudentId').val(student.id);
        $('#editFirstName').val(student.first_name);
        $('#editLastName').val(student.last_name || '');
        $('#editEmail').val(student.email);
        $('#editPassword').val('');
        $('#editStatus').val(student.active.toString());

        // إخفاء نافذة التفاصيل وعرض نافذة التعديل
        $('#studentDetailsModal').modal('hide');
        $('#editStudentModal').modal('show');
    }

    /**
     * حفظ تغييرات الطالب
     */
    async saveStudentChanges(e) {
        e.preventDefault();

        const studentId = $('#editStudentId').val();
        const formData = {
            first_name: $('#editFirstName').val().trim(),
            last_name: $('#editLastName').val().trim(),
            email: $('#editEmail').val().trim(),
            active: $('#editStatus').val() === 'true'
        };

        const newPassword = $('#editPassword').val().trim();
        if (newPassword) {
            formData.password = newPassword;
        }

        try {
            this.showLoading(true);

            const response = await fetch(`/api/instructor/students/${studentId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('تم تحديث بيانات الطالب بنجاح');
                $('#editStudentModal').modal('hide');
                await this.loadStudents();
            } else {
                this.showError(data.message || 'فشل في تحديث البيانات');
            }
        } catch (error) {
            console.error('خطأ في تحديث الطالب:', error);
            this.showError('حدث خطأ في الاتصال بالخادم');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * تبديل حالة الطالب
     */
    async toggleStudentStatus(studentId) {
        const student = this.students.find(s => s.id === studentId);
        if (!student) return;

        const action = student.active ? 'deactivate' : 'activate';
        const message = student.active ? 'إلغاء تفعيل' : 'تفعيل';

        if (!confirm(`هل أنت متأكد من ${message} هذا الطالب؟`)) return;

        try {
            this.showLoading(true);

            const response = await fetch(`/api/instructor/students/${studentId}/${action}`, {
                method: 'POST'
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess(`تم ${message} الطالب بنجاح`);
                await this.loadStudents();
            } else {
                this.showError(data.message || `فشل في ${message} الطالب`);
            }
        } catch (error) {
            console.error('خطأ في تغيير حالة الطالب:', error);
            this.showError('حدث خطأ في الاتصال بالخادم');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * إعادة تعيين كلمة مرور الطالب
     */
    async resetStudentPassword(studentId) {
        const student = this.students.find(s => s.id === studentId);
        if (!student) return;

        if (!confirm(`هل أنت متأكد من إعادة تعيين كلمة مرور الطالب ${student.full_name}؟\nسيتم إرسال كلمة المرور الجديدة عبر التليجرام.`)) return;

        try {
            this.showLoading(true);

            const response = await fetch(`/api/instructor/students/${studentId}/reset-password`, {
                method: 'POST'
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('تم إعادة تعيين كلمة المرور وإرسالها للطالب');
            } else {
                this.showError(data.message || 'فشل في إعادة تعيين كلمة المرور');
            }
        } catch (error) {
            console.error('خطأ في إعادة تعيين كلمة المرور:', error);
            this.showError('حدث خطأ في الاتصال بالخادم');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * تحديد/إلغاء تحديد جميع الطلاب
     */
    toggleSelectAll(checked) {
        $('.student-checkbox').prop('checked', checked);
        this.updateSelectedCount();
    }

    /**
     * تحديد جميع الطلاب
     */
    selectAll() {
        const allChecked = $('.student-checkbox:checked').length === $('.student-checkbox').length;
        this.toggleSelectAll(!allChecked);
    }

    /**
     * تحديث عدد الطلاب المحددين
     */
    updateSelectedCount() {
        this.selectedStudents.clear();
        $('.student-checkbox:checked').each((i, checkbox) => {
            this.selectedStudents.add($(checkbox).val());
        });

        // تحديث حالة checkbox الرئيسي
        const totalCheckboxes = $('.student-checkbox').length;
        const checkedCheckboxes = $('.student-checkbox:checked').length;

        $('#selectAllCheckbox').prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);
        $('#selectAllCheckbox').prop('checked', checkedCheckboxes === totalCheckboxes && totalCheckboxes > 0);
    }

    /**
     * تنفيذ إجراء جماعي
     */
    async bulkAction(action) {
        if (this.selectedStudents.size === 0) {
            this.showError('يرجى تحديد طالب واحد على الأقل');
            return;
        }

        const actionText = {
            'activate': 'تفعيل',
            'deactivate': 'إلغاء تفعيل',
            'delete': 'حذف'
        };

        const message = `هل أنت متأكد من ${actionText[action]} ${this.selectedStudents.size} طالب؟`;
        if (!confirm(message)) return;

        try {
            this.showLoading(true);

            const response = await fetch(`/api/instructor/students/bulk-${action}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    student_ids: Array.from(this.selectedStudents)
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess(`تم ${actionText[action]} الطلاب المحددين بنجاح`);
                this.selectedStudents.clear();
                await this.loadStudents();
            } else {
                this.showError(data.message || `فشل في ${actionText[action]} الطلاب`);
            }
        } catch (error) {
            console.error('خطأ في الإجراء الجماعي:', error);
            this.showError('حدث خطأ في الاتصال بالخادم');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * تصدير بيانات الطلاب
     */
    async exportStudents() {
        try {
            this.showLoading(true);

            const response = await fetch('/api/instructor/students/export');

            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `students-${new Date().toISOString().split('T')[0]}.xlsx`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                this.showSuccess('تم تصدير البيانات بنجاح');
            } else {
                this.showError('فشل في تصدير البيانات');
            }
        } catch (error) {
            console.error('خطأ في تصدير البيانات:', error);
            this.showError('حدث خطأ في تصدير البيانات');
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * توليد كلمة مرور عشوائية
     */
    generatePassword() {
        const length = 8;
        const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        let password = "";

        for (let i = 0; i < length; i++) {
            password += charset.charAt(Math.floor(Math.random() * charset.length));
        }

        $('#editPassword').val(password);
    }

    /**
     * تبديل إظهار/إخفاء كلمة المرور
     */
    togglePasswordVisibility() {
        const passwordField = $('#editPassword');
        const toggleBtn = $('#togglePasswordBtn i');

        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            toggleBtn.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            toggleBtn.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    }

    /**
     * تنسيق التاريخ
     */
    formatDate(dateString) {
        if (!dateString) return 'غير محدد';

        const date = new Date(dateString);
        const now = new Date();
        const diffTime = Math.abs(now - date);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays === 1) {
            return 'أمس';
        } else if (diffDays < 7) {
            return `منذ ${diffDays} أيام`;
        } else if (diffDays < 30) {
            const weeks = Math.floor(diffDays / 7);
            return `منذ ${weeks} ${weeks === 1 ? 'أسبوع' : 'أسابيع'}`;
        } else {
            return date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }
    }

    /**
     * عرض رسالة تحميل
     */
    showLoading(show) {
        if (show) {
            $('#loadingOverlay').fadeIn(300);
        } else {
            $('#loadingOverlay').fadeOut(300);
        }
    }

    /**
     * عرض رسالة نجاح
     */
    showSuccess(message) {
        // يمكن استخدام مكتبة toast أو alert
        alert(message);
    }

    /**
     * عرض رسالة خطأ
     */
    showError(message) {
        // يمكن استخدام مكتبة toast أو alert
        alert(message);
    }
}

// تصدير الكلاس للاستخدام العام
window.StudentManager = StudentManager;