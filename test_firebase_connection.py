#!/usr/bin/env python3
"""
اختبار الاتصال بـ Firebase
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.firebase_utils import get_firebase_manager
from config import Config

def test_firebase_connection():
    """اختبار الاتصال بـ Firebase"""
    
    print("🔍 اختبار الاتصال بـ Firebase...")
    
    try:
        # الحصول على مدير Firebase
        firebase_manager = get_firebase_manager()
        
        if not firebase_manager:
            print("❌ فشل في الحصول على مدير Firebase")
            return False
        
        print("✅ تم الحصول على مدير Firebase")
        
        # التحقق من التهيئة
        print(f"🔍 حالة التهيئة: {firebase_manager._initialized}")
        
        if not firebase_manager._initialized:
            print("🔧 محاولة تهيئة Firebase...")
            config = Config()
            success = firebase_manager.initialize(config.__dict__)
            print(f"🔍 نتيجة التهيئة: {success}")
        
        # التحقق من قاعدة البيانات
        if firebase_manager.database:
            print("✅ قاعدة البيانات متصلة")
            
            # اختبار قراءة بسيطة
            try:
                test_data = firebase_manager.database.child('test').get()
                print("✅ تم اختبار القراءة بنجاح")
                
                # اختبار كتابة بسيطة
                firebase_manager.database.child('test').child('connection_test').set({
                    'timestamp': '2025-01-03',
                    'status': 'connected'
                })
                print("✅ تم اختبار الكتابة بنجاح")
                
            except Exception as e:
                print(f"❌ فشل في اختبار قاعدة البيانات: {e}")
                return False
        else:
            print("❌ قاعدة البيانات غير متصلة")
            return False
        
        # اختبار جلب المستخدمين
        try:
            users = firebase_manager.database.child('users').get()
            if users:
                print(f"📊 عدد المستخدمين في قاعدة البيانات: {len(users)}")
                
                # عرض أول 3 مستخدمين
                count = 0
                for user_id, user_data in users.items():
                    if count >= 3:
                        break
                    role = user_data.get('role', 'غير محدد')
                    name = user_data.get('full_name', 'غير محدد')
                    print(f"  - {name} ({role}) - ID: {user_id}")
                    count += 1
            else:
                print("📊 لا يوجد مستخدمين في قاعدة البيانات")
        except Exception as e:
            print(f"❌ فشل في جلب المستخدمين: {e}")
        
        # اختبار جلب الكورسات
        try:
            courses = firebase_manager.database.child('courses').get()
            if courses:
                print(f"📚 عدد الكورسات في قاعدة البيانات: {len(courses)}")
                
                # عرض أول 3 كورسات
                count = 0
                for course_id, course_data in courses.items():
                    if count >= 3:
                        break
                    title = course_data.get('title', 'غير محدد')
                    instructor_id = course_data.get('instructor_id', 'غير محدد')
                    status = course_data.get('status', 'غير محدد')
                    print(f"  - {title} (المدرس: {instructor_id}, الحالة: {status}) - ID: {course_id}")
                    count += 1
            else:
                print("📚 لا يوجد كورسات في قاعدة البيانات")
        except Exception as e:
            print(f"❌ فشل في جلب الكورسات: {e}")
        
        print("\n🎉 اختبار الاتصال بـ Firebase نجح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار Firebase: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_firebase_connection()
    if success:
        print("\n✅ الاتصال بـ Firebase يعمل بشكل صحيح")
        sys.exit(0)
    else:
        print("\n❌ هناك مشكلة في الاتصال بـ Firebase")
        sys.exit(1)
