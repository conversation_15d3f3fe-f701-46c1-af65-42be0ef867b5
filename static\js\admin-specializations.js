/**
 * إدارة التخصصات - صفحة مخصصة
 * Specializations Management - Dedicated Page
 */

class SpecializationsManager {
    constructor() {
        this.specializations = [];
        this.filteredSpecializations = [];
        this.currentEditId = null;
        this.init();
    }

    init() {
        this.loadStatistics();
        this.loadSpecializations();
        this.setupEventListeners();
        this.setupFileUpload();
    }

    /**
     * تحميل إحصائيات التخصصات
     */
    async loadStatistics() {
        try {
            const response = await fetch('/api/admin/specializations/statistics');
            if (response.ok) {
                const stats = await response.json();
                this.updateStatistics(stats);
            }
        } catch (error) {
            console.error('خطأ في تحميل الإحصائيات:', error);
        }
    }

    /**
     * تحديث عرض الإحصائيات
     */
    updateStatistics(stats) {
        const elements = {
            'total-specializations': stats.total_specializations || 0,
            'active-specializations': stats.active_specializations || 0,
            'instructors-count': stats.instructors_count || 0,
            'courses-count': stats.courses_count || 0
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                this.animateNumber(element, value);
            }
        });
    }

    /**
     * تحريك الأرقام
     */
    animateNumber(element, targetValue) {
        const startValue = 0;
        const duration = 1500;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const currentValue = Math.floor(startValue + (targetValue - startValue) * progress);
            element.textContent = currentValue.toLocaleString('ar-EG');

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    /**
     * تحميل التخصصات
     */
    async loadSpecializations() {
        try {
            const response = await fetch('/api/specializations');
            if (response.ok) {
                this.specializations = await response.json();
                this.filteredSpecializations = [...this.specializations];
                this.displaySpecializations();
            }
        } catch (error) {
            console.error('خطأ في تحميل التخصصات:', error);
            this.showError('فشل في تحميل التخصصات');
        }
    }

    /**
     * عرض أيقونة التخصص حسب النوع
     */
    renderSpecializationIcon(spec) {
        if (spec.icon_type === 'uploaded' && spec.icon_url) {
            return `<img src="${spec.icon_url}" alt="${spec.name}" style="width: 48px; height: 48px; object-fit: cover; border-radius: 8px;">`;
        } else {
            return `<i class="${spec.icon || 'fas fa-stethoscope'}"></i>`;
        }
    }

    /**
     * عرض التخصصات
     */
    displaySpecializations() {
        const container = document.getElementById('specializations-grid');
        
        if (!this.filteredSpecializations || this.filteredSpecializations.length === 0) {
            container.innerHTML = `
                <div class="col-12 text-center py-5">
                    <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد تخصصات</h4>
                    <p class="text-muted">ابدأ بإضافة تخصص جديد للمنصة</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSpecializationModal">
                        <i class="fas fa-plus me-2"></i>إضافة تخصص جديد
                    </button>
                </div>
            `;
            return;
        }

        const specializationsHTML = this.filteredSpecializations.map(spec => `
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="specialization-card">
                    <div class="specialization-header">
                        <div class="specialization-icon">
                            ${this.renderSpecializationIcon(spec)}
                        </div>
                        <h4 class="mb-1">${spec.name}</h4>
                        <p class="mb-0 opacity-75">${spec.name_en || ''}</p>
                    </div>
                    
                    <div class="specialization-body">
                        ${spec.description ? `<p class="text-muted mb-3">${spec.description}</p>` : ''}
                        
                        <div class="mb-3">
                            <strong class="text-muted small">المراحل المتاحة:</strong><br>
                            ${(spec.stages || []).map(stage => 
                                `<span class="stage-badge">المرحلة ${stage}</span>`
                            ).join('')}
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <small class="text-muted">الحالة:</small>
                                <span class="badge ${spec.active ? 'bg-success' : 'bg-secondary'}">
                                    ${spec.active ? 'نشط' : 'غير نشط'}
                                </span>
                            </div>
                            <div>
                                <small class="text-muted">المدرسين:</small>
                                <span class="badge bg-info">${spec.instructors_count || 0}</span>
                            </div>
                        </div>
                        
                        <div class="action-buttons">
                            <button class="btn btn-edit btn-sm" onclick="specializationsManager.editSpecialization('${spec.id}')">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                            <button class="btn btn-delete btn-sm" onclick="specializationsManager.deleteSpecialization('${spec.id}')">
                                <i class="fas fa-trash me-1"></i>حذف
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="specializationsManager.toggleStatus('${spec.id}')">
                                <i class="fas fa-toggle-${spec.active ? 'on' : 'off'} me-1"></i>
                                ${spec.active ? 'إلغاء تفعيل' : 'تفعيل'}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = specializationsHTML;
    }

    /**
     * إعداد مستمعي الأحداث
     */
    setupEventListeners() {
        // معاينة الأيقونة
        const iconSelect = document.getElementById('spec_icon');
        const iconPreview = document.getElementById('icon-preview');
        
        if (iconSelect && iconPreview) {
            iconSelect.addEventListener('change', (e) => {
                const iconClass = e.target.value;
                if (iconClass) {
                    iconPreview.className = iconClass + ' fa-3x text-primary';
                } else {
                    iconPreview.className = 'fas fa-question fa-3x text-muted';
                }
            });
        }

        // حفظ التخصص
        const saveBtn = document.getElementById('saveSpecializationBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveSpecialization());
        }

        // البحث والفلترة
        const searchInput = document.getElementById('searchSpecialization');
        const stageFilter = document.getElementById('filterStage');
        const statusFilter = document.getElementById('filterStatus');
        const clearFilters = document.getElementById('clearFilters');

        if (searchInput) {
            searchInput.addEventListener('input', () => this.applyFilters());
        }
        if (stageFilter) {
            stageFilter.addEventListener('change', () => this.applyFilters());
        }
        if (statusFilter) {
            statusFilter.addEventListener('change', () => this.applyFilters());
        }
        if (clearFilters) {
            clearFilters.addEventListener('click', () => this.clearFilters());
        }

        // إعادة تعيين النموذج عند إغلاق النافذة المنبثقة
        const modal = document.getElementById('addSpecializationModal');
        if (modal) {
            modal.addEventListener('hidden.bs.modal', () => {
                this.resetForm();
            });
        }
    }

    /**
     * تطبيق الفلاتر
     */
    applyFilters() {
        const searchTerm = document.getElementById('searchSpecialization').value.toLowerCase();
        const stageFilter = document.getElementById('filterStage').value;
        const statusFilter = document.getElementById('filterStatus').value;

        this.filteredSpecializations = this.specializations.filter(spec => {
            // فلتر البحث
            const matchesSearch = !searchTerm || 
                spec.name.toLowerCase().includes(searchTerm) ||
                (spec.name_en && spec.name_en.toLowerCase().includes(searchTerm)) ||
                (spec.description && spec.description.toLowerCase().includes(searchTerm));

            // فلتر المرحلة
            const matchesStage = !stageFilter || 
                (spec.stages && spec.stages.includes(parseInt(stageFilter)));

            // فلتر الحالة
            const matchesStatus = !statusFilter || 
                (statusFilter === 'active' && spec.active) ||
                (statusFilter === 'inactive' && !spec.active);

            return matchesSearch && matchesStage && matchesStatus;
        });

        this.displaySpecializations();
    }

    /**
     * مسح الفلاتر
     */
    clearFilters() {
        document.getElementById('searchSpecialization').value = '';
        document.getElementById('filterStage').value = '';
        document.getElementById('filterStatus').value = '';
        this.filteredSpecializations = [...this.specializations];
        this.displaySpecializations();
    }

    /**
     * حفظ التخصص
     */
    async saveSpecialization() {
        const form = document.getElementById('specializationForm');
        const formData = new FormData(form);
        
        // جمع المراحل المحددة
        const stages = [];
        form.querySelectorAll('input[name="stages"]:checked').forEach(checkbox => {
            stages.push(parseInt(checkbox.value));
        });

        if (stages.length === 0) {
            this.showError('يجب اختيار مرحلة واحدة على الأقل');
            return;
        }

        // تحديد نوع الأيقونة والبيانات المرتبطة
        const iconType = formData.get('icon_type') || 'font_awesome';
        let iconData = {};

        if (iconType === 'font_awesome') {
            iconData = {
                icon: formData.get('icon'),
                icon_type: 'font_awesome',
                icon_url: null
            };
        } else if (iconType === 'uploaded' && iconManager.selectedIcon) {
            iconData = {
                icon: iconManager.selectedIcon.id,
                icon_type: 'uploaded',
                icon_url: iconManager.selectedIcon.url
            };
        } else {
            this.showError('يجب اختيار أيقونة');
            return;
        }

        const specializationData = {
            name: formData.get('name'),
            name_en: formData.get('name_en'),
            description: formData.get('description'),
            color: formData.get('color'),
            stages: stages,
            active: formData.get('active') === 'on',
            ...iconData
        };

        try {
            const url = this.currentEditId ? 
                `/api/admin/specializations/${this.currentEditId}` : 
                '/api/admin/specializations';
            
            const method = this.currentEditId ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(specializationData)
            });

            if (response.ok) {
                const result = await response.json();
                this.showSuccess(this.currentEditId ? 'تم تحديث التخصص بنجاح' : 'تم إضافة التخصص بنجاح');
                
                // إغلاق النافذة المنبثقة
                const modal = bootstrap.Modal.getInstance(document.getElementById('addSpecializationModal'));
                modal.hide();
                
                // إعادة تحميل البيانات
                this.loadSpecializations();
                this.loadStatistics();
                
            } else {
                const error = await response.json();
                this.showError(error.message || 'فشل في حفظ التخصص');
            }
        } catch (error) {
            console.error('خطأ في حفظ التخصص:', error);
            this.showError('حدث خطأ أثناء حفظ التخصص');
        }
    }

    /**
     * تعديل تخصص
     */
    async editSpecialization(specializationId) {
        try {
            const response = await fetch(`/api/specializations/${specializationId}`);
            if (response.ok) {
                const specialization = await response.json();
                this.populateEditForm(specialization);
                this.currentEditId = specializationId;
                
                // تغيير عنوان النافذة المنبثقة
                document.getElementById('modal-title').textContent = 'تعديل التخصص';
                
                // فتح النافذة المنبثقة
                const modal = new bootstrap.Modal(document.getElementById('addSpecializationModal'));
                modal.show();
            }
        } catch (error) {
            console.error('خطأ في تحميل بيانات التخصص:', error);
            this.showError('فشل في تحميل بيانات التخصص');
        }
    }

    /**
     * ملء نموذج التعديل
     */
    populateEditForm(specialization) {
        document.getElementById('specialization_id').value = specialization.id;
        document.getElementById('spec_name').value = specialization.name || '';
        document.getElementById('spec_name_en').value = specialization.name_en || '';
        document.getElementById('spec_description').value = specialization.description || '';
        document.getElementById('spec_color').value = specialization.color || '#667eea';
        document.getElementById('spec_active').checked = specialization.active !== false;

        // تحديد نوع الأيقونة
        const iconType = specialization.icon_type || 'font_awesome';
        document.querySelector(`input[name="icon_type"][value="${iconType}"]`).checked = true;

        if (iconType === 'font_awesome') {
            document.getElementById('spec_icon').value = specialization.icon || '';
            // تحديث معاينة الأيقونة
            const iconPreview = document.getElementById('icon-preview');
            if (specialization.icon) {
                iconPreview.className = specialization.icon + ' fa-3x text-primary';
            }
        } else if (iconType === 'uploaded' && specialization.icon_url) {
            // تحديد الأيقونة المرفوعة
            iconManager.selectedIcon = {
                id: specialization.icon,
                url: specialization.icon_url
            };
            // تحديث المعاينة
            const preview = document.getElementById('uploaded-icon-preview');
            if (preview) {
                preview.innerHTML = `<img src="${specialization.icon_url}" alt="أيقونة مختارة" style="max-width: 100px; max-height: 100px;">`;
            }
        }

        // تبديل عرض الأقسام
        iconManager.toggleIconType();

        // تحديد المراحل
        ['stage2', 'stage3', 'stage4'].forEach(stageId => {
            const checkbox = document.getElementById(stageId);
            const stageNumber = parseInt(stageId.replace('stage', ''));
            checkbox.checked = specialization.stages && specialization.stages.includes(stageNumber);
        });
    }

    /**
     * إعادة تعيين النموذج
     */
    resetForm() {
        const form = document.getElementById('specializationForm');
        form.reset();
        this.currentEditId = null;
        document.getElementById('modal-title').textContent = 'إضافة تخصص جديد';
        document.getElementById('icon-preview').className = 'fas fa-question fa-3x text-muted';

        // إعادة تعيين نوع الأيقونة إلى Font Awesome
        document.querySelector('input[name="icon_type"][value="font_awesome"]').checked = true;
        iconManager.selectedIcon = null;

        // إعادة تعيين المعاينات
        const uploadedPreview = document.getElementById('uploaded-icon-preview');
        if (uploadedPreview) {
            uploadedPreview.innerHTML = `
                <i class="fas fa-image fa-3x"></i>
                <p class="mt-2 mb-0">لم يتم اختيار أيقونة</p>
            `;
        }

        // إزالة التحديد من الأيقونات المرفوعة
        document.querySelectorAll('.uploaded-icon-item').forEach(item => {
            item.classList.remove('selected');
        });

        // تبديل عرض الأقسام
        iconManager.toggleIconType();
    }

    /**
     * تبديل حالة التخصص
     */
    async toggleStatus(specializationId) {
        try {
            const response = await fetch(`/api/admin/specializations/${specializationId}/toggle`, {
                method: 'PATCH'
            });

            if (response.ok) {
                this.showSuccess('تم تحديث حالة التخصص بنجاح');
                this.loadSpecializations();
                this.loadStatistics();
            } else {
                const error = await response.json();
                this.showError(error.message || 'فشل في تحديث حالة التخصص');
            }
        } catch (error) {
            console.error('خطأ في تحديث حالة التخصص:', error);
            this.showError('حدث خطأ أثناء تحديث حالة التخصص');
        }
    }

    /**
     * حذف تخصص
     */
    async deleteSpecialization(specializationId) {
        const specialization = this.specializations.find(s => s.id === specializationId);
        const confirmMessage = `هل أنت متأكد من حذف تخصص "${specialization?.name}"؟\n\nسيتم حذف:\n- جميع الكورسات المرتبطة\n- جميع بيانات المدرسين\n- جميع بيانات الطلاب\n\nهذا الإجراء لا يمكن التراجع عنه!`;
        
        if (!confirm(confirmMessage)) {
            return;
        }

        try {
            const response = await fetch(`/api/admin/specializations/${specializationId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                this.showSuccess('تم حذف التخصص بنجاح');
                this.loadSpecializations();
                this.loadStatistics();
            } else {
                const error = await response.json();
                this.showError(error.message || 'فشل في حذف التخصص');
            }
        } catch (error) {
            console.error('خطأ في حذف التخصص:', error);
            this.showError('حدث خطأ أثناء حذف التخصص');
        }
    }

    /**
     * إعداد رفع الملفات
     */
    setupFileUpload() {
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const importBtn = document.getElementById('importBtn');

        if (uploadArea && fileInput) {
            uploadArea.addEventListener('click', () => fileInput.click());
            
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    this.handleFileSelect(files[0]);
                }
            });
            
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    this.handleFileSelect(e.target.files[0]);
                }
            });
        }

        if (importBtn) {
            importBtn.addEventListener('click', () => this.importSpecializations());
        }
    }

    /**
     * معالجة اختيار الملف
     */
    handleFileSelect(file) {
        if (file.type !== 'application/json') {
            this.showError('يجب اختيار ملف JSON فقط');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                this.importData = JSON.parse(e.target.result);
                document.getElementById('importBtn').disabled = false;
                this.showSuccess(`تم تحميل الملف بنجاح - ${this.importData.length} تخصص`);
            } catch (error) {
                this.showError('ملف JSON غير صالح');
                document.getElementById('importBtn').disabled = true;
            }
        };
        reader.readAsText(file);
    }

    /**
     * استيراد التخصصات
     */
    async importSpecializations() {
        if (!this.importData) {
            this.showError('لم يتم تحديد ملف للاستيراد');
            return;
        }

        try {
            const response = await fetch('/api/admin/specializations/import', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ specializations: this.importData })
            });

            if (response.ok) {
                const result = await response.json();
                this.showSuccess(`تم استيراد ${result.imported_count} تخصص بنجاح`);
                
                // إغلاق النافذة المنبثقة
                const modal = bootstrap.Modal.getInstance(document.getElementById('importSpecializationsModal'));
                modal.hide();
                
                // إعادة تحميل البيانات
                this.loadSpecializations();
                this.loadStatistics();
                
            } else {
                const error = await response.json();
                this.showError(error.message || 'فشل في استيراد التخصصات');
            }
        } catch (error) {
            console.error('خطأ في استيراد التخصصات:', error);
            this.showError('حدث خطأ أثناء استيراد التخصصات');
        }
    }

    /**
     * عرض رسالة نجاح
     */
    showSuccess(message) {
        this.showAlert(message, 'success');
    }

    /**
     * عرض رسالة خطأ
     */
    showError(message) {
        this.showAlert(message, 'danger');
    }

    /**
     * عرض تنبيه
     */
    showAlert(message, type) {
        const alertHTML = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // إضافة التنبيه في أعلى الصفحة
        const container = document.querySelector('.container-fluid');
        container.insertAdjacentHTML('afterbegin', alertHTML);
        
        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}

/**
 * مدير أيقونات التخصصات
 * Specialization Icons Manager
 */
class IconManager {
    constructor() {
        this.icons = [];
        this.selectedIcon = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadIcons();
    }

    setupEventListeners() {
        // تبديل نوع الأيقونة
        const iconTypeRadios = document.querySelectorAll('input[name="icon_type"]');
        iconTypeRadios.forEach(radio => {
            radio.addEventListener('change', () => this.toggleIconType());
        });

        // رفع أيقونة جديدة
        const uploadForm = document.getElementById('uploadIconForm');
        if (uploadForm) {
            uploadForm.addEventListener('submit', (e) => this.handleIconUpload(e));
        }

        // معاينة الملف المختار
        const fileInput = document.getElementById('icon_file');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.previewFile(e));
        }

        // تحديث قائمة الأيقونات
        const refreshBtn = document.getElementById('refreshIconsBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.loadIcons());
        }

        // فتح modal إدارة الأيقونات
        const iconManagerModal = document.getElementById('iconManagerModal');
        if (iconManagerModal) {
            iconManagerModal.addEventListener('shown.bs.modal', () => {
                this.loadIcons();
            });
        }
    }

    toggleIconType() {
        const fontAwesomeSection = document.getElementById('font_awesome_section');
        const uploadedSection = document.getElementById('uploaded_icon_section');
        const selectedType = document.querySelector('input[name="icon_type"]:checked').value;

        if (selectedType === 'font_awesome') {
            fontAwesomeSection.style.display = 'block';
            uploadedSection.style.display = 'none';
            // إزالة required من الأيقونات المرفوعة
            document.getElementById('spec_icon').required = true;
        } else {
            fontAwesomeSection.style.display = 'none';
            uploadedSection.style.display = 'block';
            // إزالة required من Font Awesome
            document.getElementById('spec_icon').required = false;
            this.loadUploadedIcons();
        }
    }

    async loadIcons() {
        try {
            const response = await fetch('/api/admin/specialization-icons');
            if (response.ok) {
                const data = await response.json();
                this.icons = data.icons || [];
                this.renderIconsList();
            } else {
                this.showError('فشل في تحميل الأيقونات');
            }
        } catch (error) {
            console.error('خطأ في تحميل الأيقونات:', error);
            this.showError('حدث خطأ في تحميل الأيقونات');
        }
    }

    async loadUploadedIcons() {
        try {
            const response = await fetch('/api/admin/specialization-icons');
            if (response.ok) {
                const data = await response.json();
                this.renderUploadedIconsGrid(data.icons || []);
            }
        } catch (error) {
            console.error('خطأ في تحميل الأيقونات المرفوعة:', error);
        }
    }

    renderUploadedIconsGrid(icons) {
        const grid = document.getElementById('uploaded-icons-grid');
        if (!grid) return;

        if (icons.length === 0) {
            grid.innerHTML = `
                <div class="text-center text-muted py-3">
                    <i class="fas fa-images fa-2x"></i>
                    <p class="mt-2 mb-0">لا توجد أيقونات مرفوعة</p>
                </div>
            `;
            return;
        }

        grid.innerHTML = icons.map(icon => `
            <div class="uploaded-icon-item" data-icon-id="${icon.id}" data-icon-url="${icon.file_url}">
                <img src="${icon.sizes?.thumbnail?.url || icon.file_url}" alt="${icon.description || icon.original_name}">
                <div class="icon-name">${icon.description || icon.original_name}</div>
            </div>
        `).join('');

        // إضافة event listeners للأيقونات
        grid.querySelectorAll('.uploaded-icon-item').forEach(item => {
            item.addEventListener('click', () => this.selectUploadedIcon(item));
        });
    }

    selectUploadedIcon(item) {
        // إزالة التحديد من جميع الأيقونات
        document.querySelectorAll('.uploaded-icon-item').forEach(i => {
            i.classList.remove('selected');
        });

        // تحديد الأيقونة المختارة
        item.classList.add('selected');

        const iconUrl = item.dataset.iconUrl;
        const iconId = item.dataset.iconId;

        // تحديث المعاينة
        const preview = document.getElementById('uploaded-icon-preview');
        if (preview) {
            preview.innerHTML = `<img src="${iconUrl}" alt="أيقونة مختارة" style="max-width: 100px; max-height: 100px;">`;
        }

        // حفظ الأيقونة المختارة
        this.selectedIcon = { id: iconId, url: iconUrl };
    }

    renderIconsList() {
        const container = document.getElementById('icons-list');
        if (!container) return;

        if (this.icons.length === 0) {
            container.innerHTML = `
                <div class="col-12 text-center py-4">
                    <i class="fas fa-images fa-3x text-muted"></i>
                    <p class="mt-3 text-muted">لا توجد أيقونات مرفوعة</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.icons.map(icon => `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="icon-card">
                    <div class="d-flex align-items-center">
                        <img src="${icon.sizes?.thumbnail?.url || icon.file_url}" alt="${icon.description || icon.original_name}" class="me-3">
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${icon.description || icon.original_name}</h6>
                            <small class="text-muted">
                                ${this.formatFileSize(icon.file_size)} •
                                ${new Date(icon.created_at).toLocaleDateString('ar-SA')}
                            </small>
                            <div class="mt-2">
                                <span class="badge ${icon.active ? 'bg-success' : 'bg-secondary'}">
                                    ${icon.active ? 'نشط' : 'غير نشط'}
                                </span>
                                <span class="badge bg-info">${icon.usage_count || 0} استخدام</span>
                            </div>
                        </div>
                        <div class="icon-actions">
                            <button class="btn btn-sm btn-outline-primary" onclick="iconManager.editIcon('${icon.id}')" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="iconManager.deleteIcon('${icon.id}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    async handleIconUpload(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const submitBtn = e.target.querySelector('button[type="submit"]');

        // تعطيل الزر أثناء الرفع
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الرفع...';

        try {
            const response = await fetch('/api/admin/specialization-icons/upload', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('تم رفع الأيقونة بنجاح');
                e.target.reset();
                document.getElementById('upload-preview').style.display = 'none';
                this.loadIcons();
                this.loadUploadedIcons();
            } else {
                this.showError(data.message || 'فشل في رفع الأيقونة');
            }
        } catch (error) {
            console.error('خطأ في رفع الأيقونة:', error);
            this.showError('حدث خطأ في رفع الأيقونة');
        } finally {
            // إعادة تفعيل الزر
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-upload me-2"></i>رفع الأيقونة';
        }
    }

    previewFile(e) {
        const file = e.target.files[0];
        const preview = document.getElementById('upload-preview');
        const previewImage = document.getElementById('preview-image');

        if (file && file.type.startsWith('image/')) {
            const reader = new FileReader();
            reader.onload = (e) => {
                previewImage.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        } else {
            preview.style.display = 'none';
        }
    }

    async deleteIcon(iconId) {
        if (!confirm('هل أنت متأكد من حذف هذه الأيقونة؟')) {
            return;
        }

        try {
            const response = await fetch(`/api/admin/specialization-icons/${iconId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('تم حذف الأيقونة بنجاح');
                this.loadIcons();
                this.loadUploadedIcons();
            } else {
                this.showError(data.message || 'فشل في حذف الأيقونة');
            }
        } catch (error) {
            console.error('خطأ في حذف الأيقونة:', error);
            this.showError('حدث خطأ في حذف الأيقونة');
        }
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    showSuccess(message) {
        this.showAlert(message, 'success');
    }

    showError(message) {
        this.showAlert(message, 'danger');
    }

    showAlert(message, type) {
        const alertContainer = document.querySelector('.container-fluid');
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        alertContainer.insertBefore(alert, alertContainer.firstChild);

        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
}

// تهيئة مدير التخصصات عند تحميل الصفحة
let specializationsManager;
let iconManager;
document.addEventListener('DOMContentLoaded', () => {
    specializationsManager = new SpecializationsManager();
    iconManager = new IconManager();
});
