"""
وسطاء المصادقة والحماية
Authentication and Authorization Middleware

هذه الوحدة مسؤولة عن:
- حماية الصفحات والـ routes
- التحقق من صحة الجلسات
- التحقق من الصلاحيات حسب الدور
- إعادة التوجيه للصفحات المناسبة
"""

import logging
from functools import wraps
from typing import List, Optional, Callable, Any
from flask import request, redirect, url_for, jsonify, session, current_app, g
from utils.auth_utils import get_auth_manager
from utils.jwt_utils import get_jwt_manager

# إعداد نظام التسجيل
logger = logging.getLogger(__name__)

class AuthMiddleware:
    """وسطاء المصادقة والحماية"""
    
    def __init__(self):
        self.auth_manager = get_auth_manager()
        self.jwt_manager = get_jwt_manager()
    
    def login_required(self, redirect_to: str = 'login'):
        """
        Decorator للتحقق من تسجيل الدخول
        Decorator to require user login
        
        Args:
            redirect_to: الصفحة المراد إعادة التوجيه إليها
        """
        def decorator(f: Callable) -> Callable:
            @wraps(f)
            def decorated_function(*args, **kwargs):
                # محاولة الحصول على التوكين
                token = self._get_token_from_request()
                
                if not token:
                    logger.debug("لا يوجد توكين في الطلب")
                    return self._handle_unauthorized(redirect_to)
                
                # التحقق من صحة الجلسة
                is_valid, user_data = self.auth_manager.validate_session(token)
                
                if not is_valid or not user_data:
                    logger.debug("جلسة غير صحيحة")
                    return self._handle_unauthorized(redirect_to)
                
                # إضافة بيانات المستخدم للطلب
                g.current_user = user_data
                g.auth_token = token
                
                return f(*args, **kwargs)
            
            return decorated_function
        return decorator
    
    def role_required(self, allowed_roles: List[str], redirect_to: str = 'login'):
        """
        Decorator للتحقق من دور المستخدم
        Decorator to require specific user roles
        
        Args:
            allowed_roles: قائمة الأدوار المسموحة
            redirect_to: الصفحة المراد إعادة التوجيه إليها
        """
        def decorator(f: Callable) -> Callable:
            @wraps(f)
            @self.login_required(redirect_to)
            def decorated_function(*args, **kwargs):
                user_role = g.current_user.get('role')
                
                if user_role not in allowed_roles:
                    logger.warning(f"مستخدم بدور {user_role} حاول الوصول لصفحة تتطلب {allowed_roles}")
                    return self._handle_forbidden()
                
                return f(*args, **kwargs)
            
            return decorated_function
        return decorator
    
    def admin_required(self, redirect_to: str = 'login'):
        """
        Decorator للتحقق من دور الأدمن
        Decorator to require admin role
        """
        return self.role_required(['admin'], redirect_to)
    
    def instructor_required(self, redirect_to: str = 'login'):
        """
        Decorator للتحقق من دور المدرس أو الأدمن
        Decorator to require instructor or admin role
        """
        return self.role_required(['admin', 'instructor'], redirect_to)
    
    def student_required(self, redirect_to: str = 'login'):
        """
        Decorator للتحقق من دور الطالب أو أعلى
        Decorator to require student role or higher
        """
        return self.role_required(['admin', 'instructor', 'student'], redirect_to)
    
    def api_auth_required(self):
        """
        Decorator للـ API routes التي تتطلب مصادقة
        Decorator for API routes that require authentication
        """
        def decorator(f: Callable) -> Callable:
            @wraps(f)
            def decorated_function(*args, **kwargs):
                token = self._get_token_from_request()
                
                if not token:
                    return jsonify({
                        'success': False,
                        'message': 'مطلوب تسجيل الدخول',
                        'error': 'TOKEN_MISSING'
                    }), 401
                
                is_valid, user_data = self.auth_manager.validate_session(token)
                
                if not is_valid or not user_data:
                    return jsonify({
                        'success': False,
                        'message': 'جلسة غير صحيحة',
                        'error': 'TOKEN_INVALID'
                    }), 401
                
                g.current_user = user_data
                g.auth_token = token
                
                return f(*args, **kwargs)
            
            return decorated_function
        return decorator
    
    def api_role_required(self, allowed_roles: List[str]):
        """
        Decorator للـ API routes التي تتطلب أدوار محددة
        Decorator for API routes that require specific roles
        """
        def decorator(f: Callable) -> Callable:
            @wraps(f)
            @self.api_auth_required()
            def decorated_function(*args, **kwargs):
                user_role = g.current_user.get('role')
                
                if user_role not in allowed_roles:
                    return jsonify({
                        'success': False,
                        'message': 'ليس لديك صلاحية للوصول لهذه الوظيفة',
                        'error': 'INSUFFICIENT_PERMISSIONS'
                    }), 403
                
                return f(*args, **kwargs)
            
            return decorated_function
        return decorator
    
    def optional_auth(self):
        """
        Decorator للصفحات التي قد تحتاج مصادقة اختيارية
        Decorator for pages with optional authentication
        """
        def decorator(f: Callable) -> Callable:
            @wraps(f)
            def decorated_function(*args, **kwargs):
                token = self._get_token_from_request()
                
                if token:
                    is_valid, user_data = self.auth_manager.validate_session(token)
                    if is_valid and user_data:
                        g.current_user = user_data
                        g.auth_token = token
                    else:
                        g.current_user = None
                        g.auth_token = None
                else:
                    g.current_user = None
                    g.auth_token = None
                
                return f(*args, **kwargs)
            
            return decorated_function
        return decorator
    
    def _get_token_from_request(self) -> Optional[str]:
        """
        استخراج التوكين من الطلب
        Extract token from request
        """
        return self.jwt_manager.extract_token_from_request()
    
    def _handle_unauthorized(self, redirect_to: str):
        """
        معالجة الطلبات غير المصرح بها
        Handle unauthorized requests
        """
        if request.is_json or request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'message': 'مطلوب تسجيل الدخول',
                'error': 'UNAUTHORIZED'
            }), 401
        else:
            # حفظ الصفحة المطلوبة للعودة إليها بعد تسجيل الدخول
            session['next_page'] = request.url
            return redirect(url_for(redirect_to))
    
    def _handle_forbidden(self):
        """
        معالجة الطلبات المحظورة
        Handle forbidden requests
        """
        if request.is_json or request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'message': 'ليس لديك صلاحية للوصول لهذه الصفحة',
                'error': 'FORBIDDEN'
            }), 403
        else:
            # إعادة توجيه لصفحة خطأ 403 أو لوحة التحكم المناسبة
            user_role = g.current_user.get('role')
            if user_role == 'student':
                return redirect(url_for('student_dashboard'))
            elif user_role == 'instructor':
                return redirect(url_for('instructor_dashboard'))
            else:
                return redirect(url_for('index'))

# إنشاء مثيل مشترك
auth_middleware = AuthMiddleware()

# تصدير الـ decorators للاستخدام المباشر
login_required = auth_middleware.login_required
role_required = auth_middleware.role_required
admin_required = auth_middleware.admin_required
instructor_required = auth_middleware.instructor_required
student_required = auth_middleware.student_required
api_auth_required = auth_middleware.api_auth_required
api_role_required = auth_middleware.api_role_required
optional_auth = auth_middleware.optional_auth

def get_auth_middleware() -> AuthMiddleware:
    """الحصول على مثيل Auth Middleware"""
    return auth_middleware

def get_current_user() -> Optional[dict]:
    """الحصول على المستخدم الحالي"""
    return getattr(g, 'current_user', None)

def get_current_token() -> Optional[str]:
    """الحصول على التوكين الحالي"""
    return getattr(g, 'auth_token', None)

def is_authenticated() -> bool:
    """التحقق من تسجيل الدخول"""
    return get_current_user() is not None

def has_role(role: str) -> bool:
    """التحقق من دور المستخدم"""
    user = get_current_user()
    return user is not None and user.get('role') == role

def has_any_role(roles: List[str]) -> bool:
    """التحقق من وجود أي من الأدوار المحددة"""
    user = get_current_user()
    return user is not None and user.get('role') in roles
