{% extends "base.html" %}

{% block title %}الصفحة غير موجودة - {{ platform_name }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 text-center py-5">
            <div class="error-page">
                <div class="error-number mb-4">
                    <h1 class="display-1 fw-bold text-primary">404</h1>
                </div>
                
                <div class="error-icon mb-4">
                    <i class="fas fa-search fa-5x text-muted"></i>
                </div>
                
                <h2 class="fw-bold mb-3">الصفحة غير موجودة</h2>
                <p class="text-muted mb-4">
                    عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى مكان آخر.
                </p>
                
                <div class="error-actions">
                    <a href="{{ url_for('index') }}" class="btn btn-primary me-3">
                        <i class="fas fa-home me-2"></i>العودة للرئيسية
                    </a>
                    <button onclick="history.back()" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>العودة للخلف
                    </button>
                </div>
                
                <div class="mt-5">
                    <h5 class="mb-3">روابط مفيدة:</h5>
                    <div class="list-group list-group-flush">
                        <a href="{{ url_for('courses') }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-book me-2"></i>الكورسات
                        </a>
                        <a href="{{ url_for('specializations') }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-stethoscope me-2"></i>التخصصات
                        </a>
                        <a href="{{ url_for('login') }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    animation: fadeInUp 0.6s ease-out;
}

.error-number h1 {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.error-icon i {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
{% endblock %}
