#!/usr/bin/env python3
"""
اختبار تكامل Firebase
Firebase integration test script
"""

import os
import sys
from dotenv import load_dotenv

# تحميل متغيرات البيئة
load_dotenv()

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.firebase_utils import get_firebase_manager
from models.database_models import DatabaseModels, UserRole
from config import Config

def test_firebase_connection():
    """اختبار اتصال Firebase"""
    print("🔥 اختبار تكامل Firebase...")
    print("=" * 50)
    
    # تهيئة Firebase
    firebase_manager = get_firebase_manager()
    config = Config()
    
    print("1. تهيئة Firebase...")
    if firebase_manager.initialize(config.__dict__):
        print("✅ تم تهيئة Firebase بنجاح")
    else:
        print("❌ فشل في تهيئة Firebase")
        return False
    
    # اختبار الاتصال
    print("\n2. اختبار الاتصال...")
    if firebase_manager.is_connected():
        print("✅ الاتصال مع Firebase يعمل بشكل صحيح")
    else:
        print("❌ فشل في الاتصال مع Firebase")
        return False
    
    # تهيئة هيكل قاعدة البيانات
    print("\n3. تهيئة هيكل قاعدة البيانات...")
    if firebase_manager.initialize_database_structure():
        print("✅ تم تهيئة هيكل قاعدة البيانات بنجاح")
    else:
        print("❌ فشل في تهيئة هيكل قاعدة البيانات")
        return False
    
    # اختبار إنشاء مستخدم تجريبي
    print("\n4. اختبار إنشاء مستخدم تجريبي...")
    test_user_data = DatabaseModels.create_user_model(
        email="<EMAIL>",
        telegram_id="123456789",
        role=UserRole.STUDENT,
        first_name="مستخدم",
        last_name="تجريبي"
    )
    
    user_id = firebase_manager.create_user(test_user_data)
    if user_id:
        print(f"✅ تم إنشاء مستخدم تجريبي بمعرف: {user_id}")
        
        # اختبار استرجاع المستخدم
        retrieved_user = firebase_manager.get_user(user_id)
        if retrieved_user:
            print("✅ تم استرجاع بيانات المستخدم بنجاح")
        else:
            print("❌ فشل في استرجاع بيانات المستخدم")
            
        # اختبار البحث بمعرف التليجرام
        user_by_telegram = firebase_manager.get_user_by_telegram_id("123456789")
        if user_by_telegram:
            print("✅ تم العثور على المستخدم بمعرف التليجرام")
        else:
            print("❌ فشل في العثور على المستخدم بمعرف التليجرام")
    else:
        print("❌ فشل في إنشاء مستخدم تجريبي")
        return False
    
    # اختبار التخصصات
    print("\n5. اختبار التخصصات...")
    specializations = firebase_manager.get_all_specializations()
    if specializations:
        print(f"✅ تم العثور على {len(specializations)} تخصص:")
        for spec in specializations:
            print(f"   - {spec.get('name', 'غير محدد')} ({spec.get('name_en', 'N/A')})")
    else:
        print("⚠️ لم يتم العثور على تخصصات")
    
    print("\n" + "=" * 50)
    print("🎉 تم اكتمال جميع اختبارات Firebase بنجاح!")
    return True

def test_database_models():
    """اختبار نماذج قاعدة البيانات"""
    print("\n📊 اختبار نماذج قاعدة البيانات...")
    print("=" * 50)
    
    # اختبار نموذج المستخدم
    print("1. اختبار نموذج المستخدم...")
    user_model = DatabaseModels.create_user_model(
        email="<EMAIL>",
        telegram_id="987654321",
        role=UserRole.INSTRUCTOR,
        first_name="مدرس",
        last_name="تجريبي",
        specialization_id="spec_123",
        permissions={
            "can_create_courses": True,
            "allowed_stages": [2, 3]
        }
    )
    
    required_fields = ['email', 'telegram_id', 'role', 'first_name', 'created_at']
    if all(field in user_model for field in required_fields):
        print("✅ نموذج المستخدم يحتوي على جميع الحقول المطلوبة")
    else:
        print("❌ نموذج المستخدم ناقص بعض الحقول")
    
    # اختبار نموذج التخصص
    print("\n2. اختبار نموذج التخصص...")
    spec_model = DatabaseModels.create_specialization_model(
        name="تخصص تجريبي",
        name_en="Test Specialization",
        icon="fas fa-test",
        description="تخصص للاختبار"
    )
    
    spec_required_fields = ['name', 'name_en', 'icon', 'created_at']
    if all(field in spec_model for field in spec_required_fields):
        print("✅ نموذج التخصص يحتوي على جميع الحقول المطلوبة")
    else:
        print("❌ نموذج التخصص ناقص بعض الحقول")
    
    # اختبار مخطط قاعدة البيانات
    print("\n3. اختبار مخطط قاعدة البيانات...")
    schema = DatabaseModels.get_database_schema()
    expected_collections = ['users', 'specializations', 'courses', 'lessons', 'enrollments', 'activation_codes', 'invitation_links']
    
    if all(collection in schema for collection in expected_collections):
        print("✅ مخطط قاعدة البيانات يحتوي على جميع المجموعات المطلوبة")
    else:
        print("❌ مخطط قاعدة البيانات ناقص بعض المجموعات")
    
    print("\n" + "=" * 50)
    print("🎉 تم اكتمال جميع اختبارات النماذج بنجاح!")

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء اختبارات تكامل Firebase...")
    print("تاريخ الاختبار:", os.popen('date').read().strip())
    print()
    
    try:
        # اختبار نماذج قاعدة البيانات
        test_database_models()
        
        # اختبار اتصال Firebase
        if test_firebase_connection():
            print("\n🎊 جميع الاختبارات نجحت! Firebase جاهز للاستخدام.")
            return True
        else:
            print("\n💥 بعض الاختبارات فشلت. يرجى مراجعة الإعدادات.")
            return False
            
    except Exception as e:
        print(f"\n💥 خطأ أثناء تشغيل الاختبارات: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
