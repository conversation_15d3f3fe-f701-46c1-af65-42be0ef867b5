"""
اختبار نظام كلمات المرور غير المشفرة
Test Plain Text Password System
"""

def test_auth_with_plain_passwords():
    """اختبار المصادقة مع كلمات المرور غير المشفرة"""
    print("🔐 اختبار نظام المصادقة مع كلمات المرور غير المشفرة")
    print("="*60)
    
    try:
        import sys
        import os
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from flask import Flask
        from utils.auth_utils import AuthManager
        from config import Config
        
        # إنشاء تطبيق للاختبار
        app = Flask(__name__)
        app.config.from_object(Config)
        
        with app.app_context():
            auth_manager = AuthManager()
            
            # اختبار 1: إنشاء مستخدم بكلمة مرور غير مشفرة
            print("\n📝 اختبار 1: إنشاء مستخدم بكلمة مرور غير مشفرة")
            
            test_user_data = {
                'email': '<EMAIL>',
                'telegram_id': '123456789',
                'role': 'student',
                'first_name': 'اختبار',
                'last_name': 'المستخدم'
            }
            
            test_password = 'plain_password_123'
            
            user_id = auth_manager.create_user_with_plain_password(test_user_data, test_password)
            
            if user_id:
                print(f"✅ تم إنشاء المستخدم بنجاح: {user_id}")
                
                # اختبار 2: التحقق من أن كلمة المرور محفوظة بدون تشفير
                print("\n🔍 اختبار 2: التحقق من حفظ كلمة المرور بدون تشفير")
                
                from utils.firebase_utils import get_firebase_manager
                firebase_manager = get_firebase_manager()
                
                user = firebase_manager.get_user(user_id)
                if user:
                    stored_password = user.get('password')
                    if stored_password == test_password:
                        print("✅ كلمة المرور محفوظة بدون تشفير")
                        print(f"   كلمة المرور المحفوظة: {stored_password}")
                    else:
                        print("❌ كلمة المرور مشفرة أو مختلفة")
                        print(f"   المتوقع: {test_password}")
                        print(f"   المحفوظ: {stored_password}")
                        return False
                else:
                    print("❌ لم يتم العثور على المستخدم")
                    return False
                
                # اختبار 3: تسجيل الدخول بكلمة المرور غير المشفرة
                print("\n🔑 اختبار 3: تسجيل الدخول بكلمة المرور غير المشفرة")
                
                success, user_data, message = auth_manager.authenticate_user(
                    test_user_data['email'], 
                    test_password
                )
                
                if success:
                    print("✅ تم تسجيل الدخول بنجاح")
                    print(f"   رسالة: {message}")
                else:
                    print(f"❌ فشل في تسجيل الدخول: {message}")
                    return False
                
                # اختبار 4: تسجيل الدخول بكلمة مرور خاطئة
                print("\n🚫 اختبار 4: تسجيل الدخول بكلمة مرور خاطئة")
                
                success, user_data, message = auth_manager.authenticate_user(
                    test_user_data['email'], 
                    'wrong_password'
                )
                
                if not success:
                    print("✅ تم رفض كلمة المرور الخاطئة بشكل صحيح")
                    print(f"   رسالة: {message}")
                else:
                    print("❌ تم قبول كلمة مرور خاطئة!")
                    return False
                
                # تنظيف: حذف المستخدم التجريبي
                print("\n🗑️ تنظيف: حذف المستخدم التجريبي")
                firebase_manager.database.child('users').child(user_id).delete()
                print("✅ تم حذف المستخدم التجريبي")
                
                return True
                
            else:
                print("❌ فشل في إنشاء المستخدم")
                return False
                
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار نظام كلمات المرور غير المشفرة")
    print("="*60)
    
    success = test_auth_with_plain_passwords()
    
    print("\n" + "="*60)
    if success:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ نظام كلمات المرور غير المشفرة يعمل بشكل صحيح")
        print("\n📝 ملاحظات:")
        print("   - كلمات المرور تُحفظ بدون تشفير كما طلبت")
        print("   - النظام يدعم كلمات المرور المشفرة والغير مشفرة")
        print("   - يمكن إنشاء حسابات جديدة بكلمات مرور غير مشفرة")
    else:
        print("⚠️ بعض الاختبارات فشلت")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    return success

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
