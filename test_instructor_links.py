"""
اختبار نظام إنشاء روابط المدرسين
Test Instructor Link Creation System

هذا الملف لاختبار وظائف إنشاء وإدارة روابط دعوة المدرسين
"""

import sys
import os
from datetime import datetime

# إضافة المجلد الرئيسي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.firebase_utils import get_firebase_manager
from utils.invitation_utils import get_invitation_manager
from config import Config

def test_invitation_system():
    """اختبار نظام الدعوات"""
    print("🧪 بدء اختبار نظام إنشاء روابط المدرسين...")
    
    # تهيئة النظام
    print("\n1. تهيئة Firebase...")
    firebase_manager = get_firebase_manager()
    config = Config()
    
    if firebase_manager.initialize(config.__dict__):
        print("✅ تم تهيئة Firebase بنجاح")
    else:
        print("❌ فشل في تهيئة Firebase")
        return False
    
    # اختبار مدير الدعوات
    print("\n2. اختبار مدير الدعوات...")
    invitation_manager = get_invitation_manager()
    
    # اختبار إنشاء رابط دعوة مدرس
    print("\n3. اختبار إنشاء رابط دعوة مدرس...")
    success, invitation_url, message = invitation_manager.create_instructor_invitation_link(
        created_by="123456789",  # معرف مالك البوت التجريبي
        instructor_name="د. أحمد محمد",
        specialization_id="medical_analysis",
        expires_hours=24
    )
    
    if success:
        print(f"✅ تم إنشاء رابط الدعوة بنجاح")
        print(f"🔗 الرابط: {invitation_url}")
        
        # استخراج معرف الرابط للاختبار
        link_id = invitation_url.split('instructor_')[1] if 'instructor_' in invitation_url else None
        
        if link_id:
            # اختبار التحقق من صحة الرابط
            print(f"\n4. اختبار التحقق من صحة الرابط...")
            is_valid, link_data, validation_message = invitation_manager.validate_invitation_link(link_id)
            
            if is_valid:
                print(f"✅ الرابط صحيح: {validation_message}")
                print(f"📋 بيانات الرابط:")
                print(f"   - نوع الرابط: {link_data.get('link_type')}")
                print(f"   - اسم المدرس: {link_data.get('target_data', {}).get('instructor_name')}")
                print(f"   - التخصص: {link_data.get('target_data', {}).get('specialization_id')}")
                print(f"   - تاريخ الإنشاء: {link_data.get('created_at')}")
                print(f"   - تاريخ انتهاء الصلاحية: {link_data.get('expires_at')}")
                
                # اختبار محاكاة استخدام الرابط
                print(f"\n5. اختبار محاكاة استخدام الرابط...")
                use_success, use_message = invitation_manager.use_invitation_link(link_id, "987654321")
                
                if use_success:
                    print(f"✅ تم استخدام الرابط بنجاح: {use_message}")
                    
                    # التحقق من حالة الرابط بعد الاستخدام
                    is_valid_after, _, validation_message_after = invitation_manager.validate_invitation_link(link_id)
                    if not is_valid_after:
                        print(f"✅ الرابط أصبح غير صالح بعد الاستخدام: {validation_message_after}")
                    else:
                        print(f"⚠️ الرابط ما زال صالحاً بعد الاستخدام")
                else:
                    print(f"❌ فشل في استخدام الرابط: {use_message}")
            else:
                print(f"❌ الرابط غير صحيح: {validation_message}")
        else:
            print("❌ فشل في استخراج معرف الرابط")
    else:
        print(f"❌ فشل في إنشاء رابط الدعوة: {message}")
        return False
    
    print("\n🎉 تم اكتمال اختبار نظام الدعوات بنجاح!")
    return True

def test_specializations():
    """اختبار الحصول على التخصصات"""
    print("\n🧪 اختبار الحصول على التخصصات...")
    
    firebase_manager = get_firebase_manager()
    specializations = firebase_manager.get_all_specializations()
    
    if specializations:
        print("✅ تم الحصول على التخصصات:")
        for spec_id, spec_data in specializations.items():
            name = spec_data.get('name', 'غير محدد')
            icon = spec_data.get('icon', '📚')
            print(f"   {icon} {spec_id}: {name}")
    else:
        print("⚠️ لم يتم العثور على تخصصات في قاعدة البيانات")
        print("💡 سيتم استخدام التخصصات الافتراضية")

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار نظام إنشاء روابط المدرسين")
    print("=" * 60)
    
    try:
        # اختبار التخصصات
        test_specializations()
        
        # اختبار نظام الدعوات
        success = test_invitation_system()
        
        if success:
            print("\n" + "=" * 60)
            print("✅ جميع الاختبارات نجحت!")
            print("🚀 النظام جاهز للاستخدام")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("❌ بعض الاختبارات فشلت!")
            print("🔧 يرجى مراجعة الأخطاء وإصلاحها")
            print("=" * 60)
            
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        print("🔧 يرجى مراجعة الإعدادات والاتصال بقاعدة البيانات")

if __name__ == "__main__":
    main()
