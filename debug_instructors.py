"""
فحص بيانات المدرسين في قاعدة البيانات
Debug Instructors Data in Database
"""

import sys
import os
from flask import Flask

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_instructors():
    """فحص بيانات المدرسين في قاعدة البيانات"""
    print("🔍 فحص بيانات المدرسين...")
    
    try:
        from app import create_app
        from utils.firebase_utils import get_firebase_manager
        
        # إنشاء التطبيق
        app = create_app('development')
        
        with app.app_context():
            firebase_manager = get_firebase_manager()
            
            # التأكد من تهيئة Firebase
            if not firebase_manager.is_connected():
                print("❌ Firebase غير متصل")
                return False
            
            print("✅ Firebase متصل")
            
            # جلب جميع المستخدمين
            print("\n📋 جلب جميع المستخدمين...")
            all_users = firebase_manager.get_all_users()
            print(f"📊 إجمالي المستخدمين: {len(all_users)}")
            
            # فلترة المدرسين
            instructors = [user for user in all_users if user.get('role') == 'instructor']
            print(f"👨‍🏫 عدد المدرسين: {len(instructors)}")
            
            # عرض تفاصيل كل مدرس
            for i, instructor in enumerate(instructors, 1):
                print(f"\n👤 المدرس {i}:")
                print(f"   🆔 المعرف: {instructor.get('id', 'غير محدد')}")
                print(f"   📧 البريد: {instructor.get('email', 'غير محدد')}")
                print(f"   👤 الاسم: {instructor.get('first_name', '')} {instructor.get('last_name', '')}")
                print(f"   🏷️ الدور: {instructor.get('role', 'غير محدد')}")
                print(f"   🎯 التخصص: {instructor.get('specialization_id', 'غير محدد')}")
                print(f"   ✅ نشط: {instructor.get('active', 'غير محدد')}")
                print(f"   📅 تاريخ الإنشاء: {instructor.get('created_at', 'غير محدد')}")
                
                # فحص الصلاحيات
                permissions = instructor.get('permissions', {})
                print(f"   🔐 الصلاحيات:")
                print(f"      - إنشاء كورسات: {permissions.get('can_create_courses', 'غير محدد')}")
                print(f"      - إدارة طلاب: {permissions.get('can_manage_students', 'غير محدد')}")
                print(f"      - المراحل المسموحة: {permissions.get('allowed_stages', 'غير محدد')}")
                print(f"      - كورسات عامة: {permissions.get('can_create_general_courses', 'غير محدد')}")
            
            # اختبار وظيفة get_all_instructors
            print(f"\n🔧 اختبار وظيفة get_all_instructors...")
            api_instructors = firebase_manager.get_all_instructors()
            print(f"📊 النتيجة من API: {len(api_instructors)} مدرس")
            
            if len(api_instructors) != len(instructors):
                print("⚠️ هناك تباين بين العدد المتوقع والفعلي!")
                print("🔍 فحص تفصيلي للمشكلة...")
                
                # فحص البحث بـ role
                try:
                    users_by_role = firebase_manager.database.child('users').order_by_child('role').equal_to('instructor').get()
                    print(f"📊 البحث بـ role='instructor': {len(users_by_role) if users_by_role else 0}")
                    
                    if users_by_role:
                        for user_id, user_data in users_by_role.items():
                            print(f"   - {user_id}: {user_data.get('email')} (role: {user_data.get('role')})")
                    
                except Exception as e:
                    print(f"❌ خطأ في البحث بـ role: {e}")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في فحص المدرسين: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 فحص بيانات المدرسين في قاعدة البيانات")
    print("="*60)
    
    success = debug_instructors()
    
    if success:
        print("\n✅ تم فحص البيانات بنجاح!")
    else:
        print("\n❌ فشل في فحص البيانات")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
