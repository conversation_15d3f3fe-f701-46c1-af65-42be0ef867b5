{"rules": {".read": false, ".write": false, "users": {".indexOn": ["telegram_id", "email", "role", "specialization_id"], "$uid": {".read": "auth != null && (auth.uid == $uid || root.child('users').child(auth.uid).child('role').val() == 'admin')", ".write": "auth != null && (auth.uid == $uid || root.child('users').child(auth.uid).child('role').val() == 'admin')", ".validate": "newData.hasChildren(['email', 'telegram_id', 'role', 'first_name', 'created_at'])", "email": {".validate": "newData.isString() && newData.val().matches(/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$/)"}, "telegram_id": {".validate": "newData.isString() && newData.val().length > 0"}, "role": {".validate": "newData.isString() && (newData.val() == 'admin' || newData.val() == 'instructor' || newData.val() == 'student')"}, "first_name": {".validate": "newData.isString() && newData.val().length > 0"}, "last_name": {".validate": "newData.isString()"}, "active": {".validate": "newData.isBoolean()"}, "created_at": {".validate": "newData.isString()"}, "updated_at": {".validate": "newData.isString()"}}}, "specializations": {".indexOn": ["name", "active"], ".read": "auth != null", "$spec_id": {".write": "auth != null && root.child('users').child(auth.uid).child('role').val() == 'admin'", ".validate": "newData.hasChildren(['name', 'name_en', 'icon', 'created_at'])", "name": {".validate": "newData.isString() && newData.val().length > 0"}, "name_en": {".validate": "newData.isString() && newData.val().length > 0"}, "icon": {".validate": "newData.isString() && newData.val().length > 0"}, "active": {".validate": "newData.isBoolean()"}, "stages": {".validate": "newData.hasChildren()"}}}, "courses": {".indexOn": ["instructor_id", "specialization_id", "stage", "status", "is_general"], ".read": "auth != null", "$course_id": {".write": "auth != null && (root.child('users').child(auth.uid).child('role').val() == 'admin' || (root.child('users').child(auth.uid).child('role').val() == 'instructor' && (newData.child('instructor_id').val() == auth.uid || data.child('instructor_id').val() == auth.uid)))", ".validate": "newData.hasChildren(['title', 'instructor_id', 'stage', 'created_at'])", "title": {".validate": "newData.isString() && newData.val().length > 0"}, "instructor_id": {".validate": "newData.isString() && root.child('users').child(newData.val()).child('role').val() == 'instructor'"}, "stage": {".validate": "newData.isNumber() && (newData.val() == 2 || newData.val() == 3 || newData.val() == 4)"}, "status": {".validate": "newData.isString() && (newData.val() == 'draft' || newData.val() == 'published' || newData.val() == 'archived')"}, "is_general": {".validate": "newData.isBoolean()"}}}, "lessons": {".indexOn": ["course_id", "order"], ".read": "auth != null", "$lesson_id": {".write": "auth != null && (root.child('users').child(auth.uid).child('role').val() == 'admin' || (root.child('users').child(auth.uid).child('role').val() == 'instructor' && root.child('courses').child(newData.child('course_id').val()).child('instructor_id').val() == auth.uid))", ".validate": "newData.hasChildren(['course_id', 'title', 'content_type', 'order', 'created_at'])", "course_id": {".validate": "newData.isString() && root.child('courses').child(newData.val()).exists()"}, "title": {".validate": "newData.isString() && newData.val().length > 0"}, "content_type": {".validate": "newData.isString() && (newData.val() == 'video' || newData.val() == 'text' || newData.val() == 'quiz' || newData.val() == 'assignment')"}, "order": {".validate": "newData.isNumber() && newData.val() > 0"}}}, "enrollments": {".indexOn": ["student_id", "course_id", "status"], "$enrollment_id": {".read": "auth != null && (auth.uid == data.child('student_id').val() || root.child('users').child(auth.uid).child('role').val() == 'admin' || (root.child('users').child(auth.uid).child('role').val() == 'instructor' && root.child('courses').child(data.child('course_id').val()).child('instructor_id').val() == auth.uid))", ".write": "auth != null && (auth.uid == newData.child('student_id').val() || root.child('users').child(auth.uid).child('role').val() == 'admin')", ".validate": "newData.hasChildren(['student_id', 'course_id', 'enrolled_at'])", "student_id": {".validate": "newData.isString() && root.child('users').child(newData.val()).child('role').val() == 'student'"}, "course_id": {".validate": "newData.isString() && root.child('courses').child(newData.val()).exists()"}, "status": {".validate": "newData.isString() && (newData.val() == 'active' || newData.val() == 'completed' || newData.val() == 'suspended')"}}}, "activation_codes": {".indexOn": ["code", "course_id", "created_by", "active"], ".read": "auth != null && (root.child('users').child(auth.uid).child('role').val() == 'admin' || root.child('users').child(auth.uid).child('role').val() == 'instructor')", "$code_id": {".write": "auth != null && (root.child('users').child(auth.uid).child('role').val() == 'admin' || (root.child('users').child(auth.uid).child('role').val() == 'instructor' && (newData.child('created_by').val() == auth.uid || data.child('created_by').val() == auth.uid)))", ".validate": "newData.hasChildren(['code', 'course_id', 'created_by', 'created_at'])", "code": {".validate": "newData.isString() && newData.val().length >= 6"}, "course_id": {".validate": "newData.isString() && root.child('courses').child(newData.val()).exists()"}, "created_by": {".validate": "newData.isString() && (root.child('users').child(newData.val()).child('role').val() == 'admin' || root.child('users').child(newData.val()).child('role').val() == 'instructor')"}, "max_uses": {".validate": "newData.isNumber() && newData.val() > 0"}, "current_uses": {".validate": "newData.isNumber() && newData.val() >= 0"}, "active": {".validate": "newData.isBoolean()"}}}, "invitation_links": {".indexOn": ["link_id", "created_by", "link_type", "active"], ".read": "auth != null && root.child('users').child(auth.uid).child('role').val() == 'admin'", "$link_id": {".write": "auth != null && root.child('users').child(auth.uid).child('role').val() == 'admin'", ".validate": "newData.hasChildren(['link_id', 'created_by', 'link_type', 'created_at'])", "link_id": {".validate": "newData.isString() && newData.val().length > 0"}, "created_by": {".validate": "newData.isString() && root.child('users').child(newData.val()).child('role').val() == 'admin'"}, "link_type": {".validate": "newData.isString() && (newData.val() == 'instructor_signup' || newData.val() == 'student_invite')"}, "max_uses": {".validate": "newData.isNumber() && newData.val() > 0"}, "current_uses": {".validate": "newData.isNumber() && newData.val() >= 0"}, "active": {".validate": "newData.isBoolean()"}}}, "system_info": {".read": "auth != null", ".write": "auth != null && root.child('users').child(auth.uid).child('role').val() == 'admin'"}, "health_check": {".read": true, ".write": true}}}