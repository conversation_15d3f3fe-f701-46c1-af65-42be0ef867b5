"""
نظام المهام المجدولة لإدارة انتهاء صلاحية أكواد التفعيل
Scheduler system for managing activation code expiration
"""

import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
import atexit

# إعداد التسجيل
logger = logging.getLogger(__name__)

class ActivationCodeScheduler:
    """مجدول المهام لإدارة أكواد التفعيل"""
    
    def __init__(self, firebase_manager=None):
        """
        تهيئة المجدول
        
        Args:
            firebase_manager: مدير Firebase
        """
        self.firebase_manager = firebase_manager
        self.scheduler = BackgroundScheduler()
        self.is_running = False
        
        # إعدادات افتراضية
        self.config = {
            'check_interval_minutes': 30,  # فحص كل 30 دقيقة
            'notification_days_before': 3,  # إشعار قبل 3 أيام من الانتهاء
            'cleanup_days_after': 30,  # تنظيف بعد 30 يوم من الانتهاء
            'archive_enabled': True,  # تفعيل الأرشفة
            'auto_cleanup_enabled': True  # تفعيل التنظيف التلقائي
        }
        
        # تسجيل إيقاف المجدول عند إغلاق التطبيق
        atexit.register(self.shutdown)
    
    def start(self):
        """بدء المجدول"""
        try:
            if not self.is_running:
                # إضافة المهام المجدولة
                self._add_scheduled_jobs()
                
                # بدء المجدول
                self.scheduler.start()
                self.is_running = True
                
                logger.info("تم بدء مجدول أكواد التفعيل بنجاح")
                
                # تشغيل فحص فوري
                self.check_expired_codes()
                
        except Exception as e:
            logger.error(f"خطأ في بدء المجدول: {e}")
    
    def shutdown(self):
        """إيقاف المجدول"""
        try:
            if self.is_running:
                self.scheduler.shutdown()
                self.is_running = False
                logger.info("تم إيقاف مجدول أكواد التفعيل")
        except Exception as e:
            logger.error(f"خطأ في إيقاف المجدول: {e}")
    
    def _add_scheduled_jobs(self):
        """إضافة المهام المجدولة"""
        try:
            # مهمة فحص الأكواد المنتهية الصلاحية
            self.scheduler.add_job(
                func=self.check_expired_codes,
                trigger=IntervalTrigger(minutes=self.config['check_interval_minutes']),
                id='check_expired_codes',
                name='فحص الأكواد المنتهية الصلاحية',
                replace_existing=True
            )
            
            # مهمة إرسال إشعارات الانتهاء القريب
            self.scheduler.add_job(
                func=self.send_expiration_notifications,
                trigger=CronTrigger(hour=9, minute=0),  # يومياً في الساعة 9 صباحاً
                id='send_expiration_notifications',
                name='إرسال إشعارات انتهاء الصلاحية',
                replace_existing=True
            )
            
            # مهمة التنظيف التلقائي (أسبوعياً)
            if self.config['auto_cleanup_enabled']:
                self.scheduler.add_job(
                    func=self.auto_cleanup_expired_codes,
                    trigger=CronTrigger(day_of_week=0, hour=2, minute=0),  # كل أحد في الساعة 2 فجراً
                    id='auto_cleanup_expired_codes',
                    name='التنظيف التلقائي للأكواد المنتهية الصلاحية',
                    replace_existing=True
                )
            
            logger.info("تم إضافة المهام المجدولة بنجاح")
            
        except Exception as e:
            logger.error(f"خطأ في إضافة المهام المجدولة: {e}")
    
    def check_expired_codes(self):
        """فحص وتحديث الأكواد المنتهية الصلاحية"""
        try:
            if not self.firebase_manager:
                logger.warning("مدير Firebase غير متاح")
                return
            
            logger.info("بدء فحص الأكواد المنتهية الصلاحية...")
            
            # الحصول على جميع الأكواد النشطة
            all_codes = self.firebase_manager.get_activation_codes(active_only=False)
            
            expired_count = 0
            current_time = datetime.now(timezone.utc)
            
            for code in all_codes:
                code_id = code.get('id')
                expires_at = code.get('expires_at')
                is_active = code.get('active', True)
                
                # التحقق من وجود تاريخ انتهاء
                if not expires_at or not is_active:
                    continue
                
                try:
                    # تحويل تاريخ الانتهاء
                    expiry_date = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                    
                    # التحقق من انتهاء الصلاحية
                    if current_time > expiry_date:
                        # تحديث حالة الكود
                        success = self._mark_code_as_expired(code_id)
                        if success:
                            expired_count += 1
                            logger.info(f"تم تحديث الكود المنتهي الصلاحية: {code.get('code', 'غير محدد')}")
                
                except Exception as e:
                    logger.error(f"خطأ في معالجة الكود {code_id}: {e}")
                    continue
            
            logger.info(f"تم فحص الأكواد - تم تحديث {expired_count} كود منتهي الصلاحية")
            
        except Exception as e:
            logger.error(f"خطأ في فحص الأكواد المنتهية الصلاحية: {e}")
    
    def _mark_code_as_expired(self, code_id: str) -> bool:
        """تحديد كود كمنتهي الصلاحية"""
        try:
            # تحديث بيانات الكود
            update_data = {
                'active': False,
                'expired': True,
                'expired_at': datetime.now(timezone.utc).isoformat(),
                'expired_by_system': True
            }
            
            # تحديث في قاعدة البيانات
            self.firebase_manager.database.child('activation_codes').child(code_id).update(update_data)
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في تحديد الكود {code_id} كمنتهي الصلاحية: {e}")
            return False
    
    def send_expiration_notifications(self):
        """إرسال إشعارات انتهاء الصلاحية القريب"""
        try:
            if not self.firebase_manager:
                logger.warning("مدير Firebase غير متاح")
                return
            
            logger.info("بدء إرسال إشعارات انتهاء الصلاحية...")
            
            # الحصول على الأكواد التي ستنتهي قريباً
            expiring_codes = self._get_expiring_codes()
            
            # تجميع الإشعارات حسب المدرس
            notifications_by_instructor = {}
            
            for code in expiring_codes:
                instructor_id = code.get('created_by')
                if instructor_id not in notifications_by_instructor:
                    notifications_by_instructor[instructor_id] = []
                notifications_by_instructor[instructor_id].append(code)
            
            # إرسال الإشعارات
            for instructor_id, codes in notifications_by_instructor.items():
                self._send_instructor_notification(instructor_id, codes)
            
            logger.info(f"تم إرسال إشعارات لـ {len(notifications_by_instructor)} مدرس")
            
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعارات انتهاء الصلاحية: {e}")
    
    def _get_expiring_codes(self) -> List[Dict[str, Any]]:
        """الحصول على الأكواد التي ستنتهي قريباً"""
        try:
            all_codes = self.firebase_manager.get_activation_codes(active_only=True)
            expiring_codes = []
            
            current_time = datetime.now(timezone.utc)
            notification_threshold = current_time + timedelta(days=self.config['notification_days_before'])
            
            for code in all_codes:
                expires_at = code.get('expires_at')
                if not expires_at:
                    continue
                
                try:
                    expiry_date = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                    
                    # التحقق من اقتراب انتهاء الصلاحية
                    if current_time < expiry_date <= notification_threshold:
                        # التحقق من عدم إرسال إشعار سابق
                        if not code.get('notification_sent'):
                            expiring_codes.append(code)
                
                except Exception as e:
                    logger.error(f"خطأ في معالجة تاريخ انتهاء الكود: {e}")
                    continue
            
            return expiring_codes
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على الأكواد المنتهية قريباً: {e}")
            return []
    
    def _send_instructor_notification(self, instructor_id: str, codes: List[Dict[str, Any]]):
        """إرسال إشعار للمدرس"""
        try:
            # الحصول على بيانات المدرس
            instructor = self.firebase_manager.get_user(instructor_id)
            if not instructor:
                return
            
            # إنشاء رسالة الإشعار
            message = f"🔔 تنبيه: لديك {len(codes)} كود تفعيل سينتهي قريباً:\n\n"
            
            for code in codes:
                course = self.firebase_manager.get_course(code.get('course_id'))
                course_title = course.get('title', 'غير محدد') if course else 'غير محدد'
                expires_at = code.get('expires_at')
                
                if expires_at:
                    try:
                        expiry_date = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                        days_left = (expiry_date - datetime.now(timezone.utc)).days
                        message += f"• {course_title}: {days_left} يوم متبقي\n"
                    except:
                        message += f"• {course_title}: قريباً\n"
            
            message += "\nيرجى تجديد الأكواد أو إنشاء أكواد جديدة."
            
            # هنا يمكن إضافة إرسال الإشعار عبر التليجرام أو البريد الإلكتروني
            logger.info(f"إشعار للمدرس {instructor.get('full_name', instructor_id)}: {len(codes)} كود سينتهي قريباً")
            
            # تحديد الأكواد كمرسل لها إشعار
            for code in codes:
                code_id = code.get('id')
                if code_id:
                    self.firebase_manager.database.child('activation_codes').child(code_id).update({
                        'notification_sent': True,
                        'notification_sent_at': datetime.now(timezone.utc).isoformat()
                    })
            
        except Exception as e:
            logger.error(f"خطأ في إرسال إشعار للمدرس {instructor_id}: {e}")
    
    def auto_cleanup_expired_codes(self):
        """التنظيف التلقائي للأكواد المنتهية الصلاحية"""
        try:
            if not self.config['auto_cleanup_enabled']:
                return
            
            logger.info("بدء التنظيف التلقائي للأكواد المنتهية الصلاحية...")
            
            # الحصول على الأكواد المنتهية الصلاحية القديمة
            cleanup_threshold = datetime.now(timezone.utc) - timedelta(days=self.config['cleanup_days_after'])
            
            all_codes = self.firebase_manager.get_activation_codes(active_only=False)
            codes_to_cleanup = []
            
            for code in all_codes:
                expired_at = code.get('expired_at')
                if not expired_at:
                    continue
                
                try:
                    expired_date = datetime.fromisoformat(expired_at.replace('Z', '+00:00'))
                    if expired_date < cleanup_threshold:
                        codes_to_cleanup.append(code)
                except:
                    continue
            
            # أرشفة وحذف الأكواد القديمة
            archived_count = 0
            for code in codes_to_cleanup:
                if self.config['archive_enabled']:
                    success = self._archive_expired_code(code)
                    if success:
                        archived_count += 1
            
            logger.info(f"تم أرشفة {archived_count} كود منتهي الصلاحية")
            
        except Exception as e:
            logger.error(f"خطأ في التنظيف التلقائي: {e}")
    
    def _archive_expired_code(self, code: Dict[str, Any]) -> bool:
        """أرشفة كود منتهي الصلاحية"""
        try:
            code_id = code.get('id')
            if not code_id:
                return False
            
            # إضافة بيانات الأرشفة
            archive_data = code.copy()
            archive_data.update({
                'archived_at': datetime.now(timezone.utc).isoformat(),
                'archived_by_system': True
            })
            
            # نقل إلى مجموعة الأرشيف
            self.firebase_manager.database.child('archived_activation_codes').child(code_id).set(archive_data)
            
            # حذف من المجموعة الأصلية
            self.firebase_manager.database.child('activation_codes').child(code_id).remove()
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في أرشفة الكود: {e}")
            return False
    
    def update_config(self, new_config: Dict[str, Any]):
        """تحديث إعدادات المجدول"""
        try:
            self.config.update(new_config)
            
            # إعادة إضافة المهام المجدولة بالإعدادات الجديدة
            if self.is_running:
                self.scheduler.remove_all_jobs()
                self._add_scheduled_jobs()
            
            logger.info("تم تحديث إعدادات المجدول")
            
        except Exception as e:
            logger.error(f"خطأ في تحديث إعدادات المجدول: {e}")
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """الحصول على حالة المجدول"""
        try:
            jobs = []
            if self.is_running:
                for job in self.scheduler.get_jobs():
                    jobs.append({
                        'id': job.id,
                        'name': job.name,
                        'next_run': job.next_run_time.isoformat() if job.next_run_time else None
                    })
            
            return {
                'is_running': self.is_running,
                'config': self.config,
                'jobs': jobs
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على حالة المجدول: {e}")
            return {'is_running': False, 'config': {}, 'jobs': []}


# مثيل عام للمجدول
activation_code_scheduler = None

def get_scheduler(firebase_manager=None) -> ActivationCodeScheduler:
    """الحصول على مثيل المجدول"""
    global activation_code_scheduler
    
    if activation_code_scheduler is None:
        activation_code_scheduler = ActivationCodeScheduler(firebase_manager)
    
    if firebase_manager and not activation_code_scheduler.firebase_manager:
        activation_code_scheduler.firebase_manager = firebase_manager
    
    return activation_code_scheduler

def start_scheduler(firebase_manager=None):
    """بدء المجدول"""
    scheduler = get_scheduler(firebase_manager)
    scheduler.start()

def stop_scheduler():
    """إيقاف المجدول"""
    global activation_code_scheduler
    if activation_code_scheduler:
        activation_code_scheduler.shutdown()
