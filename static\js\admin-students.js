/**
 * إدارة الطلاب - لوحة تحكم الأدمن
 */

let studentsData = [];
let instructorsData = [];
let specializationsData = [];
let currentFilters = {
    search: '',
    instructor: '',
    specialization: '',
    status: ''
};

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadInitialData();
    setupEventListeners();
});

/**
 * تحميل البيانات الأولية
 */
async function loadInitialData() {
    showLoading(true);
    
    try {
        // تحميل البيانات بشكل متوازي
        const [studentsResponse, instructorsResponse, specializationsResponse, statisticsResponse] = await Promise.all([
            fetchWithAuth('/api/admin/students'),
            fetchWithAuth('/api/admin/instructors'),
            fetchWithAuth('/api/admin/specializations'),
            fetchWithAuth('/api/admin/students/statistics')
        ]);

        if (studentsResponse.success) {
            studentsData = studentsResponse.students;
        }

        if (instructorsResponse.success) {
            instructorsData = instructorsResponse.instructors;
            populateInstructorFilters();
        }

        if (specializationsResponse.success) {
            specializationsData = specializationsResponse.specializations;
            populateSpecializationFilters();
        }

        if (statisticsResponse.success) {
            updateStatistics(statisticsResponse.statistics);
        }

        renderStudents();
        
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        showError('حدث خطأ في تحميل البيانات');
    } finally {
        showLoading(false);
    }
}

/**
 * إعداد مستمعي الأحداث
 */
function setupEventListeners() {
    // البحث
    const searchInput = document.getElementById('searchInput');
    searchInput.addEventListener('input', debounce(handleSearch, 300));

    // الفلاتر
    document.getElementById('instructorFilter').addEventListener('change', handleInstructorFilter);
    document.getElementById('specializationFilter').addEventListener('change', handleSpecializationFilter);
    document.getElementById('statusFilter').addEventListener('change', handleStatusFilter);
}

/**
 * معالجة البحث
 */
function handleSearch(event) {
    currentFilters.search = event.target.value.trim();
    applyFilters();
}

/**
 * معالجة فلتر المدرس
 */
function handleInstructorFilter(event) {
    currentFilters.instructor = event.target.value;
    applyFilters();
}

/**
 * معالجة فلتر التخصص
 */
function handleSpecializationFilter(event) {
    currentFilters.specialization = event.target.value;
    applyFilters();
}

/**
 * معالجة فلتر الحالة
 */
function handleStatusFilter(event) {
    currentFilters.status = event.target.value;
    applyFilters();
}

/**
 * تطبيق الفلاتر
 */
function applyFilters() {
    let filteredStudents = [...studentsData];

    // فلتر البحث
    if (currentFilters.search) {
        const searchTerm = currentFilters.search.toLowerCase();
        filteredStudents = filteredStudents.filter(student => 
            student.full_name?.toLowerCase().includes(searchTerm) ||
            student.email?.toLowerCase().includes(searchTerm) ||
            student.telegram_id?.toLowerCase().includes(searchTerm)
        );
    }

    // فلتر المدرس
    if (currentFilters.instructor) {
        if (currentFilters.instructor === 'unassigned') {
            filteredStudents = filteredStudents.filter(student => !student.assigned_instructor);
        } else {
            filteredStudents = filteredStudents.filter(student => 
                student.assigned_instructor === currentFilters.instructor
            );
        }
    }

    // فلتر التخصص
    if (currentFilters.specialization) {
        filteredStudents = filteredStudents.filter(student => 
            student.specialization_id === currentFilters.specialization
        );
    }

    // فلتر الحالة
    if (currentFilters.status) {
        const isActive = currentFilters.status === 'active';
        filteredStudents = filteredStudents.filter(student => student.active === isActive);
    }

    renderStudents(filteredStudents);
}

/**
 * عرض الطلاب
 */
function renderStudents(students = studentsData) {
    const container = document.getElementById('studentsContainer');
    const noDataMessage = document.getElementById('noDataMessage');

    if (students.length === 0) {
        container.innerHTML = '';
        noDataMessage.classList.remove('d-none');
        return;
    }

    noDataMessage.classList.add('d-none');

    const studentsHTML = students.map(student => createStudentCard(student)).join('');
    container.innerHTML = studentsHTML;
}

/**
 * إنشاء بطاقة طالب
 */
function createStudentCard(student) {
    const avatar = student.full_name ? student.full_name.charAt(0).toUpperCase() : 'ط';
    const statusClass = student.active ? 'text-success' : 'text-danger';
    const statusIcon = student.active ? 'fa-check-circle' : 'fa-times-circle';
    const statusText = student.active ? 'نشط' : 'غير نشط';

    const instructorBadge = student.assigned_instructor ? 
        `<span class="instructor-badge instructor-assigned">
            <i class="fas fa-user-tie me-1"></i>
            ${student.instructor_name || 'غير محدد'}
        </span>` :
        `<span class="instructor-badge instructor-unassigned">
            <i class="fas fa-user-slash me-1"></i>
            غير مرتبط
        </span>`;

    return `
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="student-card p-3">
                <div class="d-flex align-items-start">
                    <div class="student-avatar me-3">
                        ${avatar}
                    </div>
                    <div class="student-info">
                        <h6 class="mb-1">${student.full_name || 'غير محدد'}</h6>
                        <p class="student-meta mb-1">
                            <i class="fas fa-envelope me-1"></i>
                            ${student.email || 'غير محدد'}
                        </p>
                        <p class="student-meta mb-1">
                            <i class="fab fa-telegram me-1"></i>
                            ${student.telegram_id || 'غير محدد'}
                        </p>
                        <p class="student-meta mb-2">
                            <i class="fas fa-graduation-cap me-1"></i>
                            ${student.specialization_name || 'غير محدد'}
                        </p>
                        
                        <div class="d-flex align-items-center justify-content-between mb-2">
                            ${instructorBadge}
                            <span class="course-count">
                                <i class="fas fa-book me-1"></i>
                                ${student.enrollments_count || 0} كورس
                            </span>
                        </div>
                        
                        <div class="d-flex align-items-center justify-content-between">
                            <small class="${statusClass}">
                                <i class="fas ${statusIcon} me-1"></i>
                                ${statusText}
                            </small>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary btn-sm" 
                                        onclick="editStudentInstructor('${student.id}')"
                                        title="تعديل المدرس المسؤول">
                                    <i class="fas fa-user-edit"></i>
                                </button>
                                <button class="btn btn-outline-info btn-sm" 
                                        onclick="viewStudentDetails('${student.id}')"
                                        title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * تحديث الإحصائيات
 */
function updateStatistics(statistics) {
    document.getElementById('totalStudents').textContent = statistics.total_students || 0;
    document.getElementById('assignedStudents').textContent = statistics.assigned_students || 0;
    document.getElementById('unassignedStudents').textContent = statistics.unassigned_students || 0;
    
    // حساب الطلاب النشطين
    const activeStudents = studentsData.filter(s => s.active).length;
    document.getElementById('activeStudents').textContent = activeStudents;
}

/**
 * ملء فلاتر المدرسين
 */
function populateInstructorFilters() {
    const select = document.getElementById('instructorFilter');
    const newInstructorSelect = document.getElementById('newInstructor');
    
    // مسح الخيارات الموجودة (عدا الافتراضية)
    while (select.children.length > 2) {
        select.removeChild(select.lastChild);
    }
    
    while (newInstructorSelect.children.length > 2) {
        newInstructorSelect.removeChild(newInstructorSelect.lastChild);
    }

    instructorsData.forEach(instructor => {
        const option1 = new Option(instructor.full_name, instructor.id);
        const option2 = new Option(instructor.full_name, instructor.id);
        select.appendChild(option1);
        newInstructorSelect.appendChild(option2);
    });
}

/**
 * ملء فلاتر التخصصات
 */
function populateSpecializationFilters() {
    const select = document.getElementById('specializationFilter');
    
    // مسح الخيارات الموجودة (عدا الافتراضية)
    while (select.children.length > 1) {
        select.removeChild(select.lastChild);
    }

    specializationsData.forEach(specialization => {
        const option = new Option(specialization.name, specialization.id);
        select.appendChild(option);
    });
}

/**
 * تعديل المدرس المسؤول عن طالب
 */
function editStudentInstructor(studentId) {
    const student = studentsData.find(s => s.id === studentId);
    if (!student) return;

    // ملء بيانات الطالب في النافذة المنبثقة
    document.getElementById('studentId').value = studentId;
    document.getElementById('modalStudentName').textContent = student.full_name || 'غير محدد';
    document.getElementById('modalStudentEmail').textContent = student.email || 'غير محدد';
    document.getElementById('modalStudentSpecialization').textContent = student.specialization_name || 'غير محدد';
    
    const avatar = student.full_name ? student.full_name.charAt(0).toUpperCase() : 'ط';
    document.getElementById('modalStudentAvatar').textContent = avatar;

    // تحديد المدرس الحالي
    const newInstructorSelect = document.getElementById('newInstructor');
    newInstructorSelect.value = student.assigned_instructor || '';

    // عرض النافذة المنبثقة
    const modal = new bootstrap.Modal(document.getElementById('instructorModal'));
    modal.show();
}

/**
 * حفظ تعيين المدرس
 */
async function saveInstructorAssignment() {
    const studentId = document.getElementById('studentId').value;
    const instructorId = document.getElementById('newInstructor').value || null;

    try {
        const response = await fetchWithAuth(`/api/admin/students/${studentId}/instructor`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                instructor_id: instructorId
            })
        });

        if (response.success) {
            showSuccess('تم تحديث المدرس المسؤول بنجاح');
            
            // إغلاق النافذة المنبثقة
            const modal = bootstrap.Modal.getInstance(document.getElementById('instructorModal'));
            modal.hide();
            
            // تحديث البيانات
            await refreshData();
        } else {
            showError(response.message || 'فشل في تحديث المدرس المسؤول');
        }
    } catch (error) {
        console.error('خطأ في حفظ التعيين:', error);
        showError('حدث خطأ في النظام');
    }
}

/**
 * عرض تفاصيل الطالب
 */
function viewStudentDetails(studentId) {
    const student = studentsData.find(s => s.id === studentId);
    if (!student) return;

    // يمكن إضافة نافذة منبثقة لعرض تفاصيل أكثر
    console.log('عرض تفاصيل الطالب:', student);
}

/**
 * مسح الفلاتر
 */
function clearFilters() {
    currentFilters = {
        search: '',
        instructor: '',
        specialization: '',
        status: ''
    };

    document.getElementById('searchInput').value = '';
    document.getElementById('instructorFilter').value = '';
    document.getElementById('specializationFilter').value = '';
    document.getElementById('statusFilter').value = '';

    renderStudents();
}

/**
 * تحديث البيانات
 */
async function refreshData() {
    await loadInitialData();
}

/**
 * عرض/إخفاء مؤشر التحميل
 */
function showLoading(show) {
    const spinner = document.getElementById('loadingSpinner');
    const container = document.getElementById('studentsContainer');
    
    if (show) {
        spinner.classList.remove('d-none');
        container.innerHTML = '';
    } else {
        spinner.classList.add('d-none');
    }
}

// دوال مساعدة
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
