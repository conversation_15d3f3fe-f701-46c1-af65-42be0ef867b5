#!/usr/bin/env python3
"""
اختبار إصلاح مشكلة عدم ظهور الكورسات للمدرس
Test fix for instructor courses not showing issue
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.firebase_utils import get_firebase_manager
from utils.auth_utils import get_auth_manager
from utils.jwt_utils import get_jwt_manager

def test_instructor_courses_fix():
    """اختبار إصلاح مشكلة الكورسات"""
    
    print("🔍 اختبار إصلاح مشكلة عدم ظهور الكورسات للمدرس...")
    
    try:
        # الحصول على مدير Firebase
        firebase_manager = get_firebase_manager()
        
        if not firebase_manager:
            print("❌ فشل في الحصول على مدير Firebase")
            return False
        
        print("✅ تم الحصول على مدير Firebase")
        
        # البحث عن مدرس موجود
        print("\n🔍 البحث عن مدرس موجود...")
        instructors = firebase_manager.get_all_instructors()
        
        if not instructors:
            print("❌ لا يوجد مدرسين في النظام")
            return False
        
        instructor = instructors[0]
        instructor_id = instructor.get('id')
        instructor_name = instructor.get('full_name', 'غير محدد')
        
        print(f"✅ تم العثور على مدرس: {instructor_name} (ID: {instructor_id})")
        
        # جلب الكورسات باستخدام الطريقة المصححة
        print(f"\n🔍 جلب كورسات المدرس {instructor_name}...")
        courses = firebase_manager.get_courses_by_instructor(instructor_id)
        
        print(f"📊 عدد الكورسات الموجودة: {len(courses)}")
        
        if courses:
            print("\n📚 الكورسات الموجودة:")
            for i, course in enumerate(courses, 1):
                title = course.get('title', 'بدون عنوان')
                status = course.get('status', 'غير محدد')
                created_at = course.get('created_at', 'غير محدد')
                print(f"  {i}. {title} - الحالة: {status} - تاريخ الإنشاء: {created_at}")
        else:
            print("⚠️ لا توجد كورسات لهذا المدرس")
        
        # اختبار إنشاء كورس تجريبي
        print(f"\n🔍 اختبار إنشاء كورس تجريبي للمدرس {instructor_name}...")
        
        test_course_data = {
            'title': 'كورس تجريبي - اختبار الإصلاح',
            'description': 'كورس تجريبي لاختبار إصلاح مشكلة عدم ظهور الكورسات',
            'instructor_id': instructor_id,  # استخدام instructor_id الصحيح
            'stage': 2,
            'status': 'draft',
            'is_general': False,
            'specialization_id': instructor.get('specialization_id'),
            'enrollment_count': 0,
            'lesson_count': 0
        }
        
        course_id = firebase_manager.create_course(test_course_data)
        
        if course_id:
            print(f"✅ تم إنشاء كورس تجريبي بنجاح (ID: {course_id})")
            
            # التحقق من ظهور الكورس الجديد
            print("\n🔍 التحقق من ظهور الكورس الجديد...")
            updated_courses = firebase_manager.get_courses_by_instructor(instructor_id)
            
            print(f"📊 عدد الكورسات بعد الإضافة: {len(updated_courses)}")
            
            # البحث عن الكورس الجديد
            new_course_found = False
            for course in updated_courses:
                if course.get('id') == course_id:
                    new_course_found = True
                    print(f"✅ تم العثور على الكورس الجديد: {course.get('title')}")
                    break
            
            if not new_course_found:
                print("❌ لم يتم العثور على الكورس الجديد - المشكلة لم تُحل!")
                return False
            
            # حذف الكورس التجريبي
            print(f"\n🗑️ حذف الكورس التجريبي...")
            if firebase_manager.delete_course(course_id):
                print("✅ تم حذف الكورس التجريبي بنجاح")
            else:
                print("⚠️ فشل في حذف الكورس التجريبي")
        
        else:
            print("❌ فشل في إنشاء الكورس التجريبي")
            return False
        
        print("\n🎉 تم اختبار الإصلاح بنجاح! المشكلة تم حلها.")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    success = test_instructor_courses_fix()
    if success:
        print("\n✅ الاختبار نجح - الإصلاح يعمل بشكل صحيح")
        sys.exit(0)
    else:
        print("\n❌ الاختبار فشل - هناك مشكلة في الإصلاح")
        sys.exit(1)
