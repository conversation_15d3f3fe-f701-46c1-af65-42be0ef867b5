# إعدادات Flask
FLASK_SECRET_KEY=your_secret_key_here_change_this_in_production
FLASK_ENV=development
FLASK_DEBUG=True

# إعدادات Firebase
FIREBASE_TYPE=service_account
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_PRIVATE_KEY_ID=your_private_key_id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key_here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=your_client_email
FIREBASE_CLIENT_ID=your_client_id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=your_client_cert_url
FIREBASE_DATABASE_URL=https://your_project_id-default-rtdb.firebaseio.com/

# إعدادات بوت التليجرام
TELEGRAM_BOT_TOKEN=your_bot_token_here
BOT_OWNER_TELEGRAM_ID=your_telegram_id_here

# إعدادات المنصة
PLATFORM_URL=http://localhost:5000
PLATFORM_NAME=منصة الكورسات التعليمية

# إعدادات الأمان
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_EXPIRATION_HOURS=24

# إعدادات قاعدة البيانات
DATABASE_URL=your_firebase_database_url
