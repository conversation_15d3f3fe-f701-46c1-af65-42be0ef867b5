#!/usr/bin/env python3
"""
إصلاح تخصص المدرس - تحديث معرف التخصص للمدرس
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.firebase_utils import FirebaseManager

def fix_instructor_specialization():
    """إصلاح تخصص المدرس"""
    try:
        firebase_manager = FirebaseManager()

        # معرف المدرس المعروف من الاختبار السابق
        instructor_id = "-OU9DAh78GJ6woLcC34G"
        instructor_email = "<EMAIL>"

        print(f"🔍 البحث عن المدرس: {instructor_id}")

        # استخدام get_user مباشرة
        instructor = firebase_manager.get_user(instructor_id)

        if not instructor:
            print(f"❌ لم يتم العثور على المدرس")
            return
        print(f"👤 المدرس: {instructor.get('full_name')} ({instructor_email})")
        print(f"🆔 معرف المدرس: {instructor_id}")
        print(f"🎯 التخصص الحالي: {instructor.get('specialization_id')}")
        
        # الحصول على التخصصات المتاحة
        specializations = firebase_manager.get_all_specializations()
        print(f"\n📋 التخصصات المتاحة:")
        for spec in specializations:
            print(f"   - {spec['name']} ({spec['name_en']}) - ID: {spec['id']}")
        
        # البحث عن تخصص التحليل الطبي
        medical_analysis_spec = None
        for spec in specializations:
            if spec['name_en'] == 'Medical Laboratory':
                medical_analysis_spec = spec
                break
        
        if not medical_analysis_spec:
            print("❌ لم يتم العثور على تخصص التحليل الطبي")
            return
            
        print(f"\n🎯 تخصص التحليل الطبي الصحيح:")
        print(f"   - الاسم: {medical_analysis_spec['name']}")
        print(f"   - الاسم الإنجليزي: {medical_analysis_spec['name_en']}")
        print(f"   - المعرف: {medical_analysis_spec['id']}")
        
        # تحديث تخصص المدرس
        print(f"\n🔄 تحديث تخصص المدرس...")
        
        # تحديث البيانات في Firebase باستخدام update_user
        success = firebase_manager.update_user(instructor_id, {
            'specialization_id': medical_analysis_spec['id']
        })

        if not success:
            print("❌ فشل في تحديث البيانات")
            return
        
        print(f"✅ تم تحديث تخصص المدرس بنجاح!")
        print(f"   - التخصص الجديد: {medical_analysis_spec['id']}")
        
        # التحقق من التحديث
        updated_instructor = firebase_manager.get_user(instructor_id)
        print(f"\n🔍 التحقق من التحديث:")
        print(f"   - التخصص بعد التحديث: {updated_instructor.get('specialization_id')}")
        
        # اختبار إنشاء كورس
        print(f"\n🧪 اختبار إنشاء كورس...")
        test_result = firebase_manager.can_instructor_create_course(
            instructor_id, 
            medical_analysis_spec['id'], 
            2, 
            False
        )
        print(f"   - النتيجة: {test_result}")
        
        if test_result['allowed']:
            print("✅ يمكن للمدرس الآن إنشاء كورسات!")
        else:
            print(f"❌ لا يزال هناك مشكلة: {test_result['reason']}")
            
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    fix_instructor_specialization()
