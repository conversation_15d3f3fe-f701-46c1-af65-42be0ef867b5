#!/usr/bin/env python3
"""
إنشاء مدرس تجريبي لاختبار النظام
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.firebase_utils import get_firebase_manager
import uuid

def create_test_instructor():
    """إنشاء مدرس تجريبي"""
    
    try:
        print("🔍 إنشاء مدرس تجريبي...")
        
        # الحصول على مدير Firebase
        firebase_manager = get_firebase_manager()
        
        if not firebase_manager:
            print("❌ فشل في الحصول على مدير Firebase")
            return
        
        print("✅ تم الحصول على مدير Firebase")
        
        # بيانات المدرس التجريبي
        instructor_data = {
            'email': '<EMAIL>',
            'password': 'test123',  # كلمة مرور بسيطة للاختبار
            'full_name': 'أحمد محمد المدرس',
            'first_name': 'أحمد',
            'last_name': 'محمد المدرس',
            'role': 'instructor',
            'specialization_id': 'math',  # تخصص الرياضيات
            'active': True,
            'permissions': {
                'can_create_courses': True,
                'can_manage_students': True,
                'allowed_stages': [1, 2, 3, 4],
                'can_create_general_courses': False
            }
        }
        
        # إنشاء المدرس
        result = firebase_manager.create_user(instructor_data)
        
        if result:
            print(f"✅ تم إنشاء المدرس التجريبي بنجاح")
            print(f"   الاسم: {instructor_data['full_name']}")
            print(f"   البريد: {instructor_data['email']}")
            print(f"   المعرف: {result}")
            print(f"   التخصص: {instructor_data['specialization_id']}")
            print(f"   الصلاحيات: {instructor_data['permissions']}")
        else:
            print("❌ فشل في إنشاء المدرس التجريبي")
        
        # التحقق من وجود المدرس
        print("\n🔍 التحقق من وجود المدرس...")
        instructors = firebase_manager.get_all_instructors()
        print(f"👨‍🏫 عدد المدرسين الآن: {len(instructors)}")
        
        if instructors:
            print("\n📋 قائمة المدرسين:")
            for instructor in instructors:
                print(f"  - {instructor.get('full_name', 'بدون اسم')} ({instructor.get('email', 'بدون إيميل')})")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المدرس التجريبي: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    create_test_instructor()
