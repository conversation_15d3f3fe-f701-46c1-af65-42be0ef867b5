#!/usr/bin/env python3
"""
إصلاح بسيط لتخصص المدرس
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app, get_firebase_manager

def fix_instructor():
    """إصلاح تخصص المدرس"""
    with app.app_context():
        firebase_manager = get_firebase_manager()
        
        # معرف المدرس
        instructor_id = "-OU9DAh78GJ6woLcC34G"
        
        print(f"🔍 البحث عن المدرس: {instructor_id}")
        
        # الحصول على بيانات المدرس
        instructor = firebase_manager.get_user(instructor_id)
        
        if instructor:
            print(f"✅ وجد المدرس: {instructor.get('full_name')}")
            print(f"📧 البريد: {instructor.get('email')}")
            print(f"🎯 التخصص الحالي: {instructor.get('specialization_id')}")
            
            # الحصول على التخصصات
            specializations = firebase_manager.get_all_specializations()
            medical_lab_spec = None
            
            for spec in specializations:
                if spec.get('name_en') == 'Medical Laboratory':
                    medical_lab_spec = spec
                    break
            
            if medical_lab_spec:
                print(f"🎯 التخصص الصحيح: {medical_lab_spec['name']} - {medical_lab_spec['id']}")
                
                # تحديث التخصص
                success = firebase_manager.update_user(instructor_id, {
                    'specialization_id': medical_lab_spec['id']
                })
                
                if success:
                    print("✅ تم تحديث التخصص بنجاح!")
                    
                    # اختبار الصلاحيات
                    test_result = firebase_manager.can_instructor_create_course(
                        instructor_id, 
                        medical_lab_spec['id'], 
                        2, 
                        False
                    )
                    print(f"🧪 اختبار إنشاء كورس: {test_result}")
                else:
                    print("❌ فشل في التحديث")
            else:
                print("❌ لم يتم العثور على تخصص التحليل الطبي")
        else:
            print("❌ لم يتم العثور على المدرس")

if __name__ == "__main__":
    fix_instructor()
