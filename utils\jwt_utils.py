"""
وحدة إدارة JWT Tokens
JWT Token Management Module

هذه الوحدة مسؤولة عن:
- إنشاء JWT tokens للمستخدمين
- التحقق من صحة التوكينات
- استخراج البيانات من التوكينات
- إدارة انتهاء الصلاحية
"""

import jwt
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, Optional, Union
from functools import wraps
from flask import current_app, request, jsonify

# إعداد نظام التسجيل
logger = logging.getLogger(__name__)

class JWTManager:
    """مدير JWT Tokens"""
    
    def __init__(self):
        self.algorithm = 'HS256'
    
    def generate_token(self, user_data: Dict[str, Any], expires_in_hours: Optional[int] = None) -> str:
        """
        إنشاء JWT token للمستخدم
        Generate JWT token for user
        
        Args:
            user_data: بيانات المستخدم
            expires_in_hours: مدة انتهاء الصلاحية بالساعات
            
        Returns:
            JWT token string
        """
        try:
            # الحصول على إعدادات JWT
            secret_key = current_app.config.get('JWT_SECRET_KEY')
            if not secret_key:
                raise ValueError("JWT_SECRET_KEY غير محدد في الإعدادات")
            
            # تحديد مدة انتهاء الصلاحية
            if expires_in_hours is None:
                expires_in_hours = current_app.config.get('JWT_EXPIRATION_HOURS', 24)
            
            # إنشاء payload
            now = datetime.now(timezone.utc)
            payload = {
                'user_id': user_data.get('user_id'),
                'email': user_data.get('email'),
                'role': user_data.get('role'),
                'telegram_id': user_data.get('telegram_id'),
                'first_name': user_data.get('first_name'),
                'last_name': user_data.get('last_name'),
                'specialization_id': user_data.get('specialization_id'),
                'iat': now,  # issued at
                'exp': now + timedelta(hours=expires_in_hours),  # expiration
                'iss': current_app.config.get('PLATFORM_NAME', 'منصة الكورسات'),  # issuer
            }
            
            # إنشاء التوكين
            token = jwt.encode(payload, secret_key, algorithm=self.algorithm)
            
            logger.info(f"تم إنشاء JWT token للمستخدم: {user_data.get('email')}")
            return token
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء JWT token: {e}")
            raise
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        التحقق من صحة JWT token
        Verify JWT token validity
        
        Args:
            token: JWT token string
            
        Returns:
            بيانات المستخدم إذا كان التوكين صحيح، None إذا كان غير صحيح
        """
        try:
            secret_key = current_app.config.get('JWT_SECRET_KEY')
            if not secret_key:
                logger.error("JWT_SECRET_KEY غير محدد في الإعدادات")
                return None
            
            # فك تشفير التوكين
            payload = jwt.decode(token, secret_key, algorithms=[self.algorithm])
            
            # التحقق من انتهاء الصلاحية
            exp_timestamp = payload.get('exp')
            if exp_timestamp:
                exp_datetime = datetime.fromtimestamp(exp_timestamp, tz=timezone.utc)
                if datetime.now(timezone.utc) > exp_datetime:
                    logger.warning("JWT token منتهي الصلاحية")
                    return None
            
            logger.debug(f"تم التحقق من JWT token للمستخدم: {payload.get('email')}")
            return payload
            
        except jwt.ExpiredSignatureError:
            logger.warning("JWT token منتهي الصلاحية")
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f"JWT token غير صحيح: {e}")
            return None
        except Exception as e:
            logger.error(f"خطأ في التحقق من JWT token: {e}")
            return None
    
    def refresh_token(self, token: str) -> Optional[str]:
        """
        تجديد JWT token
        Refresh JWT token
        
        Args:
            token: JWT token الحالي
            
        Returns:
            JWT token جديد أو None إذا فشل التجديد
        """
        try:
            # التحقق من التوكين الحالي
            payload = self.verify_token(token)
            if not payload:
                return None
            
            # إنشاء توكين جديد بنفس البيانات
            user_data = {
                'user_id': payload.get('user_id'),
                'email': payload.get('email'),
                'role': payload.get('role'),
                'telegram_id': payload.get('telegram_id'),
                'first_name': payload.get('first_name'),
                'last_name': payload.get('last_name'),
                'specialization_id': payload.get('specialization_id')
            }
            
            new_token = self.generate_token(user_data)
            logger.info(f"تم تجديد JWT token للمستخدم: {payload.get('email')}")
            return new_token
            
        except Exception as e:
            logger.error(f"خطأ في تجديد JWT token: {e}")
            return None
    
    def extract_token_from_request(self, request_obj=None) -> Optional[str]:
        """
        استخراج JWT token من الطلب
        Extract JWT token from request
        
        Args:
            request_obj: Flask request object (اختياري)
            
        Returns:
            JWT token string أو None
        """
        try:
            if request_obj is None:
                request_obj = request
            
            # البحث في Authorization header
            auth_header = request_obj.headers.get('Authorization')
            if auth_header and auth_header.startswith('Bearer '):
                return auth_header.split(' ')[1]
            
            # البحث في cookies
            token = request_obj.cookies.get('auth_token')
            if token:
                return token
            
            # البحث في form data
            token = request_obj.form.get('token')
            if token:
                return token
            
            # البحث في query parameters
            token = request_obj.args.get('token')
            if token:
                return token
            
            return None
            
        except Exception as e:
            logger.error(f"خطأ في استخراج JWT token من الطلب: {e}")
            return None
    
    def get_user_from_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        الحصول على بيانات المستخدم من التوكين
        Get user data from token
        
        Args:
            token: JWT token string
            
        Returns:
            بيانات المستخدم أو None
        """
        payload = self.verify_token(token)
        if not payload:
            return None
        
        return {
            'user_id': payload.get('user_id'),
            'email': payload.get('email'),
            'role': payload.get('role'),
            'telegram_id': payload.get('telegram_id'),
            'first_name': payload.get('first_name'),
            'last_name': payload.get('last_name'),
            'specialization_id': payload.get('specialization_id'),
            'full_name': f"{payload.get('first_name', '')} {payload.get('last_name', '')}".strip()
        }
    
    def is_token_expired(self, token: str) -> bool:
        """
        التحقق من انتهاء صلاحية التوكين
        Check if token is expired
        
        Args:
            token: JWT token string
            
        Returns:
            True إذا كان منتهي الصلاحية، False إذا كان صالح
        """
        try:
            payload = self.verify_token(token)
            return payload is None
        except Exception:
            return True

# إنشاء مثيل مشترك
jwt_manager = JWTManager()

def get_jwt_manager() -> JWTManager:
    """الحصول على مثيل JWT Manager"""
    return jwt_manager

# Decorators مفيدة
def token_required(f):
    """
    Decorator للتحقق من وجود JWT token صحيح
    Decorator to require valid JWT token
    """
    @wraps(f)
    def decorated(*args, **kwargs):
        token = jwt_manager.extract_token_from_request()
        if not token:
            return jsonify({
                'success': False,
                'message': 'مطلوب تسجيل الدخول',
                'error': 'TOKEN_MISSING'
            }), 401
        
        user_data = jwt_manager.get_user_from_token(token)
        if not user_data:
            return jsonify({
                'success': False,
                'message': 'جلسة غير صحيحة',
                'error': 'TOKEN_INVALID'
            }), 401
        
        # إضافة بيانات المستخدم للطلب
        request.current_user = user_data
        return f(*args, **kwargs)
    
    return decorated

def role_required(*allowed_roles):
    """
    Decorator للتحقق من دور المستخدم
    Decorator to require specific user roles
    """
    def decorator(f):
        @wraps(f)
        @token_required
        def decorated(*args, **kwargs):
            user_role = request.current_user.get('role')
            if user_role not in allowed_roles:
                return jsonify({
                    'success': False,
                    'message': 'ليس لديك صلاحية للوصول لهذه الصفحة',
                    'error': 'INSUFFICIENT_PERMISSIONS'
                }), 403
            
            return f(*args, **kwargs)
        return decorated
    return decorator
