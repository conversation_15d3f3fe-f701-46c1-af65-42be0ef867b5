#!/usr/bin/env python3
"""
إنشاء بيانات تجريبية لاختبار النظام
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import firebase_admin
from firebase_admin import credentials, db
from datetime import datetime, timezone
import uuid
import hashlib

def initialize_firebase():
    """تهيئة Firebase"""
    try:
        # التحقق من وجود التطبيق مسبقاً
        try:
            app = firebase_admin.get_app()
            print("✅ Firebase مهيأ مسبقاً")
        except ValueError:
            # إنشاء التطبيق إذا لم يكن موجوداً
            from config import Config
            config = Config()
            
            cred = credentials.Certificate(config.FIREBASE_CONFIG)
            app = firebase_admin.initialize_app(cred, {
                'databaseURL': config.FIREBASE_DATABASE_URL
            })
            print("✅ تم تهيئة Firebase")
        
        return db.reference()
    except Exception as e:
        print(f"❌ فشل في تهيئة Firebase: {e}")
        return None

def create_test_instructor_and_courses():
    """إنشاء مدرس تجريبي مع كورسات"""
    
    print("🚀 إنشاء بيانات تجريبية لاختبار النظام...")
    
    # تهيئة Firebase
    database = initialize_firebase()
    if not database:
        return False
    
    try:
        # إنشاء معرف فريد للمدرس
        instructor_id = str(uuid.uuid4())
        
        # كلمة مرور بسيطة للاختبار
        password = "test123"
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        
        # بيانات المدرس التجريبي
        instructor_data = {
            'email': '<EMAIL>',
            'password_hash': password_hash,
            'full_name': 'د. أحمد محمد المدرس',
            'first_name': 'أحمد',
            'last_name': 'محمد المدرس',
            'role': 'instructor',
            'specialization_id': 'medical_analysis',
            'status': 'active',
            'telegram_id': f'test_telegram_{instructor_id[:8]}',
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat(),
            'permissions': {
                'can_create_courses': True,
                'can_manage_students': True,
                'allowed_stages': [2, 3, 4],
                'can_create_general_courses': True
            }
        }
        
        print(f"📝 إنشاء مدرس تجريبي...")
        print(f"   البريد الإلكتروني: {instructor_data['email']}")
        print(f"   كلمة المرور: {password}")
        print(f"   المعرف: {instructor_id}")
        
        # إضافة المدرس إلى قاعدة البيانات
        database.child('users').child(instructor_id).set(instructor_data)
        print("✅ تم إنشاء المدرس التجريبي")
        
        # إنشاء كورسات تجريبية
        courses_data = [
            {
                'title': 'كورس التحليل الطبي - المرحلة الثانية',
                'description': 'كورس شامل في التحليل الطبي للمرحلة الثانية',
                'instructor_id': instructor_id,
                'stage': 2,
                'status': 'draft',
                'is_general': False,
                'specialization_id': 'medical_analysis',
                'enrollment_count': 0,
                'lesson_count': 5,
                'created_at': datetime.now(timezone.utc).isoformat(),
                'updated_at': datetime.now(timezone.utc).isoformat()
            },
            {
                'title': 'كورس التحليل المتقدم - المرحلة الثالثة',
                'description': 'كورس متقدم في التحليل الطبي للمرحلة الثالثة',
                'instructor_id': instructor_id,
                'stage': 3,
                'status': 'published',
                'is_general': False,
                'specialization_id': 'medical_analysis',
                'enrollment_count': 15,
                'lesson_count': 8,
                'created_at': datetime.now(timezone.utc).isoformat(),
                'updated_at': datetime.now(timezone.utc).isoformat()
            },
            {
                'title': 'كورس عام - أساسيات الطب',
                'description': 'كورس عام في أساسيات الطب لجميع التخصصات',
                'instructor_id': instructor_id,
                'stage': 2,
                'status': 'published',
                'is_general': True,
                'specialization_id': None,
                'enrollment_count': 25,
                'lesson_count': 10,
                'created_at': datetime.now(timezone.utc).isoformat(),
                'updated_at': datetime.now(timezone.utc).isoformat()
            }
        ]
        
        print(f"\n📚 إنشاء {len(courses_data)} كورسات تجريبية...")
        
        for i, course_data in enumerate(courses_data, 1):
            course_id = str(uuid.uuid4())
            database.child('courses').child(course_id).set(course_data)
            
            status_emoji = "📝" if course_data['status'] == 'draft' else "✅"
            general_text = " (عام)" if course_data['is_general'] else ""
            
            print(f"  {status_emoji} {i}. {course_data['title']}{general_text}")
            print(f"     الحالة: {course_data['status']}")
            print(f"     المرحلة: {course_data['stage']}")
            print(f"     عدد الطلاب: {course_data['enrollment_count']}")
        
        print(f"\n✅ تم إنشاء جميع البيانات التجريبية بنجاح!")
        
        print(f"\n🔐 بيانات تسجيل الدخول:")
        print(f"   البريد الإلكتروني: {instructor_data['email']}")
        print(f"   كلمة المرور: {password}")
        
        print(f"\n📋 خطوات الاختبار:")
        print(f"1. اذهب إلى: http://127.0.0.1:5000/login")
        print(f"2. سجل دخول بالبيانات أعلاه")
        print(f"3. اذهب إلى: http://127.0.0.1:5000/instructor/courses")
        print(f"4. يجب أن ترى 3 كورسات (1 مسودة + 2 منشورة)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات التجريبية: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = create_test_instructor_and_courses()
    if success:
        print("\n🎉 تم إنشاء البيانات التجريبية بنجاح!")
        sys.exit(0)
    else:
        print("\n❌ فشل في إنشاء البيانات التجريبية")
        sys.exit(1)
