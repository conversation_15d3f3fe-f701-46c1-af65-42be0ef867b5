"""
وحدة إدارة روابط الدعوة
Invitation Links Management Module

هذه الوحدة مسؤولة عن:
- توليد روابط دعوة آمنة للمدرسين والطلاب
- إدارة دورة حياة الروابط (إنشاء، تحقق، استخدام، انتهاء صلاحية)
- التكامل مع Firebase لحفظ واسترجاع بيانات الروابط
- ضمان الأمان والتحقق من صحة الروابط
"""

import secrets
import string
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, Tuple
from urllib.parse import urljoin
from utils.firebase_utils import get_firebase_manager
from models.database_models import DatabaseModels
from config import Config

# إعداد نظام التسجيل
logger = logging.getLogger(__name__)

class InvitationManager:
    """مدير روابط الدعوة"""
    
    def __init__(self):
        """تهيئة مدير الدعوات"""
        self.firebase_manager = get_firebase_manager()
        self.config = Config()
        self.platform_url = self.config.PLATFORM_URL
    
    def generate_secure_link_id(self, length: int = 32) -> str:
        """
        توليد معرف رابط آمن وفريد
        Generate secure and unique link ID
        
        Args:
            length: طول المعرف (افتراضي 32)
            
        Returns:
            معرف الرابط الآمن
        """
        try:
            # استخدام secrets لتوليد معرف آمن
            link_id = secrets.token_urlsafe(length)
            
            # التأكد من عدم وجود المعرف مسبقاً
            existing_link = self.get_invitation_link(link_id)
            if existing_link:
                # إعادة المحاولة مع معرف جديد
                return self.generate_secure_link_id(length)
            
            return link_id
            
        except Exception as e:
            logger.error(f"خطأ في توليد معرف الرابط: {e}")
            return None
    
    def create_instructor_invitation_link(
        self,
        created_by: str,
        instructor_name: str,
        specialization_id: str,
        expires_hours: int = 24
    ) -> Tuple[bool, Optional[str], str]:
        """
        إنشاء رابط دعوة لإنشاء حساب مدرس
        Create instructor account creation invitation link
        
        Args:
            created_by: معرف منشئ الرابط (مالك البوت)
            instructor_name: اسم المدرس
            specialization_id: معرف التخصص
            expires_hours: عدد ساعات انتهاء الصلاحية (افتراضي 24)
            
        Returns:
            (نجح الإنشاء، رابط الدعوة، رسالة)
        """
        try:
            # توليد معرف رابط آمن
            link_id = self.generate_secure_link_id()
            if not link_id:
                return False, None, "فشل في توليد معرف الرابط"
            
            # حساب وقت انتهاء الصلاحية
            expires_at = datetime.now(timezone.utc) + timedelta(hours=expires_hours)
            
            # إعداد بيانات الهدف
            target_data = {
                'instructor_name': instructor_name,
                'specialization_id': specialization_id,
                'created_by_telegram': created_by
            }
            
            # إنشاء نموذج رابط الدعوة
            invitation_data = DatabaseModels.create_invitation_link_model(
                link_id=link_id,
                created_by=created_by,
                link_type='instructor_signup',
                target_data=target_data,
                max_uses=1,  # استخدام واحد فقط للمدرسين
                expires_at=expires_at
            )
            
            # حفظ الرابط في قاعدة البيانات
            success = self.save_invitation_link(link_id, invitation_data)
            if not success:
                return False, None, "فشل في حفظ الرابط في قاعدة البيانات"
            
            # إنشاء رابط البوت مباشرة
            bot_username = self.config.TELEGRAM_BOT_TOKEN.split(':')[0] if self.config.TELEGRAM_BOT_TOKEN else "YourBot"
            invitation_url = f"https://t.me/{bot_username}?start=instructor_{link_id}"

            logger.info(f"تم إنشاء رابط دعوة مدرس: {link_id}")
            return True, invitation_url, "تم إنشاء رابط الدعوة بنجاح"
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء رابط دعوة المدرس: {e}")
            return False, None, "حدث خطأ في النظام"
    
    def create_student_invitation_link(
        self,
        created_by: str,
        instructor_id: str,
        specialization_id: str,
        expires_days: int = 30
    ) -> Tuple[bool, Optional[str], str]:
        """
        إنشاء رابط دعوة للطلاب
        Create student invitation link
        
        Args:
            created_by: معرف منشئ الرابط (المدرس)
            instructor_id: معرف المدرس
            specialization_id: معرف التخصص
            expires_days: عدد أيام انتهاء الصلاحية (افتراضي 30)
            
        Returns:
            (نجح الإنشاء، رابط الدعوة، رسالة)
        """
        try:
            # توليد معرف رابط آمن
            link_id = self.generate_secure_link_id()
            if not link_id:
                return False, None, "فشل في توليد معرف الرابط"
            
            # حساب وقت انتهاء الصلاحية
            expires_at = datetime.now(timezone.utc) + timedelta(days=expires_days)
            
            # إعداد بيانات الهدف
            target_data = {
                'instructor_id': instructor_id,
                'specialization_id': specialization_id
            }
            
            # إنشاء نموذج رابط الدعوة
            invitation_data = DatabaseModels.create_invitation_link_model(
                link_id=link_id,
                created_by=created_by,
                link_type='student_invite',
                target_data=target_data,
                max_uses=-1,  # استخدام غير محدود للطلاب
                expires_at=expires_at
            )
            
            # حفظ الرابط في قاعدة البيانات
            success = self.save_invitation_link(link_id, invitation_data)
            if not success:
                return False, None, "فشل في حفظ الرابط في قاعدة البيانات"
            
            # إنشاء الرابط الكامل
            invitation_url = urljoin(self.platform_url, f"/invite/student/{link_id}")
            
            logger.info(f"تم إنشاء رابط دعوة طلاب: {link_id}")
            return True, invitation_url, "تم إنشاء رابط الدعوة بنجاح"
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء رابط دعوة الطلاب: {e}")
            return False, None, "حدث خطأ في النظام"
    
    def save_invitation_link(self, link_id: str, invitation_data: Dict[str, Any]) -> bool:
        """
        حفظ رابط الدعوة في قاعدة البيانات
        Save invitation link to database
        
        Args:
            link_id: معرف الرابط
            invitation_data: بيانات الرابط
            
        Returns:
            نجح الحفظ
        """
        try:
            if not self.firebase_manager._initialized:
                logger.error("Firebase غير مهيأ")
                return False
            
            # حفظ الرابط في قاعدة البيانات
            self.firebase_manager.database.child('invitation_links').child(link_id).set(invitation_data)
            
            logger.info(f"تم حفظ رابط الدعوة: {link_id}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حفظ رابط الدعوة: {e}")
            return False
    
    def get_invitation_link(self, link_id: str) -> Optional[Dict[str, Any]]:
        """
        الحصول على بيانات رابط الدعوة
        Get invitation link data
        
        Args:
            link_id: معرف الرابط
            
        Returns:
            بيانات الرابط أو None
        """
        try:
            if not self.firebase_manager._initialized:
                logger.error("Firebase غير مهيأ")
                return None
            
            # الحصول على بيانات الرابط
            link_data = self.firebase_manager.database.child('invitation_links').child(link_id).get()
            
            if link_data:
                return link_data
            else:
                return None
                
        except Exception as e:
            logger.error(f"خطأ في الحصول على رابط الدعوة: {e}")
            return None
    
    def validate_invitation_link(self, link_id: str) -> Tuple[bool, Optional[Dict[str, Any]], str]:
        """
        التحقق من صحة رابط الدعوة
        Validate invitation link
        
        Args:
            link_id: معرف الرابط
            
        Returns:
            (صحيح، بيانات الرابط، رسالة)
        """
        try:
            # الحصول على بيانات الرابط
            link_data = self.get_invitation_link(link_id)
            if not link_data:
                return False, None, "رابط الدعوة غير موجود"
            
            # التحقق من حالة الرابط
            if not link_data.get('active', True):
                return False, None, "رابط الدعوة غير مفعل"
            
            # التحقق من انتهاء الصلاحية
            expires_at = link_data.get('expires_at')
            if expires_at:
                expires_datetime = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                if datetime.now(timezone.utc) > expires_datetime:
                    return False, None, "انتهت صلاحية رابط الدعوة"
            
            # التحقق من عدد الاستخدامات
            max_uses = link_data.get('max_uses', 1)
            current_uses = link_data.get('current_uses', 0)
            
            if max_uses > 0 and current_uses >= max_uses:
                return False, None, "تم استنفاد عدد استخدامات الرابط"
            
            return True, link_data, "رابط الدعوة صحيح"
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من رابط الدعوة: {e}")
            return False, None, "حدث خطأ في التحقق من الرابط"
    
    def use_invitation_link(self, link_id: str, used_by: str) -> Tuple[bool, str]:
        """
        استخدام رابط الدعوة (تحديث حالة الاستخدام)
        Use invitation link (update usage status)
        
        Args:
            link_id: معرف الرابط
            used_by: معرف المستخدم الذي استخدم الرابط
            
        Returns:
            (نجح الاستخدام، رسالة)
        """
        try:
            # التحقق من صحة الرابط أولاً
            is_valid, link_data, message = self.validate_invitation_link(link_id)
            if not is_valid:
                return False, message
            
            # تحديث بيانات الاستخدام
            current_uses = link_data.get('current_uses', 0)
            used_by_list = link_data.get('used_by', [])
            
            # إضافة المستخدم الجديد
            if used_by not in used_by_list:
                used_by_list.append(used_by)
            
            # تحديث البيانات
            update_data = {
                'current_uses': current_uses + 1,
                'used_by': used_by_list,
                'last_used_at': datetime.now(timezone.utc).isoformat(),
                'last_used_by': used_by
            }
            
            # التحقق من إنهاء الرابط إذا وصل للحد الأقصى
            max_uses = link_data.get('max_uses', 1)
            if max_uses > 0 and (current_uses + 1) >= max_uses:
                update_data['active'] = False
                update_data['completed_at'] = datetime.now(timezone.utc).isoformat()
            
            # حفظ التحديثات
            self.firebase_manager.database.child('invitation_links').child(link_id).update(update_data)
            
            logger.info(f"تم استخدام رابط الدعوة: {link_id} بواسطة: {used_by}")
            return True, "تم استخدام رابط الدعوة بنجاح"
            
        except Exception as e:
            logger.error(f"خطأ في استخدام رابط الدعوة: {e}")
            return False, "حدث خطأ في استخدام الرابط"


# إنشاء مثيل مشترك من مدير الدعوات
invitation_manager = InvitationManager()

def get_invitation_manager() -> InvitationManager:
    """الحصول على مثيل مدير الدعوات"""
    return invitation_manager
