"""
وحدة إدارة روابط الدعوة للبوت
Bot Invitation Links Management Module

نسخة مبسطة من وحدة إدارة الدعوات خاصة بالبوت
"""

import secrets
import logging
import requests
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, Tuple
from utils.firebase_utils import get_firebase_manager
from models.database_models import DatabaseModels
from bot.config import config

# إعداد نظام التسجيل
logger = logging.getLogger(__name__)

class BotInvitationManager:
    """مدير روابط الدعوة للبوت"""
    
    def __init__(self):
        """تهيئة مدير الدعوات"""
        self.firebase_manager = get_firebase_manager()
        self.platform_url = config.PLATFORM_URL
        self.bot_token = config.TELEGRAM_BOT_TOKEN
    
    def generate_secure_link_id(self, length: int = 32) -> str:
        """توليد معرف رابط آمن وفريد"""
        try:
            link_id = secrets.token_urlsafe(length)
            
            # التأكد من عدم وجود المعرف مسبقاً
            existing_link = self.get_invitation_link(link_id)
            if existing_link:
                return self.generate_secure_link_id(length)
            
            return link_id
            
        except Exception as e:
            logger.error(f"خطأ في توليد معرف الرابط: {e}")
            return None
    
    def create_instructor_invitation_link(
        self,
        created_by: str,
        instructor_name: str,
        specialization_id: str,
        expires_hours: int = 24
    ) -> Tuple[bool, Optional[str], str]:
        """إنشاء رابط دعوة لإنشاء حساب مدرس"""
        try:
            # توليد معرف رابط آمن
            link_id = self.generate_secure_link_id()
            if not link_id:
                return False, None, "فشل في توليد معرف الرابط"
            
            # حساب وقت انتهاء الصلاحية
            expires_at = datetime.now(timezone.utc) + timedelta(hours=expires_hours)
            
            # إعداد بيانات الهدف
            target_data = {
                'instructor_name': instructor_name,
                'specialization_id': specialization_id,
                'created_by_telegram': created_by
            }
            
            # إنشاء نموذج رابط الدعوة
            invitation_data = DatabaseModels.create_invitation_link_model(
                link_id=link_id,
                created_by=created_by,
                link_type='instructor_signup',
                target_data=target_data,
                max_uses=1,
                expires_at=expires_at
            )
            
            # حفظ الرابط في قاعدة البيانات
            success = self.save_invitation_link(link_id, invitation_data)
            if not success:
                return False, None, "فشل في حفظ الرابط في قاعدة البيانات"
            
            # إنشاء رابط البوت مباشرة
            bot_username = self._get_bot_username()
            invitation_url = f"https://t.me/{bot_username}?start=instructor_{link_id}"
            
            logger.info(f"تم إنشاء رابط دعوة مدرس: {link_id}")
            return True, invitation_url, "تم إنشاء رابط الدعوة بنجاح"
            
        except Exception as e:
            logger.error(f"خطأ في إنشاء رابط دعوة المدرس: {e}")
            return False, None, "حدث خطأ في النظام"

    def create_student_invitation_link(
        self,
        created_by: str,
        instructor_id: str,
        specialization_id: str,
        expires_days: int = 30
    ) -> Tuple[bool, Optional[str], str]:
        """إنشاء رابط دعوة لإنشاء حسابات الطلاب"""
        try:
            # توليد معرف رابط آمن
            link_id = self.generate_secure_link_id()
            if not link_id:
                return False, None, "فشل في توليد معرف الرابط"

            # حساب وقت انتهاء الصلاحية
            expires_at = datetime.now(timezone.utc) + timedelta(days=expires_days)

            # إعداد بيانات الهدف
            target_data = {
                'instructor_id': instructor_id,
                'specialization_id': specialization_id,
                'created_by_telegram': created_by
            }

            # إنشاء نموذج رابط الدعوة
            invitation_data = DatabaseModels.create_invitation_link_model(
                link_id=link_id,
                created_by=created_by,
                link_type='student_invite',
                target_data=target_data,
                max_uses=-1,  # استخدام غير محدود للطلاب
                expires_at=expires_at
            )

            # حفظ الرابط في قاعدة البيانات
            success = self.save_invitation_link(link_id, invitation_data)
            if not success:
                return False, None, "فشل في حفظ الرابط في قاعدة البيانات"

            # إنشاء رابط البوت مباشرة
            bot_username = self._get_bot_username()
            invitation_url = f"https://t.me/{bot_username}?start=student_{link_id}"

            logger.info(f"تم إنشاء رابط دعوة طلاب: {link_id}")
            return True, invitation_url, "تم إنشاء رابط الدعوة بنجاح"

        except Exception as e:
            logger.error(f"خطأ في إنشاء رابط دعوة الطلاب: {e}")
            return False, None, "حدث خطأ في النظام"
    
    def _get_bot_username(self) -> str:
        """الحصول على اسم المستخدم للبوت من Telegram API"""
        try:
            if not self.bot_token:
                return "YourBotUsername"

            # استدعاء Telegram API للحصول على معلومات البوت
            url = f"https://api.telegram.org/bot{self.bot_token}/getMe"
            response = requests.get(url, timeout=10)

            if response.status_code == 200:
                data = response.json()
                if data.get('ok') and data.get('result'):
                    username = data['result'].get('username')
                    if username:
                        logger.info(f"تم الحصول على اسم المستخدم للبوت: @{username}")
                        return username

            logger.warning("فشل في الحصول على اسم المستخدم للبوت، استخدام اسم افتراضي")
            return "YourBotUsername"

        except Exception as e:
            logger.error(f"خطأ في الحصول على اسم المستخدم للبوت: {e}")
            return "YourBotUsername"
    
    def save_invitation_link(self, link_id: str, invitation_data: Dict[str, Any]) -> bool:
        """حفظ رابط الدعوة في قاعدة البيانات"""
        try:
            if not self.firebase_manager._initialized:
                logger.error("Firebase غير مهيأ")
                return False
            
            self.firebase_manager.database.child('invitation_links').child(link_id).set(invitation_data)
            logger.info(f"تم حفظ رابط الدعوة: {link_id}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في حفظ رابط الدعوة: {e}")
            return False
    
    def get_invitation_link(self, link_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على بيانات رابط الدعوة"""
        try:
            if not self.firebase_manager._initialized:
                logger.error("Firebase غير مهيأ")
                return None
            
            link_data = self.firebase_manager.database.child('invitation_links').child(link_id).get()
            return link_data if link_data else None
                
        except Exception as e:
            logger.error(f"خطأ في الحصول على رابط الدعوة: {e}")
            return None
    
    def validate_invitation_link(self, link_id: str) -> Tuple[bool, Optional[Dict[str, Any]], str]:
        """التحقق من صحة رابط الدعوة"""
        try:
            link_data = self.get_invitation_link(link_id)
            if not link_data:
                return False, None, "رابط الدعوة غير موجود"
            
            if not link_data.get('active', True):
                return False, None, "رابط الدعوة غير مفعل"
            
            # التحقق من انتهاء الصلاحية
            expires_at = link_data.get('expires_at')
            if expires_at:
                expires_datetime = datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
                if datetime.now(timezone.utc) > expires_datetime:
                    return False, None, "انتهت صلاحية رابط الدعوة"
            
            # التحقق من عدد الاستخدامات
            max_uses = link_data.get('max_uses', 1)
            current_uses = link_data.get('current_uses', 0)
            
            if max_uses > 0 and current_uses >= max_uses:
                return False, None, "تم استنفاد عدد استخدامات الرابط"
            
            return True, link_data, "رابط الدعوة صحيح"
            
        except Exception as e:
            logger.error(f"خطأ في التحقق من رابط الدعوة: {e}")
            return False, None, "حدث خطأ في التحقق من الرابط"


    def use_invitation_link(self, link_id: str, used_by: str) -> Tuple[bool, str]:
        """استخدام رابط الدعوة"""
        try:
            is_valid, link_data, message = self.validate_invitation_link(link_id)
            if not is_valid:
                return False, message
            
            current_uses = link_data.get('current_uses', 0)
            used_by_list = link_data.get('used_by', [])
            
            if used_by not in used_by_list:
                used_by_list.append(used_by)
            
            update_data = {
                'current_uses': current_uses + 1,
                'used_by': used_by_list,
                'last_used_at': datetime.now(timezone.utc).isoformat(),
                'last_used_by': used_by
            }
            
            max_uses = link_data.get('max_uses', 1)
            if max_uses > 0 and (current_uses + 1) >= max_uses:
                update_data['active'] = False
                update_data['completed_at'] = datetime.now(timezone.utc).isoformat()
            
            self.firebase_manager.database.child('invitation_links').child(link_id).update(update_data)
            
            logger.info(f"تم استخدام رابط الدعوة: {link_id} بواسطة: {used_by}")
            return True, "تم استخدام رابط الدعوة بنجاح"
            
        except Exception as e:
            logger.error(f"خطأ في استخدام رابط الدعوة: {e}")
            return False, "حدث خطأ في استخدام الرابط"


# إنشاء مثيل مشترك من مدير الدعوات
bot_invitation_manager = BotInvitationManager()

def get_bot_invitation_manager() -> BotInvitationManager:
    """الحصول على مثيل مدير الدعوات للبوت"""
    return bot_invitation_manager
