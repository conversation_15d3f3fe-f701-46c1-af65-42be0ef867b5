/**
 * إدارة صفحة كورسات المدرس
 */

class InstructorCoursesManager {
    constructor() {
        this.courses = [];
        this.loadingSpinner = document.getElementById('loadingSpinner');
        this.coursesGrid = document.getElementById('coursesGrid');
        this.emptyState = document.getElementById('emptyState');
        this.errorContainer = document.getElementById('errorContainer');
        this.statsRow = document.getElementById('statsRow');
        this.deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        this.courseToDelete = null;
        
        this.init();
    }
    
    async init() {
        try {
            await this.loadCourses();
            this.setupEventListeners();
        } catch (error) {
            console.error('خطأ في تهيئة مدير الكورسات:', error);
            this.showError('حدث خطأ في تحميل البيانات');
        }
    }
    
    async loadCourses() {
        try {
            this.showLoading(true);
            
            const response = await fetchWithAuth('/api/instructor/courses');
            
            if (response.success) {
                this.courses = response.courses || [];
                this.displayCourses();
                this.updateStatistics();
            } else {
                throw new Error(response.message || 'فشل في تحميل الكورسات');
            }
            
        } catch (error) {
            console.error('خطأ في تحميل الكورسات:', error);
            this.showError('حدث خطأ في تحميل الكورسات');
        } finally {
            this.showLoading(false);
        }
    }
    
    displayCourses() {
        if (this.courses.length === 0) {
            this.emptyState.style.display = 'block';
            this.coursesGrid.style.display = 'none';
            this.statsRow.style.display = 'none';
            return;
        }
        
        this.emptyState.style.display = 'none';
        this.coursesGrid.style.display = 'grid';
        this.statsRow.style.display = 'grid';
        
        this.coursesGrid.innerHTML = '';
        
        this.courses.forEach(course => {
            const courseCard = this.createCourseCard(course);
            this.coursesGrid.appendChild(courseCard);
        });
    }
    
    createCourseCard(course) {
        const card = document.createElement('div');
        card.className = 'course-card';
        card.innerHTML = `
            <div class="course-status status-${course.status}">
                ${this.getStatusText(course.status)}
            </div>
            
            <h3 class="course-title">${course.title}</h3>
            
            <div class="course-meta">
                <span>
                    <i class="fas fa-layer-group"></i>
                    ${course.specialization_name || 'كورس عام'}
                </span>
                <span>
                    <i class="fas fa-graduation-cap"></i>
                    المرحلة ${course.stage}
                </span>
                <span>
                    <i class="fas fa-users"></i>
                    ${course.enrollment_count || 0} طالب
                </span>
                <span>
                    <i class="fas fa-book"></i>
                    ${course.lesson_count || 0} درس
                </span>
            </div>
            
            <div class="course-description">
                ${course.description || 'لا يوجد وصف'}
            </div>
            
            <div class="course-actions">
                <a href="/instructor/courses/${course.id}/edit" class="btn-action btn-edit">
                    <i class="fas fa-edit"></i>تعديل
                </a>
                <a href="/instructor/courses/${course.id}/lessons" class="btn-action btn-view">
                    <i class="fas fa-book-open"></i>الدروس
                </a>
                <a href="/instructor/courses/${course.id}/students" class="btn-action btn-view">
                    <i class="fas fa-users"></i>الطلاب
                </a>
                <button class="btn-action btn-delete" onclick="coursesManager.confirmDelete('${course.id}', '${course.title}')">
                    <i class="fas fa-trash"></i>حذف
                </button>
            </div>
        `;
        
        return card;
    }
    
    getStatusText(status) {
        const statusMap = {
            'published': 'منشور',
            'draft': 'مسودة',
            'archived': 'مؤرشف'
        };
        return statusMap[status] || status;
    }
    
    updateStatistics() {
        const stats = this.calculateStatistics();
        
        document.getElementById('totalCourses').textContent = stats.total;
        document.getElementById('publishedCourses').textContent = stats.published;
        document.getElementById('draftCourses').textContent = stats.draft;
        document.getElementById('totalEnrollments').textContent = stats.enrollments;
    }
    
    calculateStatistics() {
        return {
            total: this.courses.length,
            published: this.courses.filter(c => c.status === 'published').length,
            draft: this.courses.filter(c => c.status === 'draft').length,
            enrollments: this.courses.reduce((sum, c) => sum + (c.enrollment_count || 0), 0)
        };
    }
    
    confirmDelete(courseId, courseTitle) {
        this.courseToDelete = courseId;
        
        // تحديث نص التأكيد
        const modalBody = document.querySelector('#deleteModal .modal-body');
        modalBody.innerHTML = `
            <p>هل أنت متأكد من حذف الكورس "<strong>${courseTitle}</strong>"؟</p>
            <p class="text-danger"><strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه وسيتم حذف جميع الدروس والمحتوى المرتبط بالكورس.</p>
        `;
        
        this.deleteModal.show();
    }
    
    async deleteCourse() {
        if (!this.courseToDelete) return;
        
        try {
            const response = await fetchWithAuth(`/api/instructor/courses/${this.courseToDelete}`, {
                method: 'DELETE'
            });
            
            if (response.success) {
                showSuccess('تم حذف الكورس بنجاح');
                
                // إزالة الكورس من القائمة
                this.courses = this.courses.filter(c => c.id !== this.courseToDelete);
                
                // تحديث العرض
                this.displayCourses();
                this.updateStatistics();
                
                this.deleteModal.hide();
            } else {
                throw new Error(response.message || 'فشل في حذف الكورس');
            }
            
        } catch (error) {
            console.error('خطأ في حذف الكورس:', error);
            showError('حدث خطأ في حذف الكورس');
        } finally {
            this.courseToDelete = null;
        }
    }
    
    setupEventListeners() {
        // تأكيد الحذف
        document.getElementById('confirmDeleteBtn').addEventListener('click', () => {
            this.deleteCourse();
        });
        
        // إعادة تعيين عند إغلاق المودال
        document.getElementById('deleteModal').addEventListener('hidden.bs.modal', () => {
            this.courseToDelete = null;
        });
    }
    
    showLoading(show) {
        if (show) {
            this.loadingSpinner.style.display = 'block';
            this.coursesGrid.style.display = 'none';
            this.emptyState.style.display = 'none';
            this.statsRow.style.display = 'none';
        } else {
            this.loadingSpinner.style.display = 'none';
        }
    }
    
    showError(message) {
        this.errorContainer.innerHTML = `
            <div class="error-message">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
            </div>
        `;
        
        // إخفاء الرسالة بعد 5 ثوان
        setTimeout(() => {
            this.errorContainer.innerHTML = '';
        }, 5000);
    }
    
    // دالة لتحديث حالة الكورس
    async updateCourseStatus(courseId, newStatus) {
        try {
            const response = await fetchWithAuth(`/api/instructor/courses/${courseId}/status`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ status: newStatus })
            });
            
            if (response.success) {
                // تحديث الكورس في القائمة
                const courseIndex = this.courses.findIndex(c => c.id === courseId);
                if (courseIndex !== -1) {
                    this.courses[courseIndex].status = newStatus;
                    this.displayCourses();
                    this.updateStatistics();
                }
                
                showSuccess('تم تحديث حالة الكورس بنجاح');
            } else {
                throw new Error(response.message || 'فشل في تحديث حالة الكورس');
            }
            
        } catch (error) {
            console.error('خطأ في تحديث حالة الكورس:', error);
            showError('حدث خطأ في تحديث حالة الكورس');
        }
    }
    
    // دالة لتصفية الكورسات
    filterCourses(status = null, search = '') {
        let filteredCourses = this.courses;
        
        if (status) {
            filteredCourses = filteredCourses.filter(c => c.status === status);
        }
        
        if (search) {
            const searchLower = search.toLowerCase();
            filteredCourses = filteredCourses.filter(c => 
                c.title.toLowerCase().includes(searchLower) ||
                (c.description && c.description.toLowerCase().includes(searchLower)) ||
                (c.specialization_name && c.specialization_name.toLowerCase().includes(searchLower))
            );
        }
        
        // عرض النتائج المفلترة
        this.displayFilteredCourses(filteredCourses);
    }
    
    displayFilteredCourses(courses) {
        if (courses.length === 0) {
            this.coursesGrid.innerHTML = `
                <div class="col-12">
                    <div class="empty-state">
                        <i class="fas fa-search"></i>
                        <h3>لا توجد نتائج</h3>
                        <p>لم يتم العثور على كورسات تطابق البحث</p>
                    </div>
                </div>
            `;
            return;
        }
        
        this.coursesGrid.innerHTML = '';
        courses.forEach(course => {
            const courseCard = this.createCourseCard(course);
            this.coursesGrid.appendChild(courseCard);
        });
    }
}

// تهيئة مدير الكورسات عند تحميل الصفحة
let coursesManager;
document.addEventListener('DOMContentLoaded', function() {
    coursesManager = new InstructorCoursesManager();
});
