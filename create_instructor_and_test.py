#!/usr/bin/env python3
"""
إنشاء مدرس تجريبي واختبار إصلاح مشكلة الكورسات
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.firebase_utils import get_firebase_manager
from datetime import datetime, timezone
import uuid

def create_test_instructor_and_course():
    """إنشاء مدرس تجريبي وكورس واختبار الإصلاح"""
    
    print("🔍 إنشاء مدرس تجريبي واختبار إصلاح مشكلة الكورسات...")
    
    try:
        # الحصول على مدير Firebase
        firebase_manager = get_firebase_manager()
        
        if not firebase_manager:
            print("❌ فشل في الحصول على مدير Firebase")
            return False
        
        print("✅ تم الحصول على مدير Firebase")
        
        # إنشاء معرف فريد للمدرس
        instructor_id = str(uuid.uuid4())
        
        # بيانات المدرس التجريبي
        instructor_data = {
            'email': f'test.instructor.{instructor_id[:8]}@example.com',
            'password_hash': 'test123_hashed',  # في الواقع يجب تشفيرها
            'full_name': 'أحمد محمد المدرس التجريبي',
            'first_name': 'أحمد',
            'last_name': 'محمد المدرس التجريبي',
            'role': 'instructor',
            'specialization_id': 'medical_analysis',
            'status': 'active',
            'telegram_id': f'test_telegram_{instructor_id[:8]}',
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat(),
            'permissions': {
                'can_create_courses': True,
                'can_manage_students': True,
                'allowed_stages': [2, 3, 4],
                'can_create_general_courses': False
            }
        }
        
        print(f"📝 إنشاء مدرس بالمعرف: {instructor_id}")
        
        # إضافة المدرس مباشرة إلى قاعدة البيانات
        try:
            firebase_manager.database.child('users').child(instructor_id).set(instructor_data)
            print("✅ تم إنشاء المدرس التجريبي بنجاح")
        except Exception as e:
            print(f"❌ فشل في إنشاء المدرس: {e}")
            return False
        
        # إنشاء كورس تجريبي
        print(f"\n📚 إنشاء كورس تجريبي للمدرس...")
        
        course_data = {
            'title': 'كورس تجريبي - اختبار الإصلاح',
            'description': 'كورس تجريبي لاختبار إصلاح مشكلة عدم ظهور الكورسات',
            'instructor_id': instructor_id,  # استخدام instructor_id الصحيح
            'stage': 2,
            'status': 'draft',
            'is_general': False,
            'specialization_id': 'medical_analysis',
            'enrollment_count': 0,
            'lesson_count': 0,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        course_id = firebase_manager.create_course(course_data)
        
        if course_id:
            print(f"✅ تم إنشاء كورس تجريبي بنجاح (ID: {course_id})")
        else:
            print("❌ فشل في إنشاء الكورس التجريبي")
            return False
        
        # اختبار جلب الكورسات
        print(f"\n🔍 اختبار جلب كورسات المدرس...")
        courses = firebase_manager.get_courses_by_instructor(instructor_id)
        
        print(f"📊 عدد الكورسات الموجودة: {len(courses)}")
        
        if courses:
            print("\n📚 الكورسات الموجودة:")
            for i, course in enumerate(courses, 1):
                title = course.get('title', 'بدون عنوان')
                status = course.get('status', 'غير محدد')
                course_instructor_id = course.get('instructor_id', 'غير محدد')
                print(f"  {i}. {title}")
                print(f"     الحالة: {status}")
                print(f"     معرف المدرس: {course_instructor_id}")
                print(f"     معرف المدرس المطلوب: {instructor_id}")
                print(f"     تطابق المعرف: {'✅' if course_instructor_id == instructor_id else '❌'}")
        else:
            print("❌ لا توجد كورسات لهذا المدرس - المشكلة لم تُحل!")
            return False
        
        # إنشاء كورس آخر بحالة منشور
        print(f"\n📚 إنشاء كورس آخر بحالة منشور...")
        
        course_data_2 = {
            'title': 'كورس تجريبي منشور - اختبار الإصلاح',
            'description': 'كورس تجريبي منشور لاختبار إصلاح مشكلة عدم ظهور الكورسات',
            'instructor_id': instructor_id,
            'stage': 3,
            'status': 'published',
            'is_general': True,
            'specialization_id': None,
            'enrollment_count': 0,
            'lesson_count': 0,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        course_id_2 = firebase_manager.create_course(course_data_2)
        
        if course_id_2:
            print(f"✅ تم إنشاء الكورس الثاني بنجاح (ID: {course_id_2})")
        else:
            print("❌ فشل في إنشاء الكورس الثاني")
        
        # اختبار جلب الكورسات مرة أخرى
        print(f"\n🔍 اختبار جلب جميع كورسات المدرس...")
        all_courses = firebase_manager.get_courses_by_instructor(instructor_id)
        
        print(f"📊 إجمالي عدد الكورسات: {len(all_courses)}")
        
        if len(all_courses) >= 1:
            print("\n🎉 تم اختبار الإصلاح بنجاح! المشكلة تم حلها.")
            
            print("\n📚 جميع الكورسات:")
            for i, course in enumerate(all_courses, 1):
                title = course.get('title', 'بدون عنوان')
                status = course.get('status', 'غير محدد')
                is_general = course.get('is_general', False)
                stage = course.get('stage', 'غير محدد')
                print(f"  {i}. {title}")
                print(f"     الحالة: {status}")
                print(f"     المرحلة: {stage}")
                print(f"     كورس عام: {'نعم' if is_general else 'لا'}")
                print()
            
            return True
        else:
            print("❌ لم يتم العثور على الكورسات - المشكلة لم تُحل!")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = create_test_instructor_and_course()
    if success:
        print("\n✅ الاختبار نجح - الإصلاح يعمل بشكل صحيح")
        sys.exit(0)
    else:
        print("\n❌ الاختبار فشل - هناك مشكلة في الإصلاح")
        sys.exit(1)
