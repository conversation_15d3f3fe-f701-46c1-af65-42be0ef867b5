# 🎓 منصة الكورسات التعليمية المتقدمة مع بوت تليجرام - برومبت تطوير شامل

## 🎯 نظرة عامة على المشروع
تطوير منصة تعليمية متطورة مشابهة لـ Coursera مع بوت تليجرام لإدارة الحسابات ونظام أمان متقدم وإدارة محتوى شاملة، مخصصة للتخصصات الطبية (التحليل الطبي، الأشعة، التخدير) للمراحل الدراسية 2، 3، 4.

## 🏗️ البنية التقنية

### الخادم الخلفي (Backend):
- **الإطار**: Flask (Python)
- **قاعدة البيانات**: Firebase Realtime Database
- **المصادقة**: Firebase Auth + JWT
- **تخزين الملفات**: Firebase Storage
- **الأمان**: CORS، التحقق من المدخلات، تحديد معدل الطلبات

### بوت التليجرام (Telegram Bot):
- **المكتبة**: telebot (pyTelegramBotAPI)
- **المجلد**: bot/ (منفصل عن الموقع)
- **قاعدة البيانات**: نفس Firebase Realtime Database
- **الوظيفة**: إدارة إنشاء الحسابات والدعوات

### الواجهة الأمامية (Frontend):
- **الأساس**: HTML5, CSS3, JavaScript (ES6+)
- **إطار CSS**: Bootstrap 5 + CSS مخصص
- **الأيقونات**: Font Awesome 6
- **التصميم المتجاوب**: نهج Mobile-first

### نظام الفيديو المتقدم:
- **YouTube IFrame API**: تطبيق متقدم مع واجهة مخصصة
- **الأمان**: منع الوصول المباشر لـ YouTube
- **تحكم كامل**: إخفاء عناصر التحكم الافتراضية

## 🔐 نظام الأدوار والصلاحيات

### 🤖 مالك البوت (Bot Owner):
- **الوصول**: عبر Telegram ID المحدد مسبقاً
- إنشاء حسابات المدرسين عبر البوت
- إدارة التخصصات وصلاحياتها في الموقع
- تحديد أيقونات التخصصات
- إنشاء أكواد الوصول الشامل (تعمل لجميع الكورسات)
- مراقبة النظام والتحليلات الشاملة
- التحكم الكامل في جميع الحسابات

### 👨‍🏫 المدرس (Instructor):
- **إنشاء الحساب**: عبر رابط خاص من البوت (استخدام واحد فقط)
- إنشاء الكورسات ضمن التخصصات والصلاحيات المخصصة له
- إنتاج أكواد التفعيل (22 حرف) للكورسات التي أنشأها فقط
- إنشاء رابط دعوة موحد لجذب الطلاب لتخصصه
- إدارة طلابه (تغيير كلمة المرور، الحظر، تعديل المعلومات)
- عرض الطلاب التابعين له مع وظيفة البحث
- إدارة محتوى الكورس بمحرر غني
- تتبع تقدم طلابه

### 👨‍🎓 الطالب (Student):
- **إنشاء الحساب**: عبر رابط دعوة المدرس (حساب واحد فقط لكل Telegram ID)
- **البريد الإلكتروني**: معرف_التليجرام@rray.com
- **كلمة المرور**: عشوائية (8-18 حرف) ترسل عبر التليجرام
- تصفح جميع الكورسات (جميع التخصصات والمراحل)
- تفعيل أي كورس باستخدام الأكواد (من المدرس أو الأدمن)
- الوصول لمحتوى الكورسات المسجل بها (بدون انتهاء صلاحية)
- تتبع التقدم الشخصي في التعلم
- **الانتماء**: يظهر كطالب تابع للمدرس الذي دعاه

## 🤖 نظام بوت التليجرام المتقدم

### 📋 وظائف البوت الأساسية:
- **المجلد**: bot/ (منفصل عن الموقع الرئيسي)
- **قاعدة البيانات**: نفس Firebase Realtime Database
- **المكتبة**: telebot (pyTelegramBotAPI)
- **الأمان**: التحقق من Telegram ID للمالك

### 👑 وظائف مالك البوت:
1. **إنشاء حساب مدرس**:
   - اختيار الاسم عبر الأزرار التفاعلية
   - اختيار التخصص من القائمة المتاحة
   - تحديد الصلاحيات (جميع المراحل، مراحل محددة، الكورسات العامة)
   - إنتاج رابط إنشاء حساب (استخدام واحد فقط)

2. **إدارة التخصصات** (في الموقع):
   - إنشاء تخصص جديد (الاسم، الوصف، الأيقونة)
   - تحديد المراحل المتاحة لكل تخصص
   - تعيين صلاحيات الكورسات العامة

3. **انشاء حساب مالك ان لم يكن هنالك حساب**:
   - ادخال الايميل والباسورد الخاص بالحساب الذي سينشئه
   - ادخال الاسم 

### 🔗 نظام الروابط والدعوات:

#### رابط إنشاء حساب المدرس:
- **الإنتاج**: من البوت بعد اختيار الاسم والتخصص
- **الاستخدام**: مرة واحدة فقط
- **الوظيفة**: إنشاء حساب المدرس وإعطاء البيانات (إيميل + باسورد)
- **إضافي**: رابط المنصة من متغير البيئة (.env)

#### رابط دعوة الطلاب:
- **الإنتاج**: من المدرس لتخصصه
- **الاستخدام**: غير محدود لكن حساب واحد لكل Telegram ID
- **الوظيفة**: إنشاء حساب طالب تلقائياً
- **البيانات**: معرف_التليجرام@rray.com + كلمة مرور عشوائية (8-18 حرف)
- **عند عدم توفر معرف للتليكرام لدى المستخدم يتم استخدام معرف عشوائي غير مكرر يجب التاكد من هذا **

## 📊 هيكل قاعدة البيانات المحدث

### المستخدمون:
```json
{
  "users": {
    "user_id": {
      "email": "معرف_التليجرام@rray.com",
      "name": "الاسم الكامل",
      "role": "owner | instructor | student",
      "telegram_id": "*********",
      "telegram_username": "@username",
      "specializations": {
        "spec_id": {
          "name": "اسم التخصص",
          "permissions": {
            "all_stages": true,
            "specific_stages": [2, 3, 4],
            "general_courses": true
          }
        }
      },
      "assigned_instructor": "instructor_id (للطلاب فقط)",
      "account_status": "active/suspended",
      "created_by": "bot | instructor_invite",
      "created_at": "timestamp",
      "whatsapp": "رقم الواتساب",
      "telegram": "معرف التليجرام"
    }
  }
}
```

### التخصصات والكورسات:
```json
{
  "specializations": {
    "spec_id": {
      "name": "التحليل الطبي | الأشعة | التخدير",
      "description": "وصف التخصص",
      "icon": "رابط الأيقونة أو اسم الأيقونة",
      "stages": [2, 3, 4],
      "general_courses_enabled": true,
      "isActive": true,
      "created_by": "admin_id"
    }
  },
  "courses": {
    "course_id": {
      "title": "عنوان الكورس",
      "description": "وصف الكورس",
      "instructorId": "معرف المدرس",
      "specializationId": "معرف التخصص",
      "stage": "المرحلة الدراسية أو general",
      "price": "السعر",
      "isActive": true,
      "created_at": "timestamp",
      "content": {
        "section_id": {
          "type": "text | image | text_image | video | mcq | essay",
          "title": "عنوان القسم",
          "content": "المحتوى",
          "order": "الترتيب"
        }
      }
    }
  }
}
```

### أكواد التفعيل والروابط:
```json
{
  "activationCodes": {
    "code_id": {
      "code": "22 حرف عشوائي",
      "courseId": "معرف الكورس",
      "createdBy": "instructor_id | admin_id",
      "type": "course_specific | admin_universal",
      "isUsed": false,
      "usedBy": "معرف المستخدم",
      "usedAt": "وقت الاستخدام",
      "createdAt": "وقت الإنشاء",
      "expiresAt": "وقت انتهاء الكود (ليس الكورس)"
    }
  },
  "inviteLinks": {
    "link_id": {
      "type": "instructor_creation | student_invite",
      "created_by": "bot_owner_id | instructor_id",
      "target_role": "instructor | student",
      "specialization_id": "معرف التخصص (للطلاب)",
      "instructor_id": "معرف المدرس (للطلاب)",
      "is_used": false,
      "used_by": "telegram_id",
      "used_at": "timestamp",
      "expires_at": "timestamp",
      "max_uses": 1,
      "current_uses": 0
    }
  },
  "telegramAccounts": {
    "telegram_id": {
      "user_id": "معرف المستخدم في النظام",
      "username": "@telegram_username",
      "has_account": true,
      "account_type": "instructor | student",
      "created_via": "bot_invite | instructor_invite"
    }
  }
}
```

## 🎥 مشغل الفيديو المتقدم

### المتطلبات الأساسية:
- إخفاء جميع عناصر التحكم في YouTube
- واجهة تحكم مخصصة بالكامل
- منع التنقل المباشر لـ YouTube
- تصميم متجاوب لجميع الشاشات

### إعدادات YouTube API:
```javascript
playerVars: {
    'autoplay': 0,
    'controls': 0,           // إخفاء عناصر التحكم
    'modestbranding': 1,     // إخفاء شعار YouTube
    'rel': 0,                // عدم عرض فيديوهات مقترحة
    'showinfo': 0,           // إخفاء معلومات الفيديو
    'iv_load_policy': 3,     // إخفاء التعليقات التوضيحية
    'disablekb': 1,          // تعطيل لوحة المفاتيح
    'fs': 0,                 // تعطيل زر الشاشة الكاملة
    'playsinline': 1         // تشغيل مضمن في الموبايل
}
```

## 🎨 تصميم الواجهة والهوية البصرية المتقدمة

### 🎯 مبادئ التصميم الأساسية:
- **حديث**: واجهة نظيفة وبسيطة مع تصميم Material Design 3.0
- **متجاوب**: نهج Mobile-first مع Fluid Design
- **سهل الوصول**: متوافق مع معايير WCAG 2.1 AA
- **سريع**: تحسين التحميل والتفاعلات مع Micro-interactions
- **متسق**: نظام تصميم موحد عبر جميع الصفحات

### 🌈 نظام الألوان والهوية المرئية:
```css
:root {
    /* الألوان الأساسية */
    --primary: #2563eb;           /* أزرق أساسي */
    --primary-light: #3b82f6;     /* أزرق فاتح */
    --primary-dark: #1d4ed8;      /* أزرق داكن */
    --primary-50: #eff6ff;        /* أزرق شفاف جداً */
    --primary-100: #dbeafe;       /* أزرق شفاف */

    /* الألوان الثانوية */
    --secondary: #64748b;         /* رمادي */
    --secondary-light: #94a3b8;   /* رمادي فاتح */
    --secondary-dark: #475569;    /* رمادي داكن */

    /* ألوان الحالة */
    --success: #059669;           /* أخضر */
    --success-light: #10b981;     /* أخضر فاتح */
    --warning: #d97706;           /* برتقالي */
    --warning-light: #f59e0b;     /* برتقالي فاتح */
    --danger: #dc2626;            /* أحمر */
    --danger-light: #ef4444;      /* أحمر فاتح */

    /* ألوان الخلفية */
    --bg-primary: #ffffff;        /* خلفية أساسية */
    --bg-secondary: #f8fafc;      /* خلفية ثانوية */
    --bg-tertiary: #f1f5f9;       /* خلفية ثالثية */
    --bg-dark: #1e293b;           /* خلفية داكنة */

    /* ألوان النصوص */
    --text-primary: #0f172a;      /* نص أساسي */
    --text-secondary: #475569;    /* نص ثانوي */
    --text-muted: #94a3b8;        /* نص خافت */
    --text-inverse: #ffffff;      /* نص معكوس */

    /* التدرجات اللونية */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}
```

### 📝 نظام الطباعة (Typography):
```css
/* خطوط النظام */
:root {
    --font-primary: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-secondary: 'Poppins', 'Arial', sans-serif;
    --font-mono: 'Fira Code', 'Consolas', monospace;
}

/* أحجام النصوص */
.text-xs { font-size: 0.75rem; line-height: 1rem; }      /* 12px */
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }  /* 14px */
.text-base { font-size: 1rem; line-height: 1.5rem; }     /* 16px */
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }  /* 18px */
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }   /* 20px */
.text-2xl { font-size: 1.5rem; line-height: 2rem; }      /* 24px */
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; } /* 30px */
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }   /* 36px */
.text-5xl { font-size: 3rem; line-height: 1; }           /* 48px */

/* أوزان النصوص */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
```

## 🤖 تطبيق بوت التليجرام

### هيكل المجلد:
```
bot/
├── main.py                 # الملف الرئيسي للبوت
├── handlers/
│   ├── owner_handlers.py   # معالجات مالك البوت
│   ├── instructor_handlers.py # معالجات المدرسين
│   └── student_handlers.py # معالجات الطلاب
├── utils/
│   ├── firebase_utils.py   # وظائف Firebase
│   ├── link_generator.py   # إنتاج الروابط
│   └── validators.py       # التحقق من البيانات
├── keyboards/
│   ├── owner_keyboards.py  # أزرار مالك البوت
│   └── common_keyboards.py # الأزرار المشتركة
├── config.py              # إعدادات البوت
└── requirements.txt       # المتطلبات
```

### وظائف البوت الأساسية:
```python
# نظام أكواد التفعيل المحدث
import secrets
import string

def generate_activation_code():
    characters = string.ascii_uppercase + string.digits
    code = ''.join(secrets.choice(characters) for _ in range(22))
    return code

def generate_invite_link(link_type, creator_id, **kwargs):
    # إنتاج رابط دعوة فريد
    link_id = secrets.token_urlsafe(32)
    return f"{PLATFORM_URL}/invite/{link_id}"

def generate_random_password():
    # كلمة مرور عشوائية للطلاب (8-18 حرف)
    length = secrets.randbelow(11) + 8  # 8-18
    characters = string.ascii_letters + string.digits + "!@#$%"
    return ''.join(secrets.choice(characters) for _ in range(length))
```

### 🎨 نظام المكونات البصرية (Design System):

#### الأزرار (Buttons):
```css
/* أزرار أساسية */
.btn-primary {
    background: var(--gradient-primary);
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 14px 0 rgba(37, 99, 235, 0.25);
}

.btn-secondary {
    background: transparent;
    border: 2px solid var(--primary);
    color: var(--primary);
    border-radius: 12px;
    padding: 10px 22px;
}

.btn-ghost {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    padding: 8px 16px;
}

/* أحجام الأزرار */
.btn-xs { padding: 6px 12px; font-size: 0.75rem; }
.btn-sm { padding: 8px 16px; font-size: 0.875rem; }
.btn-md { padding: 12px 24px; font-size: 1rem; }
.btn-lg { padding: 16px 32px; font-size: 1.125rem; }
.btn-xl { padding: 20px 40px; font-size: 1.25rem; }
```

#### البطاقات (Cards):
```css
.card {
    background: var(--bg-primary);
    border-radius: 16px;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    padding: 24px;
    transition: all 0.3s ease;
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.card-hover:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.card-course {
    position: relative;
    overflow: hidden;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
}

.card-instructor {
    text-align: center;
    background: var(--gradient-primary);
    color: white;
}
```

#### الأيقونات والرموز:
```css
.icon {
    width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.icon-sm { width: 16px; height: 16px; }
.icon-md { width: 24px; height: 24px; }
.icon-lg { width: 32px; height: 32px; }
.icon-xl { width: 48px; height: 48px; }

/* أيقونات التخصصات */
.specialization-icon {
    width: 64px;
    height: 64px;
    border-radius: 16px;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}
```

### إدارة المحتوى:
```javascript
// قوالب المحتوى المختلفة
const contentTemplates = {
    text: { type: 'text', content: '', formatting: {} },
    image: { type: 'image', url: '', caption: '', alt: '' },
    text_image: { type: 'text_image', text: '', image: '', layout: 'side' },
    video: { type: 'video', youtubeId: '', title: '', description: '' },
    mcq: { type: 'mcq', question: '', options: [], correct: [], feedback: '' },
    essay: { type: 'essay', question: '', maxWords: 500, rubric: '' }
};
```

## � قواعد النظام المحدثة

### قواعد إنشاء الحسابات:
1. **المدرسين**: فقط عبر البوت من مالك البوت
2. **الطلاب**: فقط عبر روابط دعوة المدرسين
3. **حساب واحد لكل Telegram ID**: لا يمكن إنشاء حسابات متعددة
4. **البريد الإلكتروني**: معرف_التليجرام@rray.com (للطلاب)
5. **حساب الادمن**:يمكن لادمن انشائه عبر بوت التليكرام

### قواعد أكواد التفعيل:
1. **المدرس**: يمكنه إنشاء أكواد للكورسات التي أنشأها فقط
2. **الأدمن**: يمكنه إنشاء أكواد شاملة لجميع الكورسات
3. **الاستخدام**: مرة واحدة فقط لكورس واحد فقط
4. **انتهاء الصلاحية**: للكود فقط، ليس للكورس بعد التفعيل

### قواعد الصلاحيات:
1. **التخصصات**: يحددها الأدمن في الموقع مع الأيقونات
2. **صلاحيات المدرس**: تحدد عند إنشاء الحساب (مراحل محددة، عامة)
3. **وصول الطلاب**: لجميع الكورسات بغض النظر عن التخصص
4. **إدارة الطلاب**: المدرس يدير طلابه فقط، الأدمن يدير الجميع

## �🚀 مراحل التطوير المحدثة

### المرحلة 1: البنية الأساسية + البوت
- إعداد تطبيق Flask
- تطوير بوت التليجرام (مجلد bot/)
- تكامل Firebase Realtime Database
- نظام المصادقة المحدث
- إطار الواجهة الأساسي

### المرحلة 2: نظام الدعوات والروابط
- نظام إنشاء روابط الدعوة
- معالجة روابط إنشاء حسابات المدرسين
- معالجة روابط دعوة الطلاب
- نظام التحقق من Telegram ID

### المرحلة 3: إدارة التخصصات والصلاحيات
- واجهة إدارة التخصصات (الموقع)
- نظام الأيقونات للتخصصات
- إدارة صلاحيات المدرسين
- نظام تتبع انتماء الطلاب

### المرحلة 4: إدارة الكورسات المحدثة
- نظام إنشاء الكورسات مع قيود الصلاحيات
- إدارة المحتوى
- نظام أكواد التفعيل المحدث (مدرس + أدمن)
- إدارة التسجيلات

### المرحلة 5: مشغل الفيديو
- تكامل YouTube IFrame API
- واجهة مشغل الفيديو المخصصة
- تطبيق الأمان
- تحسين الموبايل

### المرحلة 6: الميزات المتقدمة
- البحث والتصفية
- لوحة التحليلات
- نظام إدارة الطلاب للمدرسين
- تحسين الأداء والاختبار

## 🤖 متطلبات بوت التليجرام

### متغيرات البيئة (.env):
```env
# بوت التليجرام
TELEGRAM_BOT_TOKEN=your_bot_token_here
BOT_OWNER_ID=*********  # Telegram ID للمالك
PLATFORM_URL=https://your-platform.com

# Firebase
FIREBASE_CONFIG=path_to_firebase_config.json
```

### الأزرار التفاعلية للبوت:
```python
# أزرار مالك البوت
owner_main_menu = [
    ["➕ إنشاء حساب مدرس", "📊 إحصائيات النظام"],
    ["👥 إدارة المدرسين", "🎓 إدارة التخصصات"],
    ["🔧 إعدادات البوت"]
]

# أزرار اختيار التخصص
specialization_buttons = [
    ["🔬 التحليل الطبي", "📡 الأشعة"],
    ["💉 التخدير", "🏥 عام"],
    ["🔙 رجوع"]
]

# أزرار صلاحيات المدرس
permissions_buttons = [
    ["✅ جميع المراحل", "📝 مراحل محددة"],
    ["🌐 الكورسات العامة", "🔙 رجوع"]
]
```

## ⚠️ تحذيرات مهمة

1. **حماية Telegram ID**: التأكد من صحة معرف مالك البوت
2. **أمان الروابط**: التحقق من انتهاء صلاحية الروابط
3. **منع التكرار**: التأكد من عدم إنشاء حسابات متعددة لنفس Telegram ID
4. **نسخ احتياطي**: عمل نسخة احتياطية قبل أي تعديل على قاعدة البيانات
5. **اختبار البوت**: اختبار جميع وظائف البوت في بيئة التطوير

## 🎯 معايير النجاح المحدثة

### المتطلبات التقنية:
✅ بوت تليجرام فعال مع جميع الوظائف
✅ نظام روابط الدعوة يعمل بشكل صحيح
✅ خادم Flask فعال مع تكامل Firebase
✅ واجهة أمامية متجاوبة مع UI/UX حديث
✅ مشغل فيديو YouTube مخصص مع إجراءات أمان
✅ نظام إدارة أدوار المستخدمين الكامل
✅ نظام أكواد التفعيل المحدث (مدرس + أدمن)
✅ إدارة محتوى الكورس مع محرر غني
✅ تصميم متجاوب محسن للموبايل

### المتطلبات التجارية:
✅ مالك البوت يمكنه إنشاء حسابات المدرسين عبر البوت
✅ المدرسون يمكنهم إنشاء روابط دعوة للطلاب
✅ الطلاب يحصلون على حسابات تلقائياً عبر روابط الدعوة
✅ نظام البريد الإلكتروني الموحد (@rray.com) يعمل
✅ المدرسون يمكنهم إنشاء كورسات ضمن صلاحياتهم فقط
✅ الطلاب يمكنهم الوصول لجميع الكورسات
✅ أكواد التفعيل تعمل مع القيود المحددة
✅ نظام إدارة الطلاب للمدرسين فعال
✅ تشغيل فيديو آمن بدون علامة YouTube التجارية
✅ وظائف البحث والتصفية
✅ قدرات التحليلات والتقارير

---

**ملاحظة مهمة**: هذا نظام متكامل يجمع بين الموقع وبوت التليجرام. يجب اختبار التكامل بين النظامين بدقة والتأكد من عمل جميع الروابط والوظائف بشكل صحيح قبل النشر.
