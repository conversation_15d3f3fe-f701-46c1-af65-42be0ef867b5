{% extends "base.html" %}

{% block title %}لوحة التحكم - {{ platform_name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- ترحيب -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="card-title mb-1">مرحباً {{ session.full_name }}!</h2>
                            <p class="card-text mb-0">تابع تقدمك في الكورسات وواصل رحلة التعلم</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <i class="fas fa-graduation-cap fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات -->
    <div class="row mb-4" id="statsCards">
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-book fa-2x"></i>
                    </div>
                    <h3 class="card-title text-primary mb-1" id="totalCourses">-</h3>
                    <p class="card-text text-muted small">إجمالي الكورسات</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                    <h3 class="card-title text-success mb-1" id="completedCourses">-</h3>
                    <p class="card-text text-muted small">كورسات مكتملة</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-play-circle fa-2x"></i>
                    </div>
                    <h3 class="card-title text-warning mb-1" id="activeCourses">-</h3>
                    <p class="card-text text-muted small">كورسات نشطة</p>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-info mb-2">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                    <h3 class="card-title text-info mb-1" id="averageProgress">-%</h3>
                    <p class="card-text text-muted small">متوسط التقدم</p>
                </div>
            </div>
        </div>
    </div>

    <!-- الكورسات الحديثة -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-clock text-primary me-2"></i>
                                آخر الكورسات المُحدثة
                            </h5>
                        </div>
                        <div class="col-auto">
                            <a href="{{ url_for('student_courses') }}" class="btn btn-outline-primary btn-sm">
                                عرض جميع الكورسات
                                <i class="fas fa-arrow-left ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div id="recentCoursesContainer">
                        <!-- سيتم تحميل الكورسات هنا -->
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal لتفعيل كورس جديد -->
<div class="modal fade" id="activateCourseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفعيل كورس جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="activateCourseForm">
                    <div class="mb-3">
                        <label for="activationCode" class="form-label">كود التفعيل</label>
                        <input type="text" class="form-control" id="activationCode" placeholder="أدخل كود التفعيل" required>
                        <div class="form-text">أدخل كود التفعيل الذي حصلت عليه من المدرس</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="activateCourseBtn">تفعيل الكورس</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    loadDashboardData();
    
    // تفعيل كورس جديد
    $('#activateCourseBtn').click(function() {
        const code = $('#activationCode').val().trim();
        if (!code) {
            showAlert('يرجى إدخال كود التفعيل', 'warning');
            return;
        }
        
        activateCourse(code);
    });
});

function loadDashboardData() {
    $.ajax({
        url: '/api/student/dashboard',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                updateStats(response.data.stats);
                displayRecentCourses(response.data.recent_courses);
            } else {
                showAlert('فشل في تحميل البيانات: ' + response.message, 'danger');
            }
        },
        error: function() {
            showAlert('حدث خطأ في تحميل البيانات', 'danger');
        }
    });
}

function updateStats(stats) {
    $('#totalCourses').text(stats.total_courses);
    $('#completedCourses').text(stats.completed_courses);
    $('#activeCourses').text(stats.active_courses);
    $('#averageProgress').text(stats.average_progress + '%');
}

function displayRecentCourses(courses) {
    const container = $('#recentCoursesContainer');
    
    if (!courses || courses.length === 0) {
        container.html(`
            <div class="text-center py-4">
                <i class="fas fa-book-open fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد كورسات مسجل بها</h5>
                <p class="text-muted">ابدأ بتفعيل كورس جديد باستخدام كود التفعيل</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#activateCourseModal">
                    <i class="fas fa-plus me-2"></i>تفعيل كورس جديد
                </button>
            </div>
        `);
        return;
    }
    
    let html = '<div class="row">';
    
    courses.forEach(function(course) {
        const progress = course.progress || {};
        const completionPercentage = progress.completion_percentage || 0;
        const progressBarClass = completionPercentage >= 100 ? 'bg-success' : 
                               completionPercentage >= 50 ? 'bg-warning' : 'bg-primary';
        
        html += `
            <div class="col-md-6 mb-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title mb-0">${course.course_title || course.course_name || 'كورس غير محدد'}</h6>
                            <span class="badge ${course.status === 'completed' ? 'bg-success' : 'bg-primary'} rounded-pill">
                                ${course.status === 'completed' ? 'مكتمل' : 'نشط'}
                            </span>
                        </div>
                        <p class="card-text text-muted small mb-2">
                            <i class="fas fa-user me-1"></i>
                            ${course.course_instructor || 'غير محدد'}
                        </p>
                        <div class="progress mb-2" style="height: 6px;">
                            <div class="progress-bar ${progressBarClass}" style="width: ${completionPercentage}%"></div>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">${completionPercentage}% مكتمل</small>
                            <a href="/student/course/${course.course_id}" class="btn btn-outline-primary btn-sm">
                                متابعة
                                <i class="fas fa-arrow-left ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    
    // إضافة زر تفعيل كورس جديد
    html += `
        <div class="text-center mt-3">
            <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#activateCourseModal">
                <i class="fas fa-plus me-2"></i>تفعيل كورس جديد
            </button>
        </div>
    `;
    
    container.html(html);
}

function activateCourse(code) {
    const btn = $('#activateCourseBtn');
    const originalText = btn.text();
    
    btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>جاري التفعيل...');
    
    $.ajax({
        url: '/api/activation-codes/use',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ code: code }),
        success: function(response) {
            if (response.success) {
                showAlert('تم تفعيل الكورس بنجاح!', 'success');
                $('#activateCourseModal').modal('hide');
                $('#activationCode').val('');
                loadDashboardData(); // إعادة تحميل البيانات
            } else {
                showAlert('فشل في تفعيل الكورس: ' + response.message, 'danger');
            }
        },
        error: function() {
            showAlert('حدث خطأ في تفعيل الكورس', 'danger');
        },
        complete: function() {
            btn.prop('disabled', false).text(originalText);
        }
    });
}

function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // إضافة التنبيه في أعلى الصفحة
    $('.container').first().prepend(alertHtml);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}
</script>
{% endblock %}
