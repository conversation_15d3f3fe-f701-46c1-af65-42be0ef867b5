/**
 * مدير لوحة التحليلات الشاملة
 * Comprehensive Analytics Dashboard Manager
 */

class AnalyticsDashboard {
    constructor(options = {}) {
        this.options = {
            apiEndpoint: '/api/analytics',
            refreshInterval: 300000, // 5 دقائق
            autoRefresh: false,
            ...options
        };

        this.charts = {};
        this.data = {};
        this.filters = {
            timeRange: '30',
            userType: 'all',
            specialization: 'all',
            stage: 'all'
        };

        this.isLoading = false;
        this.refreshTimer = null;

        // ألوان الرسوم البيانية
        this.colors = {
            primary: '#667eea',
            secondary: '#764ba2',
            success: '#38ef7d',
            warning: '#fecfef',
            info: '#fed6e3',
            gradient: {
                primary: ['#667eea', '#764ba2'],
                success: ['#11998e', '#38ef7d'],
                warning: ['#ff9a9e', '#fecfef'],
                info: ['#a8edea', '#fed6e3']
            }
        };

        this.initializeEventListeners();
    }

    /**
     * تهيئة مستمعي الأحداث
     */
    initializeEventListeners() {
        // تحديث الفلاتر
        $('#timeRangeFilter, #userTypeFilter, #specializationFilter, #stageFilter').on('change', () => {
            this.updateFilters();
        });

        // أزرار التحكم في الرسوم البيانية
        $(document).on('click', '[data-chart]', (e) => {
            const chartType = $(e.target).data('chart');
            this.switchChart(chartType);
        });

        $(document).on('click', '[data-metric]', (e) => {
            const metric = $(e.target).data('metric');
            this.switchMetric(metric);
        });

        // تحديث البيانات
        $('#refreshDataBtn').on('click', () => {
            this.refreshData();
        });

        // تصدير التقرير
        $('#exportReportBtn').on('click', () => {
            this.exportReport();
        });

        // تبديل التبويبات
        $('[data-bs-toggle="tab"]').on('shown.bs.tab', (e) => {
            const target = $(e.target).attr('data-bs-target');
            this.handleTabSwitch(target);
        });
    }

    /**
     * تهيئة لوحة التحليلات
     */
    async initialize() {
        try {
            this.showLoading();
            
            // تحميل التخصصات للفلتر
            await this.loadSpecializations();
            
            // تحميل البيانات الأولية
            await this.loadData();
            
            // إنشاء الرسوم البيانية
            this.createCharts();
            
            // تحديث الإحصائيات السريعة
            this.updateQuickStats();
            
            // تحديث الجداول
            this.updateTables();
            
            // بدء التحديث التلقائي
            if (this.options.autoRefresh) {
                this.startAutoRefresh();
            }
            
        } catch (error) {
            console.error('خطأ في تهيئة لوحة التحليلات:', error);
            this.showError('حدث خطأ في تحميل البيانات');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * تحميل التخصصات للفلتر
     */
    async loadSpecializations() {
        try {
            const response = await fetch('/api/specializations');
            const data = await response.json();
            
            if (data.success) {
                const select = $('#specializationFilter');
                select.find('option:not([value="all"])').remove();
                
                data.specializations.forEach(spec => {
                    select.append(`<option value="${spec.id}">${spec.name}</option>`);
                });
            }
        } catch (error) {
            console.error('خطأ في تحميل التخصصات:', error);
        }
    }

    /**
     * تحميل البيانات من الخادم
     */
    async loadData() {
        try {
            const params = new URLSearchParams(this.filters);
            const response = await fetch(`${this.options.apiEndpoint}?${params}`);
            const data = await response.json();
            
            if (data.success) {
                this.data = data.analytics;
                return this.data;
            } else {
                throw new Error(data.message || 'فشل في تحميل البيانات');
            }
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
            throw error;
        }
    }

    /**
     * تحديث الفلاتر
     */
    updateFilters() {
        this.filters = {
            timeRange: $('#timeRangeFilter').val(),
            userType: $('#userTypeFilter').val(),
            specialization: $('#specializationFilter').val(),
            stage: $('#stageFilter').val()
        };

        this.refreshData();
    }

    /**
     * تحديث البيانات
     */
    async refreshData() {
        if (this.isLoading) return;

        try {
            this.showLoading();
            await this.loadData();
            this.updateAllComponents();
        } catch (error) {
            console.error('خطأ في تحديث البيانات:', error);
            this.showError('حدث خطأ في تحديث البيانات');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * تحديث جميع المكونات
     */
    updateAllComponents() {
        this.updateQuickStats();
        this.updateCharts();
        this.updateTables();
        this.updateAdvancedAnalytics();
    }

    /**
     * تحديث الإحصائيات السريعة
     */
    updateQuickStats() {
        if (!this.data.quickStats) return;

        const stats = this.data.quickStats;
        
        $('#totalUsers').text(this.formatNumber(stats.totalUsers || 0));
        $('#totalCourses').text(this.formatNumber(stats.totalCourses || 0));
        $('#totalEnrollments').text(this.formatNumber(stats.totalEnrollments || 0));
        $('#avgProgress').text(`${stats.avgProgress || 0}%`);

        // تحديث نسب التغيير
        $('#usersChange').text(`${stats.usersChange > 0 ? '+' : ''}${stats.usersChange || 0}%`);
        $('#coursesChange').text(`${stats.coursesChange > 0 ? '+' : ''}${stats.coursesChange || 0}%`);
        $('#enrollmentsChange').text(`${stats.enrollmentsChange > 0 ? '+' : ''}${stats.enrollmentsChange || 0}%`);
        $('#progressChange').text(`${stats.progressChange > 0 ? '+' : ''}${stats.progressChange || 0}%`);
    }

    /**
     * إنشاء الرسوم البيانية
     */
    createCharts() {
        this.createGrowthChart();
        this.createUserDistributionChart();
        this.createSpecializationsChart();
        this.createStagesChart();
        this.createPerformanceChart();
        this.createAdvancedCharts();
    }

    /**
     * إنشاء رسم النمو
     */
    createGrowthChart() {
        const ctx = document.getElementById('growthChart');
        if (!ctx) return;

        const gradient = ctx.getContext('2d').createLinearGradient(0, 0, 0, 400);
        gradient.addColorStop(0, 'rgba(102, 126, 234, 0.8)');
        gradient.addColorStop(1, 'rgba(102, 126, 234, 0.1)');

        this.charts.growth = new Chart(ctx, {
            type: 'line',
            data: {
                labels: this.data.growth?.labels || [],
                datasets: [{
                    label: 'المستخدمين',
                    data: this.data.growth?.users || [],
                    borderColor: this.colors.primary,
                    backgroundColor: gradient,
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: this.colors.primary,
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                elements: {
                    point: {
                        hoverRadius: 8
                    }
                }
            }
        });
    }

    /**
     * إنشاء رسم توزيع المستخدمين
     */
    createUserDistributionChart() {
        const ctx = document.getElementById('userDistributionChart');
        if (!ctx) return;

        this.charts.userDistribution = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['المدرسين', 'الطلاب', 'الأدمن'],
                datasets: [{
                    data: this.data.userDistribution || [0, 0, 0],
                    backgroundColor: [
                        this.colors.primary,
                        this.colors.success,
                        this.colors.warning
                    ],
                    borderWidth: 0,
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }

    /**
     * إنشاء رسم التخصصات
     */
    createSpecializationsChart() {
        const ctx = document.getElementById('specializationsChart');
        if (!ctx) return;

        this.charts.specializations = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: this.data.specializations?.labels || [],
                datasets: [{
                    label: 'عدد المستخدمين',
                    data: this.data.specializations?.data || [],
                    backgroundColor: this.colors.gradient.success,
                    borderRadius: 10,
                    borderSkipped: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    /**
     * إنشاء رسم المراحل
     */
    createStagesChart() {
        const ctx = document.getElementById('stagesChart');
        if (!ctx) return;

        this.charts.stages = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: this.data.stages?.labels || [],
                datasets: [{
                    data: this.data.stages?.data || [],
                    backgroundColor: [
                        this.colors.primary,
                        this.colors.success,
                        this.colors.warning,
                        this.colors.info
                    ],
                    borderWidth: 0,
                    hoverOffset: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }

    /**
     * إنشاء رسم الأداء
     */
    createPerformanceChart() {
        const ctx = document.getElementById('performanceChart');
        if (!ctx) return;

        this.charts.performance = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: this.data.performance?.labels || [],
                datasets: [{
                    label: 'معدل الإكمال',
                    data: this.data.performance?.completion || [],
                    backgroundColor: this.colors.gradient.primary,
                    borderRadius: 8,
                    borderSkipped: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    /**
     * إنشاء الرسوم البيانية المتقدمة
     */
    createAdvancedCharts() {
        this.createActivityChart();
        this.createEngagementChart();
        this.createRetentionChart();
        this.createContentChart();
        this.createTrendsChart();
    }

    /**
     * إنشاء رسم النشاط
     */
    createActivityChart() {
        const ctx = document.getElementById('activityChart');
        if (!ctx) return;

        this.charts.activity = new Chart(ctx, {
            type: 'line',
            data: {
                labels: this.data.activity?.labels || [],
                datasets: [{
                    label: 'تسجيلات الدخول',
                    data: this.data.activity?.logins || [],
                    borderColor: this.colors.primary,
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }, {
                    label: 'التسجيلات الجديدة',
                    data: this.data.activity?.registrations || [],
                    borderColor: this.colors.success,
                    backgroundColor: 'rgba(56, 239, 125, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    /**
     * إنشاء رسم التفاعل
     */
    createEngagementChart() {
        const ctx = document.getElementById('engagementChart');
        if (!ctx) return;

        this.charts.engagement = new Chart(ctx, {
            type: 'radar',
            data: {
                labels: ['مشاهدة الدروس', 'إكمال الكورسات', 'التفاعل', 'الحضور', 'المشاركة'],
                datasets: [{
                    label: 'مستوى التفاعل',
                    data: this.data.engagement?.data || [0, 0, 0, 0, 0],
                    borderColor: this.colors.primary,
                    backgroundColor: 'rgba(102, 126, 234, 0.2)',
                    borderWidth: 2,
                    pointBackgroundColor: this.colors.primary,
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            stepSize: 20
                        }
                    }
                }
            }
        });
    }

    /**
     * إنشاء رسم الاحتفاظ
     */
    createRetentionChart() {
        const ctx = document.getElementById('retentionChart');
        if (!ctx) return;

        this.charts.retention = new Chart(ctx, {
            type: 'line',
            data: {
                labels: this.data.retention?.labels || [],
                datasets: [{
                    label: 'معدل الاحتفاظ',
                    data: this.data.retention?.data || [],
                    borderColor: this.colors.warning,
                    backgroundColor: 'rgba(255, 154, 158, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: this.colors.warning,
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * إنشاء رسم المحتوى
     */
    createContentChart() {
        const ctx = document.getElementById('contentChart');
        if (!ctx) return;

        this.charts.content = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: this.data.content?.labels || [],
                datasets: [{
                    label: 'عدد الدروس',
                    data: this.data.content?.lessons || [],
                    backgroundColor: this.colors.gradient.info[0],
                    borderRadius: 8,
                    borderSkipped: false
                }, {
                    label: 'المشاهدات',
                    data: this.data.content?.views || [],
                    backgroundColor: this.colors.gradient.info[1],
                    borderRadius: 8,
                    borderSkipped: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    /**
     * إنشاء رسم الاتجاهات
     */
    createTrendsChart() {
        const ctx = document.getElementById('trendsChart');
        if (!ctx) return;

        this.charts.trends = new Chart(ctx, {
            type: 'line',
            data: {
                labels: this.data.trends?.labels || [],
                datasets: [{
                    label: 'نمو المستخدمين',
                    data: this.data.trends?.users || [],
                    borderColor: this.colors.primary,
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                }, {
                    label: 'نمو الكورسات',
                    data: this.data.trends?.courses || [],
                    borderColor: this.colors.success,
                    backgroundColor: 'rgba(56, 239, 125, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                }, {
                    label: 'نمو التسجيلات',
                    data: this.data.trends?.enrollments || [],
                    borderColor: this.colors.warning,
                    backgroundColor: 'rgba(255, 154, 158, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    }

    /**
     * تحديث الرسوم البيانية
     */
    updateCharts() {
        Object.keys(this.charts).forEach(chartKey => {
            if (this.charts[chartKey] && this.data[chartKey]) {
                this.updateChartData(this.charts[chartKey], this.data[chartKey]);
            }
        });
    }

    /**
     * تحديث بيانات رسم بياني
     */
    updateChartData(chart, newData) {
        if (newData.labels) {
            chart.data.labels = newData.labels;
        }
        
        if (newData.datasets) {
            chart.data.datasets = newData.datasets;
        } else if (newData.data) {
            chart.data.datasets[0].data = newData.data;
        }
        
        chart.update('active');
    }

    /**
     * تحديث الجداول
     */
    updateTables() {
        this.updateTopCoursesTable();
        this.updateTopInstructorsTable();
    }

    /**
     * تحديث جدول أفضل الكورسات
     */
    updateTopCoursesTable() {
        const tbody = $('#topCoursesTable tbody');
        tbody.empty();

        if (this.data.topCourses) {
            this.data.topCourses.forEach(course => {
                tbody.append(`
                    <tr>
                        <td>${course.title}</td>
                        <td>${course.instructor}</td>
                        <td>${this.formatNumber(course.enrollments)}</td>
                        <td>${course.completion}%</td>
                    </tr>
                `);
            });
        }
    }

    /**
     * تحديث جدول أفضل المدرسين
     */
    updateTopInstructorsTable() {
        const tbody = $('#topInstructorsTable tbody');
        tbody.empty();

        if (this.data.topInstructors) {
            this.data.topInstructors.forEach(instructor => {
                tbody.append(`
                    <tr>
                        <td>${instructor.name}</td>
                        <td>${instructor.specialization}</td>
                        <td>${instructor.courses}</td>
                        <td>${instructor.students}</td>
                    </tr>
                `);
            });
        }
    }

    /**
     * تحديث التحليلات المتقدمة
     */
    updateAdvancedAnalytics() {
        this.updateActivityStats();
        this.updateContentInsights();
    }

    /**
     * تحديث إحصائيات النشاط
     */
    updateActivityStats() {
        const container = $('#activityStats');
        container.empty();

        if (this.data.activityStats) {
            Object.entries(this.data.activityStats).forEach(([key, value]) => {
                container.append(`
                    <div class="activity-stat">
                        <span class="activity-stat-label">${this.getStatLabel(key)}</span>
                        <span class="activity-stat-value">${this.formatNumber(value)}</span>
                    </div>
                `);
            });
        }
    }

    /**
     * تحديث رؤى المحتوى
     */
    updateContentInsights() {
        const container = $('#contentInsights');
        container.empty();

        if (this.data.contentInsights) {
            this.data.contentInsights.forEach(insight => {
                container.append(`
                    <div class="insight-item">
                        <div class="insight-title">${insight.title}</div>
                        <div class="insight-description">${insight.description}</div>
                        <div class="insight-value">${insight.value}</div>
                    </div>
                `);
            });
        }
    }

    /**
     * تبديل نوع الرسم البياني
     */
    switchChart(chartType) {
        $('.chart-controls [data-chart]').removeClass('active');
        $(`[data-chart="${chartType}"]`).addClass('active');

        if (this.charts.growth && this.data.growth) {
            const newData = this.data.growth[chartType] || [];
            this.charts.growth.data.datasets[0].data = newData;
            this.charts.growth.data.datasets[0].label = this.getChartLabel(chartType);
            this.charts.growth.update();
        }
    }

    /**
     * تبديل المقياس
     */
    switchMetric(metric) {
        $('.chart-controls [data-metric]').removeClass('active');
        $(`[data-metric="${metric}"]`).addClass('active');

        if (this.charts.performance && this.data.performance) {
            const newData = this.data.performance[metric] || [];
            this.charts.performance.data.datasets[0].data = newData;
            this.charts.performance.data.datasets[0].label = this.getMetricLabel(metric);
            this.charts.performance.update();
        }
    }

    /**
     * التعامل مع تبديل التبويبات
     */
    handleTabSwitch(target) {
        // إعادة رسم الرسوم البيانية عند تبديل التبويبات
        setTimeout(() => {
            Object.values(this.charts).forEach(chart => {
                if (chart && chart.resize) {
                    chart.resize();
                }
            });
        }, 100);
    }

    /**
     * بدء التحديث التلقائي
     */
    startAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }

        this.refreshTimer = setInterval(() => {
            this.refreshData();
        }, this.options.refreshInterval);
    }

    /**
     * إيقاف التحديث التلقائي
     */
    stopAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
    }

    /**
     * تصدير التقرير
     */
    async exportReport() {
        try {
            this.showLoading();
            
            const params = new URLSearchParams(this.filters);
            const response = await fetch(`/api/analytics/export?${params}`);
            
            if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `analytics-report-${new Date().toISOString().split('T')[0]}.pdf`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                this.showSuccess('تم تصدير التقرير بنجاح');
            } else {
                throw new Error('فشل في تصدير التقرير');
            }
        } catch (error) {
            console.error('خطأ في تصدير التقرير:', error);
            this.showError('حدث خطأ في تصدير التقرير');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * عرض حالة التحميل
     */
    showLoading() {
        this.isLoading = true;
        $('#loadingOverlay').show();
    }

    /**
     * إخفاء حالة التحميل
     */
    hideLoading() {
        this.isLoading = false;
        $('#loadingOverlay').hide();
    }

    /**
     * عرض رسالة خطأ
     */
    showError(message) {
        // يمكن استخدام مكتبة إشعارات مثل Toastr
        alert(message);
    }

    /**
     * عرض رسالة نجاح
     */
    showSuccess(message) {
        // يمكن استخدام مكتبة إشعارات مثل Toastr
        alert(message);
    }

    /**
     * تنسيق الأرقام
     */
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    /**
     * الحصول على تسمية الرسم البياني
     */
    getChartLabel(chartType) {
        const labels = {
            users: 'المستخدمين',
            courses: 'الكورسات',
            enrollments: 'التسجيلات'
        };
        return labels[chartType] || chartType;
    }

    /**
     * الحصول على تسمية المقياس
     */
    getMetricLabel(metric) {
        const labels = {
            completion: 'معدل الإكمال',
            engagement: 'مستوى التفاعل',
            progress: 'التقدم'
        };
        return labels[metric] || metric;
    }

    /**
     * الحصول على تسمية الإحصائية
     */
    getStatLabel(key) {
        const labels = {
            dailyLogins: 'تسجيلات الدخول اليومية',
            weeklyLogins: 'تسجيلات الدخول الأسبوعية',
            newRegistrations: 'التسجيلات الجديدة',
            activeUsers: 'المستخدمين النشطين'
        };
        return labels[key] || key;
    }

    /**
     * تدمير المثيل
     */
    destroy() {
        this.stopAutoRefresh();
        
        Object.values(this.charts).forEach(chart => {
            if (chart && chart.destroy) {
                chart.destroy();
            }
        });
        
        this.charts = {};
        this.data = {};
    }
}
