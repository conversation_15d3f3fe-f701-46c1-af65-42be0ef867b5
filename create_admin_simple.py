#!/usr/bin/env python3
"""
إنشاء حساب أدمن بسيط
"""

import os
import sys
import pyrebase
from datetime import datetime, timezone

def create_admin_directly():
    """إنشاء حساب أدمن مباشرة في Firebase"""
    
    # إعداد Firebase
    config = {
        "apiKey": "AIzaSyDGqKZKZKZKZKZKZKZKZKZKZKZKZKZKZKZ",  # مفتاح وهمي
        "authDomain": "cors-dd880.firebaseapp.com",
        "databaseURL": "https://cors-dd880-default-rtdb.firebaseio.com/",
        "projectId": "cors-dd880",
        "storageBucket": "cors-dd880.appspot.com",
        "messagingSenderId": "123456789"
    }
    
    try:
        firebase = pyrebase.initialize_app(config)
        db = firebase.database()
        
        # بيانات الأدمن
        admin_data = {
            'email': '<EMAIL>',
            'password': 'Zs6573zs',
            'full_name': 'علي الهدراوي',
            'first_name': 'علي',
            'last_name': 'الهدراوي',
            'role': 'admin',
            'active': True,
            'telegram_id': 'alhdrawi_admin',
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        # إنشاء المستخدم
        result = db.child('users').push(admin_data)
        user_id = result['name']
        
        print(f"✅ تم إنشاء حساب الأدمن بنجاح!")
        print(f"📧 البريد الإلكتروني: {admin_data['email']}")
        print(f"🔑 كلمة المرور: {admin_data['password']}")
        print(f"🆔 معرف المستخدم: {user_id}")
        
        return user_id
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء حساب الأدمن: {e}")
        return None

if __name__ == "__main__":
    create_admin_directly()
