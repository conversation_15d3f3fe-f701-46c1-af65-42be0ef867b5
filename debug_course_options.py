#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ملف تجريبي لاختبار وظيفة خيارات إنشاء الكورسات للمدرسين
"""

import os
import sys
from dotenv import load_dotenv

# إضافة المجلد الجذر للمشروع إلى مسار Python
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# تحميل متغيرات البيئة
load_dotenv()

from app import create_app, get_firebase_manager

def test_instructor_course_options():
    """اختبار وظيفة خيارات إنشاء الكورسات للمدرسين"""

    print("🚀 اختبار وظيفة خيارات إنشاء الكورسات للمدرسين")
    print("=" * 60)

    # تهيئة التطبيق
    app = create_app()

    with app.app_context():
        # تهيئة Firebase
        firebase_manager = get_firebase_manager()

        if not firebase_manager._initialized:
            print("❌ فشل في تهيئة Firebase")
            return

        print("✅ Firebase متصل")

        # قائمة المدرسين للاختبار
        instructor_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]

        print(f"\n📋 اختبار {len(instructor_emails)} مدرسين...")

        for email in instructor_emails:
            print(f"\n👤 اختبار المدرس: {email}")
            print("-" * 40)

            # البحث عن المدرس بالإيميل
            instructor = firebase_manager.get_user_by_email(email)

            if not instructor:
                print(f"❌ المدرس غير موجود: {email}")
                continue

            instructor_id = instructor.get('user_id') or instructor.get('id')
            print(f"🆔 معرف المدرس: {instructor_id}")
            print(f"👤 الاسم: {instructor.get('full_name', 'غير محدد')}")
            print(f"🏷️ الدور: {instructor.get('role', 'غير محدد')}")
            print(f"🎯 التخصص: {instructor.get('specialization_id', 'غير محدد')}")

            # عرض الصلاحيات
            permissions = instructor.get('permissions', {})
            print(f"🔐 الصلاحيات:")
            print(f"   - إنشاء كورسات: {permissions.get('can_create_courses', 'غير محدد')}")
            print(f"   - إدارة طلاب: {permissions.get('can_manage_students', 'غير محدد')}")
            print(f"   - المراحل المسموحة: {permissions.get('allowed_stages', 'غير محدد')}")
            print(f"   - كورسات عامة: {permissions.get('can_create_general_courses', 'غير محدد')}")

            # اختبار وظيفة خيارات إنشاء الكورسات
            print(f"\n🔧 اختبار وظيفة get_instructor_course_options...")
            try:
                options = firebase_manager.get_instructor_course_options(instructor_id)
                print(f"📊 النتيجة: {options}")

                if options:
                    if options.get('can_create'):
                        print("✅ يمكن للمدرس إنشاء كورسات")
                        print(f"   - المراحل المسموحة: {options.get('allowed_stages', [])}")
                        print(f"   - يمكن إنشاء كورسات عامة: {options.get('can_create_general', False)}")
                        print(f"   - تخصص المدرس: {options.get('instructor_specialization', 'غير محدد')}")
                        print(f"   - اسم التخصص: {options.get('instructor_specialization_name', 'غير محدد')}")
                    else:
                        print(f"❌ لا يمكن للمدرس إنشاء كورسات: {options.get('reason', 'سبب غير محدد')}")
                else:
                    print("❌ لم يتم إرجاع أي خيارات")

            except Exception as e:
                print(f"❌ خطأ في اختبار الوظيفة: {e}")
    
        print(f"\n✅ تم الانتهاء من الاختبار!")

if __name__ == "__main__":
    test_instructor_course_options()
