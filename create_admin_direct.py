#!/usr/bin/env python3
"""
إنشاء حساب أدمن مباشرة باستخدام Firebase Admin SDK
"""

import os
import json
import firebase_admin
from firebase_admin import credentials, db
from datetime import datetime, timezone

def create_admin_account():
    """إنشاء حساب أدمن مباشرة"""
    
    try:
        # إعداد Firebase Admin SDK
        if not firebase_admin._apps:
            # إنشاء ملف credentials من متغيرات البيئة
            cred_dict = *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            
            cred = credentials.Certificate(cred_dict)
            firebase_admin.initialize_app(cred, {
                'databaseURL': 'https://cors-dd880-default-rtdb.firebaseio.com/'
            })
        
        # الحصول على مرجع قاعدة البيانات
        ref = db.reference('users')
        
        # بيانات الأدمن
        admin_data = {
            'email': '<EMAIL>',
            'password': 'Zs6573zs',
            'full_name': 'علي الهدراوي',
            'first_name': 'علي',
            'last_name': 'الهدراوي',
            'role': 'admin',
            'active': True,
            'telegram_id': 'alhdrawi_admin',
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        # إنشاء المستخدم
        new_user_ref = ref.push(admin_data)
        user_id = new_user_ref.key
        
        print(f"✅ تم إنشاء حساب الأدمن بنجاح!")
        print(f"📧 البريد الإلكتروني: {admin_data['email']}")
        print(f"🔑 كلمة المرور: {admin_data['password']}")
        print(f"🆔 معرف المستخدم: {user_id}")
        
        # إنشاء مدرس للاختبار
        instructor_data = {
            'email': '<EMAIL>',
            'password': 'instructor123',
            'full_name': 'د. أحمد محمد',
            'first_name': 'أحمد',
            'last_name': 'محمد',
            'role': 'instructor',
            'active': True,
            'telegram_id': 'instructor_test',
            'specialization_id': None,
            'permissions': {
                'can_teach_all_levels': True,
                'allowed_levels': [],
                'can_teach_general_courses': True
            },
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        instructor_ref = ref.push(instructor_data)
        instructor_id = instructor_ref.key
        
        print(f"✅ تم إنشاء حساب المدرس بنجاح!")
        print(f"📧 البريد الإلكتروني: {instructor_data['email']}")
        print(f"🔑 كلمة المرور: {instructor_data['password']}")
        print(f"🆔 معرف المدرس: {instructor_id}")
        
        # إنشاء طالب للاختبار
        student_data = {
            'email': '<EMAIL>',
            'password': 'student123',
            'full_name': 'محمد علي',
            'first_name': 'محمد',
            'last_name': 'علي',
            'role': 'student',
            'active': True,
            'telegram_id': 'student_test',
            'specialization_id': None,
            'assigned_instructor': instructor_id,  # ربط الطالب بالمدرس
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat()
        }
        
        student_ref = ref.push(student_data)
        student_id = student_ref.key
        
        print(f"✅ تم إنشاء حساب الطالب بنجاح!")
        print(f"📧 البريد الإلكتروني: {student_data['email']}")
        print(f"🔑 كلمة المرور: {student_data['password']}")
        print(f"🆔 معرف الطالب: {student_id}")
        print(f"🔗 مرتبط بالمدرس: {instructor_id}")
        
        return user_id, instructor_id, student_id
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الحسابات: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

if __name__ == "__main__":
    print("🚀 بدء إنشاء حسابات الاختبار...")
    admin_id, instructor_id, student_id = create_admin_account()
    
    if admin_id:
        print("\n📋 ملخص الحسابات:")
        print("👨‍💼 الأدمن: <EMAIL> / Zs6573zs")
        print("👨‍🏫 المدرس: <EMAIL> / instructor123")
        print("👨‍🎓 الطالب: <EMAIL> / student123")
        print("\n🎉 يمكنك الآن تسجيل الدخول واختبار النظام!")
    else:
        print("\n❌ فشل في إنشاء الحسابات")
