{% extends "base.html" %}

{% block title %}إدارة التخصصات - {{ platform_name }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin-specializations.css') }}">
<style>
.specialization-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.specialization-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.specialization-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    text-align: center;
}

.specialization-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.specialization-body {
    padding: 1.5rem;
}

.stage-badge {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 20px;
    padding: 0.3rem 0.8rem;
    font-size: 0.8rem;
    margin: 0.2rem;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    margin-top: 1rem;
}

.btn-edit {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    color: white;
}

.btn-delete {
    background: linear-gradient(45deg, #dc3545, #fd7e14);
    border: none;
    color: white;
}

.btn-edit:hover, .btn-delete:hover {
    transform: translateY(-2px);
    color: white;
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    margin-bottom: 2rem;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: bold;
}

.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #667eea;
    background-color: #f8f9ff;
}

.upload-area.dragover {
    border-color: #667eea;
    background-color: #f0f4ff;
}

/* أنماط الأيقونات المرفوعة */
.uploaded-icons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 10px;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 10px;
}

.uploaded-icon-item {
    position: relative;
    border: 2px solid transparent;
    border-radius: 8px;
    padding: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.uploaded-icon-item:hover {
    border-color: #667eea;
    background-color: #f8f9fa;
}

.uploaded-icon-item.selected {
    border-color: #667eea;
    background-color: #e3f2fd;
}

.uploaded-icon-item img {
    width: 48px;
    height: 48px;
    object-fit: cover;
    border-radius: 4px;
}

.uploaded-icon-item .icon-name {
    font-size: 10px;
    color: #6c757d;
    margin-top: 4px;
    word-break: break-all;
}

.icon-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.icon-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.icon-card img {
    width: 64px;
    height: 64px;
    object-fit: cover;
    border-radius: 8px;
}

.icon-actions {
    display: flex;
    gap: 5px;
}

.icon-actions .btn {
    padding: 4px 8px;
    font-size: 12px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-0">
                        <i class="fas fa-stethoscope text-primary me-3"></i>
                        إدارة التخصصات
                    </h1>
                    <p class="text-muted mt-2">إدارة التخصصات المتاحة في المنصة وأيقوناتها</p>
                </div>
                <div>
                    <button class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#addSpecializationModal">
                        <i class="fas fa-plus me-2"></i>إضافة تخصص جديد
                    </button>
                    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#importSpecializationsModal">
                        <i class="fas fa-upload me-2"></i>استيراد تخصصات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات التخصصات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number" id="total-specializations">0</div>
                <div>إجمالي التخصصات</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card bg-success">
                <div class="stats-number" id="active-specializations">0</div>
                <div>التخصصات النشطة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card bg-info">
                <div class="stats-number" id="instructors-count">0</div>
                <div>المدرسين المسجلين</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card bg-warning">
                <div class="stats-number" id="courses-count">0</div>
                <div>الكورسات المتاحة</div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-end">
                        <div class="col-md-4">
                            <label for="searchSpecialization" class="form-label">البحث في التخصصات</label>
                            <input type="text" class="form-control" id="searchSpecialization" placeholder="ابحث بالاسم أو الوصف...">
                        </div>
                        <div class="col-md-3">
                            <label for="filterStage" class="form-label">فلترة حسب المرحلة</label>
                            <select class="form-select" id="filterStage">
                                <option value="">جميع المراحل</option>
                                <option value="2">المرحلة الثانية</option>
                                <option value="3">المرحلة الثالثة</option>
                                <option value="4">المرحلة الرابعة</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="filterStatus" class="form-label">فلترة حسب الحالة</label>
                            <select class="form-select" id="filterStatus">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-outline-secondary w-100" id="clearFilters">
                                <i class="fas fa-times me-2"></i>مسح الفلاتر
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة التخصصات -->
    <div class="row" id="specializations-grid">
        <div class="col-12 text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-3 text-muted">جاري تحميل التخصصات...</p>
        </div>
    </div>
</div>

<!-- Modal إضافة/تعديل تخصص -->
<div class="modal fade" id="addSpecializationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    <span id="modal-title">إضافة تخصص جديد</span>
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="specializationForm">
                    <input type="hidden" id="specialization_id" name="id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="spec_name" class="form-label">اسم التخصص (عربي) *</label>
                                <input type="text" class="form-control" id="spec_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="spec_name_en" class="form-label">اسم التخصص (إنجليزي) *</label>
                                <input type="text" class="form-control" id="spec_name_en" name="name_en" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="spec_description" class="form-label">وصف التخصص</label>
                        <textarea class="form-control" id="spec_description" name="description" rows="3" placeholder="وصف مختصر للتخصص..."></textarea>
                    </div>
                    
                    <!-- نوع الأيقونة -->
                    <div class="mb-3">
                        <label class="form-label">نوع الأيقونة *</label>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="icon_type" id="icon_type_font" value="font_awesome" checked>
                                    <label class="form-check-label" for="icon_type_font">
                                        <i class="fas fa-font me-2"></i>أيقونة Font Awesome
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="icon_type" id="icon_type_upload" value="uploaded">
                                    <label class="form-check-label" for="icon_type_upload">
                                        <i class="fas fa-image me-2"></i>صورة مرفوعة
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- اختيار أيقونة Font Awesome -->
                    <div id="font_awesome_section" class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="spec_icon" class="form-label">أيقونة Font Awesome *</label>
                                <select class="form-select" id="spec_icon" name="icon" required>
                                    <option value="">اختر أيقونة</option>
                                    <option value="fas fa-microscope">🔬 المجهر (التحليل الطبي)</option>
                                    <option value="fas fa-x-ray">📡 الأشعة السينية</option>
                                    <option value="fas fa-syringe">💉 الحقنة (التخدير)</option>
                                    <option value="fas fa-stethoscope">🩺 السماعة الطبية</option>
                                    <option value="fas fa-heartbeat">💓 نبضات القلب</option>
                                    <option value="fas fa-user-md">👨‍⚕️ الطبيب</option>
                                    <option value="fas fa-hospital">🏥 المستشفى</option>
                                    <option value="fas fa-pills">💊 الأدوية</option>
                                    <option value="fas fa-dna">🧬 الحمض النووي</option>
                                    <option value="fas fa-brain">🧠 الدماغ</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">معاينة الأيقونة</label>
                                <div class="border rounded p-3 text-center bg-light">
                                    <i id="icon-preview" class="fas fa-question fa-3x text-muted"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- اختيار صورة مرفوعة -->
                    <div id="uploaded_icon_section" class="row" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اختيار من الأيقونات المرفوعة</label>
                                <div id="uploaded-icons-grid" class="uploaded-icons-grid">
                                    <div class="text-center text-muted py-3">
                                        <i class="fas fa-spinner fa-spin"></i>
                                        جاري تحميل الأيقونات...
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#iconManagerModal">
                                    <i class="fas fa-cog me-2"></i>إدارة الأيقونات
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">معاينة الأيقونة المختارة</label>
                                <div class="border rounded p-3 text-center bg-light">
                                    <div id="uploaded-icon-preview" class="text-muted">
                                        <i class="fas fa-image fa-3x"></i>
                                        <p class="mt-2 mb-0">لم يتم اختيار أيقونة</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">المراحل المتاحة *</label>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="stage2" name="stages" value="2">
                                    <label class="form-check-label" for="stage2">المرحلة الثانية</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="stage3" name="stages" value="3">
                                    <label class="form-check-label" for="stage3">المرحلة الثالثة</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="stage4" name="stages" value="4">
                                    <label class="form-check-label" for="stage4">المرحلة الرابعة</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="spec_color" class="form-label">لون التخصص</label>
                                <input type="color" class="form-control form-control-color" id="spec_color" name="color" value="#667eea">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="spec_active" name="active" checked>
                                    <label class="form-check-label" for="spec_active">تخصص نشط</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="saveSpecializationBtn">
                    <i class="fas fa-save me-2"></i>حفظ التخصص
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal استيراد التخصصات -->
<div class="modal fade" id="importSpecializationsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-upload me-2"></i>استيراد التخصصات
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="upload-area" id="uploadArea">
                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                    <h5>اسحب وأفلت ملف JSON هنا</h5>
                    <p class="text-muted">أو انقر للاختيار من الجهاز</p>
                    <input type="file" id="fileInput" accept=".json" style="display: none;">
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        يجب أن يكون الملف بصيغة JSON ويحتوي على مصفوفة من التخصصات
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="importBtn" disabled>
                    <i class="fas fa-upload me-2"></i>استيراد
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal إدارة الأيقونات -->
<div class="modal fade" id="iconManagerModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">
                    <i class="fas fa-images me-2"></i>إدارة أيقونات التخصصات
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- قسم رفع أيقونة جديدة -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-plus me-2"></i>رفع أيقونة جديدة
                                </h6>
                            </div>
                            <div class="card-body">
                                <form id="uploadIconForm" enctype="multipart/form-data">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="icon_file" class="form-label">ملف الأيقونة *</label>
                                                <input type="file" class="form-control" id="icon_file" name="icon_file" accept="image/*" required>
                                                <div class="form-text">الأنواع المدعومة: PNG, JPG, JPEG, GIF, SVG, WEBP (حد أقصى: 2MB)</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="icon_description" class="form-label">وصف الأيقونة</label>
                                                <input type="text" class="form-control" id="icon_description" name="description" placeholder="وصف مختصر للأيقونة...">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-upload me-2"></i>رفع الأيقونة
                                            </button>
                                        </div>
                                        <div class="col-md-6">
                                            <div id="upload-preview" class="text-center" style="display: none;">
                                                <img id="preview-image" src="" alt="معاينة" class="img-thumbnail" style="max-height: 100px;">
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة الأيقونات الموجودة -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-list me-2"></i>الأيقونات المرفوعة
                                </h6>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="refreshIconsBtn">
                                    <i class="fas fa-sync-alt me-1"></i>تحديث
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="icons-list" class="row">
                                    <div class="col-12 text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                        <p class="mt-2 text-muted">جاري تحميل الأيقونات...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/admin-specializations.js') }}"></script>
{% endblock %}
