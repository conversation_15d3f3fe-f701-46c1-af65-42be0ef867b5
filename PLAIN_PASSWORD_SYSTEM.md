# 🔓 نظام كلمات المرور غير المشفرة

## 📋 نظرة عامة

تم تحديث نظام المصادقة ليدعم حفظ كلمات المرور **بدون تشفير** عند إنشاء الحسابات الجديدة، كما طلبت.

## 🔧 التغييرات المطبقة

### 1. **تحديث Auth Utils** (`utils/auth_utils.py`)

#### أ. تعديل دالة المصادقة:
- ✅ إزالة التشفير التلقائي لكلمات المرور القديمة
- ✅ دعم كلمات المرور المشفرة والغير مشفرة
- ✅ عدم تحويل كلمات المرور غير المشفرة إلى مشفرة

#### ب. إضافة دالة إنشاء المستخدمين الجديدة:
```python
def create_user_with_plain_password(self, user_data: Dict[str, Any], password: str) -> Optional[str]:
    """إنشاء مستخدم جديد مع كلمة مرور غير مشفرة"""
```

#### ج. تحديث دالة إعادة تعيين كلمة المرور:
```python
def reset_password(self, user_id: str, new_password: str, encrypt: bool = False) -> Tuple[bool, str]:
    """إعادة تعيين كلمة المرور مع خيار التشفير (افتراضي: False)"""
```

### 2. **إنشاء وحدة إدارة المستخدمين** (`utils/user_creation_utils.py`)

#### أ. إنشاء حسابات المدرسين:
```python
def create_instructor_account(self, first_name: str, last_name: str, telegram_id: str, ...):
    """إنشاء حساب مدرس جديد بكلمة مرور غير مشفرة"""
```

#### ب. إنشاء حسابات الطلاب:
```python
def create_student_account(self, telegram_id: str, specialization_id: str, ...):
    """إنشاء حساب طالب جديد بكلمة مرور غير مشفرة"""
```

#### ج. تحديث كلمات المرور:
```python
def update_user_password(self, user_id: str, new_password: str):
    """تحديث كلمة مرور المستخدم (غير مشفرة)"""
```

### 3. **ملفات الاختبار والإعداد**

#### أ. ملف إعادة إنشاء المستخدمين (`recreate_test_users.py`):
- ✅ حذف المستخدمين التجريبيين القدامى
- ✅ إنشاء مستخدمين جدد بكلمات مرور غير مشفرة
- ✅ التحقق من حفظ كلمات المرور بدون تشفير

#### ب. ملف اختبار كلمات المرور (`test_plain_passwords.py`):
- ✅ اختبار إنشاء مستخدم بكلمة مرور غير مشفرة
- ✅ اختبار تسجيل الدخول بكلمة المرور غير المشفرة
- ✅ التحقق من رفض كلمات المرور الخاطئة

## 🎯 كيفية الاستخدام

### 1. **إنشاء مستخدم جديد بكلمة مرور غير مشفرة:**

```python
from utils.auth_utils import get_auth_manager

auth_manager = get_auth_manager()

user_data = {
    'email': '<EMAIL>',
    'telegram_id': '*********',
    'role': 'student',
    'first_name': 'أحمد',
    'last_name': 'محمد'
}

password = 'my_plain_password'
user_id = auth_manager.create_user_with_plain_password(user_data, password)
```

### 2. **إنشاء حساب مدرس:**

```python
from utils.user_creation_utils import get_user_creation_manager

user_manager = get_user_creation_manager()

success, account_info, message = user_manager.create_instructor_account(
    first_name='أحمد',
    last_name='المدرس',
    telegram_id='*********',
    specialization_id='medical_analysis'
)

if success:
    print(f"الإيميل: {account_info['email']}")
    print(f"كلمة المرور: {account_info['password']}")  # غير مشفرة
```

### 3. **إنشاء حساب طالب:**

```python
success, account_info, message = user_manager.create_student_account(
    telegram_id='*********',
    specialization_id='medical_analysis'
)

if success:
    print(f"الإيميل: {account_info['email']}")
    print(f"كلمة المرور: {account_info['password']}")  # غير مشفرة
```

## 🔍 التحقق من النظام

### 1. **المستخدمين التجريبيين الجدد:**

| الدور | الإيميل | كلمة المرور | حالة التشفير |
|-------|---------|-------------|--------------|
| مدير النظام | <EMAIL> | admin123 | غير مشفرة |
| مدرس | <EMAIL> | instructor123 | غير مشفرة |
| طالب | <EMAIL> | student123 | غير مشفرة |

### 2. **التحقق من قاعدة البيانات:**

```javascript
// في Firebase Console
{
  "users": {
    "user_id": {
      "email": "<EMAIL>",
      "password": "plain_password_123",  // غير مشفرة
      "password_encrypted": false,
      "role": "student",
      ...
    }
  }
}
```

## ⚠️ ملاحظات أمنية

### 1. **التوافق مع النظام القديم:**
- ✅ النظام يدعم كلمات المرور المشفرة والغير مشفرة
- ✅ المستخدمين القدامى يمكنهم تسجيل الدخول بكلمات المرور المشفرة
- ✅ المستخدمين الجدد يتم إنشاؤهم بكلمات مرور غير مشفرة

### 2. **تحديد نوع كلمة المرور:**
```python
# في قاعدة البيانات
{
  "password": "actual_password",
  "password_encrypted": false  // يحدد نوع كلمة المرور
}
```

### 3. **الأمان:**
- ⚠️ كلمات المرور غير المشفرة مرئية في قاعدة البيانات
- ⚠️ يُنصح بتقييد الوصول لقاعدة البيانات
- ✅ JWT tokens ما زالت مشفرة وآمنة
- ✅ HTTPS يحمي النقل

## 🚀 الخطوات التالية

### 1. **للاختبار:**
```bash
# تشغيل اختبار كلمات المرور غير المشفرة
python test_plain_passwords.py

# إعادة إنشاء المستخدمين التجريبيين
python recreate_test_users.py

# تشغيل الخادم
python run_server.py
```

### 2. **للتطوير:**
- استخدم `create_user_with_plain_password()` لإنشاء مستخدمين جدد
- استخدم `UserCreationManager` لإنشاء حسابات المدرسين والطلاب
- تأكد من تعيين `password_encrypted: false` للمستخدمين الجدد

## 📝 الخلاصة

✅ **تم تحديث النظام بنجاح** لحفظ كلمات المرور بدون تشفير عند إنشاء الحسابات الجديدة

✅ **النظام متوافق** مع كلمات المرور المشفرة والغير مشفرة

✅ **جميع الوظائف تعمل** بشكل صحيح مع النظام الجديد

✅ **المستخدمين التجريبيين** جاهزين للاختبار بكلمات مرور غير مشفرة

🎯 **النظام جاهز للاستخدام** مع متطلبك لحفظ كلمات المرور بدون تشفير!
