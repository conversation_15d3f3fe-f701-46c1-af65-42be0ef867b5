"""
فحص بيانات المستخدم في قاعدة البيانات
"""

import sys
import os

# إضافة مجلد البوت للمسار
sys.path.append(os.path.join(os.path.dirname(__file__), 'bot'))

from bot.config import config
from utils.firebase_utils import get_firebase_manager

def check_user():
    """فحص بيانات المستخدم"""
    
    # تهيئة Firebase
    firebase_manager = get_firebase_manager()
    
    firebase_config = {
        'FIREBASE_CONFIG': config.FIREBASE_CONFIG,
        'FIREBASE_DATABASE_URL': config.FIREBASE_DATABASE_URL
    }
    
    if firebase_manager.initialize(firebase_config):
        print("✅ تم تهيئة Firebase بنجاح")
        
        # اختبار البحث بالإيميل المُحدث
        email = '<EMAIL>'
        print(f"\n🔍 اختبار البحث بالإيميل: {email}")

        user = firebase_manager.get_user_by_email(email)
        if user:
            print(f"✅ تم العثور على المستخدم بالإيميل:")
            print(f"📧 الإيميل: {user.get('email')}")
            print(f"🔑 كلمة المرور: {user.get('password')}")
            print(f"📱 معرف التليجرام: {user.get('telegram_id')}")
            print(f"👤 الاسم: {user.get('first_name')} {user.get('last_name')}")
            print(f"🎭 الدور: {user.get('role')}")
            print(f"📊 الحالة: {user.get('status')}")

            # اختبار تسجيل الدخول
            print(f"\n🔐 اختبار تسجيل الدخول:")
            from utils.auth_utils import get_auth_manager
            auth_manager = get_auth_manager()

            success, user_data, message = auth_manager.authenticate_user(email, user.get('password'))
            if success:
                print(f"✅ تم تسجيل الدخول بنجاح!")
                print(f"📝 رسالة: {message}")
            else:
                print(f"❌ فشل في تسجيل الدخول: {message}")
        else:
            print(f"❌ لم يتم العثور على المستخدم بالإيميل: {email}")
    else:
        print("❌ فشل في تهيئة Firebase")

if __name__ == "__main__":
    check_user()
