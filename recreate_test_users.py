"""
إعادة إنشاء مستخدمين تجريبيين بكلمات مرور غير مشفرة
Recreate Test Users with Plain Text Passwords
"""

import sys
import os
from flask import Flask

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def delete_existing_test_users():
    """حذف المستخدمين التجريبيين الموجودين"""
    print("🗑️ حذف المستخدمين التجريبيين الموجودين...")
    
    try:
        from app import create_app
        from utils.firebase_utils import get_firebase_manager
        
        app = create_app('development')
        
        with app.app_context():
            firebase_manager = get_firebase_manager()
            
            test_emails = [
                '<EMAIL>',
                '<EMAIL>',
                '<EMAIL>'
            ]
            
            deleted_count = 0
            
            for email in test_emails:
                try:
                    user = firebase_manager.get_user_by_email(email)
                    if user:
                        user_id = user.get('user_id')
                        if user_id:
                            # حذف المستخدم من Firebase
                            firebase_manager.database.child('users').child(user_id).delete()
                            print(f"✅ تم حذف المستخدم: {email}")
                            deleted_count += 1
                        else:
                            print(f"⚠️ لم يتم العثور على معرف للمستخدم: {email}")
                    else:
                        print(f"ℹ️ المستخدم غير موجود: {email}")
                except Exception as e:
                    print(f"❌ خطأ في حذف المستخدم {email}: {e}")
            
            print(f"\n📊 تم حذف {deleted_count} مستخدم(ين)")
            return deleted_count > 0
            
    except Exception as e:
        print(f"❌ خطأ في حذف المستخدمين: {e}")
        return False

def create_new_test_users():
    """إنشاء مستخدمين تجريبيين جدد بكلمات مرور غير مشفرة"""
    print("\n👤 إنشاء مستخدمين تجريبيين جدد...")
    
    try:
        from app import create_app
        from utils.firebase_utils import get_firebase_manager
        from utils.auth_utils import get_auth_manager
        from models.database_models import DatabaseModels, UserRole
        
        app = create_app('development')
        
        with app.app_context():
            firebase_manager = get_firebase_manager()
            auth_manager = get_auth_manager()
            
            # قائمة المستخدمين التجريبيين الجدد
            test_users = [
                {
                    'email': '<EMAIL>',
                    'password': 'admin123',
                    'role': UserRole.ADMIN,
                    'first_name': 'مدير',
                    'last_name': 'النظام',
                    'telegram_id': '999999999'
                },
                {
                    'email': '<EMAIL>',
                    'password': 'instructor123',
                    'role': UserRole.INSTRUCTOR,
                    'first_name': 'أحمد',
                    'last_name': 'المدرس',
                    'telegram_id': '888888888',
                    'specialization_id': 'medical_analysis'
                },
                {
                    'email': '<EMAIL>',
                    'password': 'student123',
                    'role': UserRole.STUDENT,
                    'first_name': 'محمد',
                    'last_name': 'الطالب',
                    'telegram_id': '777777777',
                    'specialization_id': 'medical_analysis'
                }
            ]
            
            created_users = []
            
            for user_info in test_users:
                try:
                    # إنشاء نموذج المستخدم
                    user_data = DatabaseModels.create_user_model(
                        email=user_info['email'],
                        telegram_id=user_info['telegram_id'],
                        role=user_info['role'],
                        first_name=user_info['first_name'],
                        last_name=user_info['last_name'],
                        specialization_id=user_info.get('specialization_id')
                    )
                    
                    # إضافة معلومات إضافية
                    user_data['status'] = 'active'
                    user_data['account_type'] = user_info['role'].lower()
                    user_data['created_by'] = 'test_system'
                    
                    # إنشاء المستخدم مع كلمة مرور غير مشفرة
                    user_id = auth_manager.create_user_with_plain_password(user_data, user_info['password'])
                    
                    if user_id:
                        created_users.append({
                            'id': user_id,
                            'email': user_info['email'],
                            'password': user_info['password'],
                            'role': user_info['role'],
                            'name': f"{user_info['first_name']} {user_info['last_name']}"
                        })
                        print(f"✅ تم إنشاء المستخدم: {user_info['email']}")
                    else:
                        print(f"❌ فشل في إنشاء المستخدم: {user_info['email']}")
                        
                except Exception as e:
                    print(f"❌ خطأ في إنشاء المستخدم {user_info['email']}: {e}")
            
            return created_users
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدمين الجدد: {e}")
        return []

def verify_plain_passwords():
    """التحقق من أن كلمات المرور محفوظة بدون تشفير"""
    print("\n🔍 التحقق من كلمات المرور...")
    
    try:
        from app import create_app
        from utils.firebase_utils import get_firebase_manager
        
        app = create_app('development')
        
        with app.app_context():
            firebase_manager = get_firebase_manager()
            
            test_credentials = [
                ('<EMAIL>', 'admin123'),
                ('<EMAIL>', 'instructor123'),
                ('<EMAIL>', 'student123')
            ]
            
            all_verified = True
            
            for email, expected_password in test_credentials:
                try:
                    user = firebase_manager.get_user_by_email(email)
                    if user:
                        stored_password = user.get('password')
                        if stored_password == expected_password:
                            print(f"✅ {email}: كلمة المرور محفوظة بدون تشفير")
                        else:
                            print(f"❌ {email}: كلمة المرور مشفرة أو مختلفة")
                            print(f"   المتوقع: {expected_password}")
                            print(f"   المحفوظ: {stored_password}")
                            all_verified = False
                    else:
                        print(f"❌ {email}: المستخدم غير موجود")
                        all_verified = False
                except Exception as e:
                    print(f"❌ خطأ في التحقق من {email}: {e}")
                    all_verified = False
            
            return all_verified
            
    except Exception as e:
        print(f"❌ خطأ في التحقق من كلمات المرور: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔄 إعادة إنشاء المستخدمين التجريبيين بكلمات مرور غير مشفرة")
    print("="*70)
    
    # حذف المستخدمين الموجودين
    delete_success = delete_existing_test_users()
    
    # إنشاء مستخدمين جدد
    created_users = create_new_test_users()
    
    if created_users:
        print("\n" + "="*70)
        print("📋 تفاصيل المستخدمين التجريبيين الجدد:")
        print("="*70)
        
        for user in created_users:
            print(f"\n👤 {user['name']} ({user['role']})")
            print(f"   📧 البريد الإلكتروني: {user['email']}")
            print(f"   🔑 كلمة المرور: {user['password']} (غير مشفرة)")
            print(f"   🆔 معرف المستخدم: {user['id']}")
        
        # التحقق من كلمات المرور
        if verify_plain_passwords():
            print("\n✅ تم التحقق: جميع كلمات المرور محفوظة بدون تشفير")
        else:
            print("\n⚠️ تحذير: بعض كلمات المرور قد تكون مشفرة")
        
        print("\n" + "="*70)
        print("🎯 يمكنك الآن استخدام هذه البيانات لاختبار نظام المصادقة")
        print("🌐 اذهب إلى /login وجرب تسجيل الدخول")
        print("📝 ملاحظة: كلمات المرور محفوظة بدون تشفير كما طلبت")
        
        return True
    else:
        print("\n⚠️ فشل في إنشاء المستخدمين التجريبيين")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
