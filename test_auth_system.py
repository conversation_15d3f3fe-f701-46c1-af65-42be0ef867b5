"""
اختبار نظام المصادقة والأمان
Authentication System Testing

هذا الملف يحتوي على اختبارات شاملة لنظام المصادقة:
- اختبار JWT tokens
- اختبار المصادقة مع Firebase
- اختبار الحماية والصلاحيات
- اختبار APIs المصادقة
"""

import unittest
import json
import tempfile
import os
from datetime import datetime, timezone
from flask import Flask
from utils.jwt_utils import JWTManager, get_jwt_manager
from utils.auth_utils import AuthManager, get_auth_manager
from utils.auth_middleware import AuthMiddleware, get_auth_middleware
from config import Config

class TestJWTManager(unittest.TestCase):
    """اختبار JWT Manager"""
    
    def setUp(self):
        """إعداد الاختبار"""
        self.app = Flask(__name__)
        self.app.config.from_object(Config)
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        self.jwt_manager = JWTManager()
        
        self.test_user_data = {
            'user_id': 'test_user_123',
            'email': '<EMAIL>',
            'role': 'student',
            'telegram_id': '123456789',
            'first_name': 'أحمد',
            'last_name': 'محمد',
            'specialization_id': 'spec_1'
        }
    
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        self.app_context.pop()
    
    def test_generate_token(self):
        """اختبار إنشاء JWT token"""
        token = self.jwt_manager.generate_token(self.test_user_data)
        
        self.assertIsNotNone(token)
        self.assertIsInstance(token, str)
        self.assertTrue(len(token) > 50)  # JWT tokens are typically long
    
    def test_verify_token(self):
        """اختبار التحقق من JWT token"""
        # إنشاء توكين
        token = self.jwt_manager.generate_token(self.test_user_data)
        
        # التحقق من التوكين
        payload = self.jwt_manager.verify_token(token)
        
        self.assertIsNotNone(payload)
        self.assertEqual(payload['email'], self.test_user_data['email'])
        self.assertEqual(payload['role'], self.test_user_data['role'])
        self.assertEqual(payload['user_id'], self.test_user_data['user_id'])
    
    def test_invalid_token(self):
        """اختبار توكين غير صحيح"""
        invalid_token = "invalid.token.here"
        payload = self.jwt_manager.verify_token(invalid_token)
        
        self.assertIsNone(payload)
    
    def test_get_user_from_token(self):
        """اختبار استخراج بيانات المستخدم من التوكين"""
        token = self.jwt_manager.generate_token(self.test_user_data)
        user_data = self.jwt_manager.get_user_from_token(token)
        
        self.assertIsNotNone(user_data)
        self.assertEqual(user_data['email'], self.test_user_data['email'])
        self.assertEqual(user_data['role'], self.test_user_data['role'])
        self.assertIn('full_name', user_data)

class TestAuthManager(unittest.TestCase):
    """اختبار Auth Manager"""
    
    def setUp(self):
        """إعداد الاختبار"""
        self.app = Flask(__name__)
        self.app.config.from_object(Config)
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        self.auth_manager = AuthManager()
    
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        self.app_context.pop()
    
    def test_hash_password(self):
        """اختبار تشفير كلمة المرور"""
        password = "test_password_123"
        hashed = self.auth_manager.hash_password(password)
        
        self.assertIsNotNone(hashed)
        self.assertNotEqual(password, hashed)
        self.assertTrue(hashed.startswith('pbkdf2:sha256:'))
    
    def test_verify_password(self):
        """اختبار التحقق من كلمة المرور"""
        password = "test_password_123"
        hashed = self.auth_manager.hash_password(password)
        
        # كلمة مرور صحيحة
        self.assertTrue(self.auth_manager.verify_password(password, hashed))
        
        # كلمة مرور خاطئة
        self.assertFalse(self.auth_manager.verify_password("wrong_password", hashed))
    
    def test_generate_random_password(self):
        """اختبار توليد كلمة مرور عشوائية"""
        password = self.auth_manager.generate_random_password(12)
        
        self.assertIsNotNone(password)
        self.assertEqual(len(password), 12)
        
        # توليد كلمة مرور أخرى للتأكد من العشوائية
        password2 = self.auth_manager.generate_random_password(12)
        self.assertNotEqual(password, password2)

class TestAuthMiddleware(unittest.TestCase):
    """اختبار Auth Middleware"""
    
    def setUp(self):
        """إعداد الاختبار"""
        self.app = Flask(__name__)
        self.app.config.from_object(Config)
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        self.auth_middleware = AuthMiddleware()
        self.jwt_manager = get_jwt_manager()
        
        # بيانات مستخدم للاختبار
        self.test_user_data = {
            'user_id': 'test_user_123',
            'email': '<EMAIL>',
            'role': 'student',
            'telegram_id': '123456789',
            'first_name': 'أحمد',
            'last_name': 'محمد'
        }
        
        self.test_token = self.jwt_manager.generate_token(self.test_user_data)
    
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        self.app_context.pop()
    
    def test_token_extraction(self):
        """اختبار استخراج التوكين من الطلب"""
        with self.app.test_request_context(
            headers={'Authorization': f'Bearer {self.test_token}'}
        ):
            token = self.auth_middleware._get_token_from_request()
            self.assertEqual(token, self.test_token)

class TestAuthAPIs(unittest.TestCase):
    """اختبار APIs المصادقة"""
    
    def setUp(self):
        """إعداد الاختبار"""
        # إنشاء تطبيق للاختبار
        from app import create_app
        self.app = create_app('testing')
        self.client = self.app.test_client()
        self.app_context = self.app.app_context()
        self.app_context.push()
    
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        self.app_context.pop()
    
    def test_login_api_missing_data(self):
        """اختبار API تسجيل الدخول مع بيانات ناقصة"""
        response = self.client.post('/api/auth/login',
                                  data=json.dumps({}),
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertFalse(data['success'])
    
    def test_login_api_invalid_credentials(self):
        """اختبار API تسجيل الدخول مع بيانات خاطئة"""
        response = self.client.post('/api/auth/login',
                                  data=json.dumps({
                                      'email': '<EMAIL>',
                                      'password': 'wrong_password'
                                  }),
                                  content_type='application/json')
        
        self.assertEqual(response.status_code, 401)
        data = json.loads(response.data)
        self.assertFalse(data['success'])
    
    def test_me_api_without_token(self):
        """اختبار API الحصول على بيانات المستخدم بدون توكين"""
        response = self.client.get('/api/auth/me')
        
        self.assertEqual(response.status_code, 401)
        data = json.loads(response.data)
        self.assertFalse(data['success'])
    
    def test_logout_api_without_token(self):
        """اختبار API تسجيل الخروج بدون توكين"""
        response = self.client.post('/api/auth/logout')
        
        self.assertEqual(response.status_code, 401)
        data = json.loads(response.data)
        self.assertFalse(data['success'])

def run_auth_tests():
    """تشغيل جميع اختبارات المصادقة"""
    print("🧪 بدء اختبار نظام المصادقة...")
    print("=" * 50)

    # إنشاء test suite
    loader = unittest.TestLoader()
    test_suite = unittest.TestSuite()

    # إضافة اختبارات JWT
    test_suite.addTests(loader.loadTestsFromTestCase(TestJWTManager))
    print("✅ تم إضافة اختبارات JWT Manager")

    # إضافة اختبارات Auth Manager
    test_suite.addTests(loader.loadTestsFromTestCase(TestAuthManager))
    print("✅ تم إضافة اختبارات Auth Manager")

    # إضافة اختبارات Auth Middleware
    test_suite.addTests(loader.loadTestsFromTestCase(TestAuthMiddleware))
    print("✅ تم إضافة اختبارات Auth Middleware")

    # إضافة اختبارات APIs
    test_suite.addTests(loader.loadTestsFromTestCase(TestAuthAPIs))
    print("✅ تم إضافة اختبارات Auth APIs")

    print("=" * 50)

    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("=" * 50)
    print(f"📊 نتائج الاختبار:")
    print(f"   - الاختبارات المنجزة: {result.testsRun}")
    print(f"   - الاختبارات الناجحة: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"   - الاختبارات الفاشلة: {len(result.failures)}")
    print(f"   - الأخطاء: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ الاختبارات الفاشلة:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback}")
    
    if result.errors:
        print("\n🚨 الأخطاء:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback}")
    
    success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun) * 100
    print(f"\n🎯 معدل النجاح: {success_rate:.1f}%")
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_auth_tests()
    if success:
        print("\n🎉 جميع اختبارات المصادقة نجحت!")
    else:
        print("\n⚠️ بعض الاختبارات فشلت، يرجى مراجعة الأخطاء أعلاه.")
