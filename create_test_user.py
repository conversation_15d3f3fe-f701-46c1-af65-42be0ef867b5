"""
إنشاء مستخدم تجريبي لاختبار نظام المصادقة
Create Test User for Authentication System Testing
"""

import sys
import os
from flask import Flask

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_users():
    """إنشاء مستخدمين تجريبيين لاختبار النظام"""
    print("👤 إنشاء مستخدمين تجريبيين...")
    
    try:
        from app import create_app
        from utils.firebase_utils import get_firebase_manager
        from utils.auth_utils import get_auth_manager
        from models.database_models import DatabaseModels, UserRole
        
        # إنشاء التطبيق
        app = create_app('development')
        
        with app.app_context():
            firebase_manager = get_firebase_manager()
            auth_manager = get_auth_manager()
            
            # التأكد من تهيئة Firebase
            if not firebase_manager.is_connected():
                print("❌ Firebase غير متصل")
                return False
            
            # قائمة المستخدمين التجريبيين
            test_users = [
                {
                    'email': '<EMAIL>',
                    'password': 'admin123',
                    'role': UserRole.ADMIN,
                    'first_name': 'مدير',
                    'last_name': 'النظام',
                    'telegram_id': '999999999'
                },
                {
                    'email': '<EMAIL>',
                    'password': 'instructor123',
                    'role': UserRole.INSTRUCTOR,
                    'first_name': 'أحمد',
                    'last_name': 'المدرس',
                    'telegram_id': '888888888',
                    'specialization_id': 'medical_analysis'
                },
                {
                    'email': '<EMAIL>',
                    'password': 'student123',
                    'role': UserRole.STUDENT,
                    'first_name': 'محمد',
                    'last_name': 'الطالب',
                    'telegram_id': '777777777',
                    'specialization_id': 'medical_analysis'
                }
            ]
            
            created_users = []
            
            for user_info in test_users:
                # التحقق من وجود المستخدم مسبقاً
                existing_user = firebase_manager.get_user_by_email(user_info['email'])
                if existing_user:
                    print(f"⚠️ المستخدم {user_info['email']} موجود مسبقاً")
                    continue

                # إنشاء نموذج المستخدم
                user_data = DatabaseModels.create_user_model(
                    email=user_info['email'],
                    telegram_id=user_info['telegram_id'],
                    role=user_info['role'],
                    first_name=user_info['first_name'],
                    last_name=user_info['last_name'],
                    specialization_id=user_info.get('specialization_id')
                )

                # إضافة كلمة المرور غير المشفرة والحالة
                user_data['status'] = 'active'

                # إنشاء المستخدم مع كلمة مرور غير مشفرة
                user_id = auth_manager.create_user_with_plain_password(user_data, user_info['password'])
                
                if user_id:
                    created_users.append({
                        'id': user_id,
                        'email': user_info['email'],
                        'password': user_info['password'],  # كلمة المرور غير المشفرة للعرض
                        'role': user_info['role'],
                        'name': f"{user_info['first_name']} {user_info['last_name']}"
                    })
                    print(f"✅ تم إنشاء المستخدم: {user_info['email']}")
                else:
                    print(f"❌ فشل في إنشاء المستخدم: {user_info['email']}")
            
            # عرض تفاصيل المستخدمين المنشأين
            if created_users:
                print("\n" + "="*60)
                print("📋 تفاصيل المستخدمين التجريبيين:")
                print("="*60)
                
                for user in created_users:
                    print(f"\n👤 {user['name']} ({user['role']})")
                    print(f"   📧 البريد الإلكتروني: {user['email']}")
                    print(f"   🔑 كلمة المرور: {user['password']}")
                    print(f"   🆔 معرف المستخدم: {user['id']}")
                
                print("\n" + "="*60)
                print("🎯 يمكنك الآن استخدام هذه البيانات لاختبار نظام المصادقة")
                print("🌐 اذهب إلى /login وجرب تسجيل الدخول")
                
            return len(created_users) > 0
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدمين التجريبيين: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء مستخدمين تجريبيين لنظام المصادقة")
    print("="*60)
    
    success = create_test_users()
    
    if success:
        print("\n🎉 تم إنشاء المستخدمين التجريبيين بنجاح!")
        print("✅ يمكنك الآن اختبار نظام المصادقة")
    else:
        print("\n⚠️ فشل في إنشاء المستخدمين التجريبيين")
        print("🔧 يرجى التحقق من إعدادات Firebase")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
