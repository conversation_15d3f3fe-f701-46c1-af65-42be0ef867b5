#!/usr/bin/env python3
"""
اختبار أساسي للتحقق من صحة الكود
Basic test to verify code correctness
"""

import sys
import os

# إضافة المجلد الحالي للمسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار استيراد الوحدات"""
    print("🔍 اختبار استيراد الوحدات...")
    
    try:
        from utils.firebase_utils import FirebaseManager, get_firebase_manager
        print("✅ تم استيراد firebase_utils بنجاح")
    except ImportError as e:
        print(f"❌ فشل في استيراد firebase_utils: {e}")
        return False
    
    try:
        from models.database_models import DatabaseModels, UserRole, CourseStatus, EnrollmentStatus
        print("✅ تم استيراد database_models بنجاح")
    except ImportError as e:
        print(f"❌ فشل في استيراد database_models: {e}")
        return False
    
    try:
        from config import Config
        print("✅ تم استيراد config بنجاح")
    except ImportError as e:
        print(f"❌ فشل في استيراد config: {e}")
        return False
    
    return True

def test_database_models():
    """اختبار نماذج قاعدة البيانات"""
    print("\n📊 اختبار نماذج قاعدة البيانات...")
    
    try:
        from models.database_models import DatabaseModels, UserRole
        
        # اختبار إنشاء نموذج مستخدم
        user_model = DatabaseModels.create_user_model(
            email="<EMAIL>",
            telegram_id="123456789",
            role=UserRole.STUDENT,
            first_name="مستخدم",
            last_name="تجريبي"
        )
        
        required_fields = ['email', 'telegram_id', 'role', 'first_name', 'created_at']
        if all(field in user_model for field in required_fields):
            print("✅ نموذج المستخدم يحتوي على جميع الحقول المطلوبة")
        else:
            print("❌ نموذج المستخدم ناقص بعض الحقول")
            return False
        
        # اختبار إنشاء نموذج تخصص
        spec_model = DatabaseModels.create_specialization_model(
            name="التحليل الطبي",
            name_en="Medical Laboratory",
            icon="fas fa-microscope",
            description="تخصص التحليل الطبي"
        )
        
        spec_required_fields = ['name', 'name_en', 'icon', 'created_at']
        if all(field in spec_model for field in spec_required_fields):
            print("✅ نموذج التخصص يحتوي على جميع الحقول المطلوبة")
        else:
            print("❌ نموذج التخصص ناقص بعض الحقول")
            return False
        
        # اختبار مخطط قاعدة البيانات
        schema = DatabaseModels.get_database_schema()
        expected_collections = ['users', 'specializations', 'courses', 'lessons', 'enrollments']
        
        if all(collection in schema for collection in expected_collections):
            print("✅ مخطط قاعدة البيانات يحتوي على المجموعات الأساسية")
        else:
            print("❌ مخطط قاعدة البيانات ناقص بعض المجموعات")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نماذج قاعدة البيانات: {e}")
        return False

def test_firebase_manager():
    """اختبار مدير Firebase (بدون اتصال فعلي)"""
    print("\n🔥 اختبار مدير Firebase...")
    
    try:
        from utils.firebase_utils import FirebaseManager
        
        # إنشاء مثيل من المدير
        manager = FirebaseManager()
        print("✅ تم إنشاء مثيل من FirebaseManager")
        
        # التحقق من الحالة الأولية
        if not manager._initialized:
            print("✅ الحالة الأولية صحيحة (غير مهيأ)")
        else:
            print("❌ الحالة الأولية خاطئة")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار FirebaseManager: {e}")
        return False

def test_file_structure():
    """اختبار هيكل الملفات"""
    print("\n📁 اختبار هيكل الملفات...")
    
    required_files = [
        'utils/__init__.py',
        'utils/firebase_utils.py',
        'models/__init__.py',
        'models/database_models.py',
        'database_schema.json',
        'firebase_rules.json',
        'FIREBASE_INTEGRATION.md'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if not missing_files:
        print("✅ جميع الملفات المطلوبة موجودة")
        return True
    else:
        print(f"❌ الملفات المفقودة: {missing_files}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 بدء الاختبارات الأساسية...")
    print("=" * 50)
    
    tests = [
        ("هيكل الملفات", test_file_structure),
        ("استيراد الوحدات", test_imports),
        ("نماذج قاعدة البيانات", test_database_models),
        ("مدير Firebase", test_firebase_manager)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 اختبار: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                print(f"✅ نجح اختبار: {test_name}")
                passed += 1
            else:
                print(f"❌ فشل اختبار: {test_name}")
        except Exception as e:
            print(f"💥 خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 النتائج: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! الكود جاهز للاستخدام.")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
