{% extends "base.html" %}

{% block title %}إدارة الكورسات - {{ platform_name }}{% endblock %}

{% block extra_css %}
<style>
    .courses-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }
    
    .courses-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
    }
    
    .courses-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 2rem;
        margin-top: 2rem;
    }
    
    .course-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        padding: 1.5rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .course-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
    }
    
    .course-status {
        position: absolute;
        top: 1rem;
        right: 1rem;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .status-published {
        background: #d4edda;
        color: #155724;
    }
    
    .status-draft {
        background: #fff3cd;
        color: #856404;
    }
    
    .course-title {
        font-size: 1.25rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        margin-top: 2rem;
    }
    
    .course-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 1rem;
        font-size: 0.9rem;
        color: #6c757d;
    }
    
    .course-meta span {
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }
    
    .course-description {
        color: #6c757d;
        margin-bottom: 1.5rem;
        line-height: 1.6;
    }
    
    .course-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }
    
    .btn-action {
        padding: 0.5rem 1rem;
        border-radius: 10px;
        border: none;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.9rem;
    }
    
    .btn-edit {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .btn-edit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        color: white;
    }
    
    .btn-view {
        background: #28a745;
        color: white;
    }
    
    .btn-view:hover {
        background: #218838;
        color: white;
    }
    
    .btn-delete {
        background: #dc3545;
        color: white;
    }
    
    .btn-delete:hover {
        background: #c82333;
        color: white;
    }
    
    .btn-create {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 15px;
        padding: 1rem 2rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.1rem;
    }
    
    .btn-create:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 30px rgba(102, 126, 234, 0.3);
        color: white;
    }
    
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #6c757d;
    }
    
    .empty-state i {
        font-size: 4rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    .loading-spinner {
        text-align: center;
        padding: 4rem;
        color: #6c757d;
    }
    
    .error-message {
        background: #f8d7da;
        color: #721c24;
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 1rem;
        border-left: 4px solid #dc3545;
    }
    
    .stats-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        color: #6c757d;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="courses-container">
    <div class="container">
        <!-- Header -->
        <div class="courses-header">
            <h1><i class="fas fa-graduation-cap me-3"></i>إدارة الكورسات</h1>
            <p class="mb-3">إدارة وتنظيم الكورسات التعليمية الخاصة بك</p>
            <a href="{{ url_for('instructor_create_course') }}" class="btn-create">
                <i class="fas fa-plus-circle"></i>إنشاء كورس جديد
            </a>
        </div>
        
        <!-- Statistics -->
        <div class="stats-row" id="statsRow" style="display: none;">
            <div class="stat-card">
                <div class="stat-number" id="totalCourses">0</div>
                <div class="stat-label">إجمالي الكورسات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="publishedCourses">0</div>
                <div class="stat-label">كورسات منشورة</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="draftCourses">0</div>
                <div class="stat-label">مسودات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalEnrollments">0</div>
                <div class="stat-label">إجمالي التسجيلات</div>
            </div>
        </div>
        
        <!-- Error Container -->
        <div id="errorContainer"></div>
        
        <!-- Loading Spinner -->
        <div class="loading-spinner" id="loadingSpinner">
            <i class="fas fa-spinner fa-spin fa-3x"></i>
            <p class="mt-3">جاري تحميل الكورسات...</p>
        </div>
        
        <!-- Courses Grid -->
        <div class="courses-grid" id="coursesGrid" style="display: none;">
            <!-- سيتم ملء الكورسات هنا بواسطة JavaScript -->
        </div>
        
        <!-- Empty State -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="fas fa-book-open"></i>
            <h3>لا توجد كورسات بعد</h3>
            <p>ابدأ بإنشاء أول كورس تعليمي لك</p>
            <a href="{{ url_for('instructor_create_course') }}" class="btn-create mt-3">
                <i class="fas fa-plus-circle"></i>إنشاء كورس جديد
            </a>
        </div>
    </div>
</div>

<!-- Modal تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذا الكورس؟</p>
                <p class="text-danger"><strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/instructor-courses.js') }}"></script>
{% endblock %}
