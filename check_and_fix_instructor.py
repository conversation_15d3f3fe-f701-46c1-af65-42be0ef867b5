#!/usr/bin/env python3
"""
فحص وإصلاح بيانات المدرس
"""

import requests
import json
import sys
import firebase_admin
from firebase_admin import credentials, db

def initialize_firebase():
    """تهيئة Firebase"""
    try:
        try:
            app = firebase_admin.get_app()
        except ValueError:
            from config import Config
            config = Config()
            cred = credentials.Certificate(config.FIREBASE_CONFIG)
            app = firebase_admin.initialize_app(cred, {
                'databaseURL': config.FIREBASE_DATABASE_URL
            })
        return db.reference()
    except Exception as e:
        print(f"❌ فشل في تهيئة Firebase: {e}")
        return None

def check_and_fix_instructor():
    """فحص وإصلاح بيانات المدرس"""
    
    print("🔍 فحص وإصلاح بيانات المدرس <EMAIL>...")
    
    # تهيئة Firebase
    database = initialize_firebase()
    if not database:
        return False
    
    try:
        # البحث عن المدرس بالبريد الإلكتروني
        print("🔍 البحث عن المدرس في قاعدة البيانات...")
        
        users = database.child('users').get()
        instructor_id = None
        instructor_data = None
        
        if users:
            for user_id, user_data in users.items():
                if user_data.get('email') == '<EMAIL>':
                    instructor_id = user_id
                    instructor_data = user_data
                    break
        
        if not instructor_id:
            print("❌ لم يتم العثور على المدرس في قاعدة البيانات")
            return False
        
        print(f"✅ تم العثور على المدرس (ID: {instructor_id})")
        
        # عرض البيانات الحالية
        print("\n📊 البيانات الحالية للمدرس:")
        print(f"   الاسم: {instructor_data.get('full_name', 'غير محدد')}")
        print(f"   البريد: {instructor_data.get('email', 'غير محدد')}")
        print(f"   الدور: {instructor_data.get('role', 'غير محدد')}")
        print(f"   التخصص: {instructor_data.get('specialization_id', 'غير محدد')}")
        print(f"   الحالة: {instructor_data.get('status', 'غير محدد')}")
        
        permissions = instructor_data.get('permissions', {})
        print(f"\n🔐 الصلاحيات الحالية:")
        print(f"   إنشاء كورسات: {permissions.get('can_create_courses', False)}")
        print(f"   إدارة طلاب: {permissions.get('can_manage_students', False)}")
        print(f"   المراحل المسموحة: {permissions.get('allowed_stages', [])}")
        print(f"   إنشاء كورسات عامة: {permissions.get('can_create_general_courses', False)}")
        
        # إصلاح البيانات
        print(f"\n🔧 إصلاح بيانات المدرس...")
        
        updated_data = {
            'specialization_id': 'medical_analysis',  # تخصص التحليل الطبي
            'status': 'active',
            'permissions': {
                'can_create_courses': True,
                'can_manage_students': True,
                'allowed_stages': [1, 2, 3, 4],  # جميع المراحل
                'can_create_general_courses': True  # السماح بالكورسات العامة
            }
        }
        
        # تحديث البيانات
        database.child('users').child(instructor_id).update(updated_data)
        print("✅ تم تحديث بيانات المدرس")
        
        # التحقق من التحديث
        updated_instructor = database.child('users').child(instructor_id).get()
        
        print(f"\n📊 البيانات بعد التحديث:")
        print(f"   التخصص: {updated_instructor.get('specialization_id', 'غير محدد')}")
        print(f"   الحالة: {updated_instructor.get('status', 'غير محدد')}")
        
        updated_permissions = updated_instructor.get('permissions', {})
        print(f"\n🔐 الصلاحيات بعد التحديث:")
        print(f"   إنشاء كورسات: {updated_permissions.get('can_create_courses', False)}")
        print(f"   إدارة طلاب: {updated_permissions.get('can_manage_students', False)}")
        print(f"   المراحل المسموحة: {updated_permissions.get('allowed_stages', [])}")
        print(f"   إنشاء كورسات عامة: {updated_permissions.get('can_create_general_courses', False)}")
        
        # الآن محاولة إنشاء كورس تجريبي
        print(f"\n📚 محاولة إنشاء كورس تجريبي...")
        
        # تسجيل الدخول أولاً
        base_url = "http://127.0.0.1:5000"
        session = requests.Session()
        
        login_data = {
            'email': '<EMAIL>',
            'password': 'instructor123'
        }
        
        login_response = session.post(
            f"{base_url}/api/auth/login",
            json=login_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            token = login_result.get('token')
            if token:
                session.headers.update({'Authorization': f'Bearer {token}'})
            
            # إنشاء كورس تجريبي
            course_data = {
                'title': 'كورس تجريبي - التحليل الطبي',
                'description': 'كورس تجريبي في التحليل الطبي للمرحلة الثانية',
                'stage': 2,
                'status': 'draft',
                'is_general': False,
                'specialization_id': 'medical_analysis'
            }
            
            create_response = session.post(
                f"{base_url}/api/instructor/courses",
                json=course_data,
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"📊 استجابة إنشاء الكورس: {create_response.status_code}")
            
            if create_response.status_code == 201:
                create_result = create_response.json()
                if create_result.get('success'):
                    course_id = create_result.get('course_id')
                    print(f"✅ تم إنشاء الكورس التجريبي بنجاح (ID: {course_id})")
                    
                    # التحقق من ظهور الكورس
                    courses_response = session.get(f"{base_url}/api/instructor/courses")
                    if courses_response.status_code == 200:
                        courses_result = courses_response.json()
                        if courses_result.get('success'):
                            courses = courses_result.get('courses', [])
                            print(f"📊 عدد الكورسات بعد الإنشاء: {len(courses)}")
                            
                            if courses:
                                print("\n🎉 نجح الإصلاح! الكورسات تظهر الآن:")
                                for i, course in enumerate(courses, 1):
                                    title = course.get('title', 'بدون عنوان')
                                    status = course.get('status', 'غير محدد')
                                    print(f"  {i}. {title} - {status}")
                                
                                print(f"\n📋 الآن يمكنك:")
                                print(f"1. الذهاب إلى: http://127.0.0.1:5000/login")
                                print(f"2. تسجيل الدخول بـ: <EMAIL> / instructor123")
                                print(f"3. الذهاب إلى: http://127.0.0.1:5000/instructor/courses")
                                print(f"4. ستجد الكورسات تظهر بشكل صحيح!")
                                
                                return True
                            else:
                                print("❌ لا تزال الكورسات لا تظهر")
                                return False
                else:
                    print(f"❌ فشل في إنشاء الكورس: {create_result.get('message', 'غير محدد')}")
                    return False
            else:
                print(f"❌ خطأ في إنشاء الكورس: {create_response.status_code}")
                print(f"   الاستجابة: {create_response.text}")
                return False
        else:
            print("❌ فشل في تسجيل الدخول")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في العملية: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = check_and_fix_instructor()
    if success:
        print("\n✅ تم إصلاح المدرس وإنشاء الكورسات بنجاح!")
        sys.exit(0)
    else:
        print("\n❌ فشل في إصلاح المدرس")
        sys.exit(1)
