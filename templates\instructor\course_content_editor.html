{% extends "base.html" %}

{% block title %}محرر المحتوى - {{ course.title }} - {{ platform_name }}{% endblock %}

{% block extra_css %}
<!-- Quill Editor CSS -->
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
<!-- SortableJS CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.css">

<style>
    .content-editor-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }
    
    .editor-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
    }
    
    .editor-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        text-align: center;
    }
    
    .editor-body {
        padding: 2rem;
    }
    
    .content-toolbar {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 2rem;
        border: 1px solid #e9ecef;
    }
    
    .content-type-btn {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 0.75rem 1rem;
        margin: 0.25rem;
        color: #6c757d;
        font-weight: 500;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .content-type-btn:hover {
        border-color: #667eea;
        color: #667eea;
        transform: translateY(-2px);
    }
    
    .content-type-btn.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
        color: white;
    }
    
    .lessons-container {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    }
    
    .lesson-item {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
        cursor: move;
    }
    
    .lesson-item:hover {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }
    
    .lesson-item.active {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.05);
    }
    
    .lesson-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 0.5rem;
    }
    
    .lesson-title {
        font-weight: 600;
        color: #2c3e50;
        margin: 0;
        flex-grow: 1;
    }
    
    .lesson-type-badge {
        background: #667eea;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .lesson-actions {
        display: flex;
        gap: 0.5rem;
        margin-top: 0.5rem;
    }
    
    .lesson-btn {
        background: none;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 0.25rem 0.5rem;
        color: #6c757d;
        font-size: 0.8rem;
        transition: all 0.3s ease;
    }
    
    .lesson-btn:hover {
        background: #667eea;
        border-color: #667eea;
        color: white;
    }
    
    .lesson-btn.danger:hover {
        background: #dc3545;
        border-color: #dc3545;
    }
    
    .content-editor-panel {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        display: none;
    }
    
    .content-editor-panel.active {
        display: block;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        display: block;
    }
    
    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        color: white;
    }
    
    .btn-secondary {
        background: #6c757d;
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-secondary:hover {
        background: #5a6268;
        color: white;
    }
    
    .quill-editor {
        min-height: 200px;
        border-radius: 10px;
    }
    
    .file-upload-area {
        border: 2px dashed #dee2e6;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .file-upload-area:hover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.05);
    }
    
    .file-upload-area.dragover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.1);
    }
    
    .option-input {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }
    
    .option-input input {
        flex-grow: 1;
        margin-right: 0.5rem;
    }
    
    .option-input .form-check-input {
        margin-left: 0.5rem;
    }
    
    .remove-option-btn {
        background: #dc3545;
        border: none;
        color: white;
        border-radius: 5px;
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }
    
    .add-option-btn {
        background: #28a745;
        border: none;
        color: white;
        border-radius: 5px;
        padding: 0.5rem 1rem;
        margin-top: 0.5rem;
    }
    
    .loading-spinner {
        display: none;
        text-align: center;
        padding: 2rem;
    }
    
    .error-message {
        background: #f8d7da;
        color: #721c24;
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 1rem;
        border-left: 4px solid #dc3545;
    }
    
    .success-message {
        background: #d4edda;
        color: #155724;
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 1rem;
        border-left: 4px solid #28a745;
    }
    
    .drag-handle {
        cursor: move;
        color: #6c757d;
        margin-right: 0.5rem;
    }
    
    .sortable-ghost {
        opacity: 0.4;
    }
    
    .sortable-chosen {
        transform: scale(1.02);
    }

    .pages-management {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 0.75rem;
        border: 1px solid #e9ecef;
    }

    .lessons-stats .badge {
        font-size: 0.75rem;
    }

    .page-controls {
        display: flex;
        align-items: center;
    }

    .lessons-actions {
        border-top: 1px solid #e9ecef;
        padding-top: 1rem;
    }

    .lesson-item .lesson-meta {
        font-size: 0.8rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }

    .lesson-item .lesson-page-badge {
        background: #e9ecef;
        color: #495057;
        padding: 0.15rem 0.5rem;
        border-radius: 10px;
        font-size: 0.7rem;
        margin-left: 0.5rem;
    }

    .bulk-actions-panel {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
        display: none;
    }

    .bulk-actions-panel.active {
        display: block;
    }
</style>
{% endblock %}

{% block content %}
<div class="content-editor-container">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="editor-card">
                    <div class="editor-header">
                        <h2><i class="fas fa-edit me-2"></i>محرر المحتوى</h2>
                        <p class="mb-0">{{ course.title }}</p>
                    </div>
                    
                    <div class="editor-body">
                        <!-- شريط الأدوات -->
                        <div class="content-toolbar">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h5 class="mb-2"><i class="fas fa-plus-circle me-2"></i>إضافة محتوى جديد</h5>
                                    <div class="content-types">
                                        <button class="content-type-btn" data-type="text">
                                            <i class="fas fa-font me-2"></i>نص
                                        </button>
                                        <button class="content-type-btn" data-type="image">
                                            <i class="fas fa-image me-2"></i>صورة
                                        </button>
                                        <button class="content-type-btn" data-type="video">
                                            <i class="fas fa-video me-2"></i>فيديو
                                        </button>
                                        <button class="content-type-btn" data-type="mcq">
                                            <i class="fas fa-list-ul me-2"></i>أسئلة متعددة
                                        </button>
                                        <button class="content-type-btn" data-type="essay">
                                            <i class="fas fa-pen-alt me-2"></i>سؤال مقالي
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-4 text-end">
                                    <button class="btn btn-info me-2" onclick="previewCourse()">
                                        <i class="fas fa-eye me-2"></i>معاينة الكورس
                                    </button>
                                    <button class="btn btn-secondary me-2" onclick="saveDraft()">
                                        <i class="fas fa-save me-2"></i>حفظ مسودة
                                    </button>
                                    <button class="btn btn-primary" onclick="publishCourse()">
                                        <i class="fas fa-check me-2"></i>نشر الكورس
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- رسائل النظام -->
                        <div id="messageContainer"></div>
                        
                        <div class="row">
                            <!-- قائمة الدروس -->
                            <div class="col-md-4">
                                <div class="lessons-container">
                                    <div class="d-flex justify-content-between align-items-center mb-3">
                                        <h5><i class="fas fa-list me-2"></i>دروس الكورس</h5>
                                        <div class="lessons-stats">
                                            <span class="badge bg-primary" id="lessonsCount">0 دروس</span>
                                        </div>
                                    </div>

                                    <!-- إدارة الصفحات -->
                                    <div class="pages-management mb-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <label class="form-label mb-0">الصفحة الحالية:</label>
                                            <div class="page-controls">
                                                <select id="currentPageSelect" class="form-select form-select-sm" style="width: auto;">
                                                    <option value="1">الصفحة 1</option>
                                                </select>
                                                <button class="btn btn-sm btn-outline-primary ms-2" onclick="addNewPage()">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="lessonsList" class="lessons-list">
                                        <!-- سيتم تحميل الدروس هنا -->
                                    </div>

                                    <!-- أزرار إدارة الدروس -->
                                    <div class="lessons-actions mt-3">
                                        <button class="btn btn-sm btn-outline-success w-100 mb-2" onclick="showBulkActions()">
                                            <i class="fas fa-tasks me-2"></i>إدارة متعددة
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- محرر المحتوى -->
                            <div class="col-md-8">
                                <!-- محرر النص -->
                                <div id="textEditor" class="content-editor-panel">
                                    <h5><i class="fas fa-font me-2"></i>محرر النص</h5>
                                    <form id="textForm">
                                        <div class="form-group">
                                            <label for="textTitle" class="form-label">عنوان الدرس</label>
                                            <input type="text" class="form-control" id="textTitle" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="textContent" class="form-label">المحتوى</label>
                                            <div id="textContent" class="quill-editor"></div>
                                        </div>
                                        <div class="form-group">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>حفظ الدرس
                                            </button>
                                            <button type="button" class="btn btn-secondary ms-2" onclick="cancelEdit()">
                                                <i class="fas fa-times me-2"></i>إلغاء
                                            </button>
                                        </div>
                                    </form>
                                </div>
                                
                                <!-- محرر الصور -->
                                <div id="imageEditor" class="content-editor-panel">
                                    <h5><i class="fas fa-image me-2"></i>محرر الصور</h5>
                                    <form id="imageForm">
                                        <div class="form-group">
                                            <label for="imageTitle" class="form-label">عنوان الدرس</label>
                                            <input type="text" class="form-control" id="imageTitle" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="imageUpload" class="form-label">رفع الصورة</label>
                                            <div class="file-upload-area" id="imageUploadArea">
                                                <i class="fas fa-cloud-upload-alt fa-3x mb-3 text-muted"></i>
                                                <p>اسحب الصورة هنا أو انقر للاختيار</p>
                                                <input type="file" id="imageUpload" accept="image/*" style="display: none;">
                                            </div>
                                            <div id="imagePreview" class="mt-3" style="display: none;">
                                                <img id="previewImg" class="img-fluid rounded" style="max-height: 300px;">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="imageCaption" class="form-label">وصف الصورة</label>
                                            <textarea class="form-control" id="imageCaption" rows="3"></textarea>
                                        </div>
                                        <div class="form-group">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>حفظ الدرس
                                            </button>
                                            <button type="button" class="btn btn-secondary ms-2" onclick="cancelEdit()">
                                                <i class="fas fa-times me-2"></i>إلغاء
                                            </button>
                                        </div>
                                    </form>
                                </div>
                                
                                <!-- محرر الفيديو -->
                                <div id="videoEditor" class="content-editor-panel">
                                    <h5><i class="fas fa-video me-2"></i>محرر الفيديو</h5>
                                    <form id="videoForm">
                                        <div class="form-group">
                                            <label for="videoTitle" class="form-label">عنوان الدرس</label>
                                            <input type="text" class="form-control" id="videoTitle" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="videoUrl" class="form-label">رابط الفيديو (YouTube)</label>
                                            <input type="url" class="form-control" id="videoUrl"
                                                   placeholder="https://www.youtube.com/watch?v=..." required>
                                            <small class="form-text text-muted">أدخل رابط فيديو YouTube</small>
                                        </div>
                                        <div class="form-group">
                                            <label for="videoDescription" class="form-label">وصف الفيديو</label>
                                            <textarea class="form-control" id="videoDescription" rows="3"></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label for="videoDuration" class="form-label">مدة الفيديو (بالدقائق)</label>
                                            <input type="number" class="form-control" id="videoDuration" min="1">
                                        </div>
                                        <div class="form-group">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>حفظ الدرس
                                            </button>
                                            <button type="button" class="btn btn-secondary ms-2" onclick="cancelEdit()">
                                                <i class="fas fa-times me-2"></i>إلغاء
                                            </button>
                                        </div>
                                    </form>
                                </div>

                                <!-- محرر الأسئلة متعددة الخيارات -->
                                <div id="mcqEditor" class="content-editor-panel">
                                    <h5><i class="fas fa-list-ul me-2"></i>محرر الأسئلة متعددة الخيارات</h5>
                                    <form id="mcqForm">
                                        <div class="form-group">
                                            <label for="mcqTitle" class="form-label">عنوان الدرس</label>
                                            <input type="text" class="form-control" id="mcqTitle" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="mcqQuestion" class="form-label">نص السؤال</label>
                                            <textarea class="form-control" id="mcqQuestion" rows="3" required></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label class="form-label">الخيارات</label>
                                            <div id="mcqOptions">
                                                <div class="option-input">
                                                    <input type="text" class="form-control" placeholder="الخيار الأول" required>
                                                    <input type="checkbox" class="form-check-input" title="إجابة صحيحة">
                                                    <button type="button" class="remove-option-btn" onclick="removeOption(this)" style="display: none;">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                                <div class="option-input">
                                                    <input type="text" class="form-control" placeholder="الخيار الثاني" required>
                                                    <input type="checkbox" class="form-check-input" title="إجابة صحيحة">
                                                    <button type="button" class="remove-option-btn" onclick="removeOption(this)" style="display: none;">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <button type="button" class="add-option-btn" onclick="addMcqOption()">
                                                <i class="fas fa-plus me-2"></i>إضافة خيار
                                            </button>
                                        </div>
                                        <div class="form-group">
                                            <label for="mcqFeedback" class="form-label">تفسير الإجابة (اختياري)</label>
                                            <textarea class="form-control" id="mcqFeedback" rows="2"></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label for="mcqPoints" class="form-label">النقاط</label>
                                            <input type="number" class="form-control" id="mcqPoints" value="1" min="1">
                                        </div>
                                        <div class="form-group">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>حفظ الدرس
                                            </button>
                                            <button type="button" class="btn btn-secondary ms-2" onclick="cancelEdit()">
                                                <i class="fas fa-times me-2"></i>إلغاء
                                            </button>
                                        </div>
                                    </form>
                                </div>

                                <!-- محرر الأسئلة المقالية -->
                                <div id="essayEditor" class="content-editor-panel">
                                    <h5><i class="fas fa-pen-alt me-2"></i>محرر الأسئلة المقالية</h5>
                                    <form id="essayForm">
                                        <div class="form-group">
                                            <label for="essayTitle" class="form-label">عنوان الدرس</label>
                                            <input type="text" class="form-control" id="essayTitle" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="essayQuestion" class="form-label">نص السؤال</label>
                                            <textarea class="form-control" id="essayQuestion" rows="4" required></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label for="essayMaxWords" class="form-label">الحد الأقصى للكلمات</label>
                                            <input type="number" class="form-control" id="essayMaxWords" value="500" min="50">
                                        </div>
                                        <div class="form-group">
                                            <label for="essayRubric" class="form-label">معايير التقييم (اختياري)</label>
                                            <textarea class="form-control" id="essayRubric" rows="3"
                                                      placeholder="أدخل معايير التقييم والنقاط المطلوبة"></textarea>
                                        </div>
                                        <div class="form-group">
                                            <label for="essayPoints" class="form-label">النقاط</label>
                                            <input type="number" class="form-control" id="essayPoints" value="10" min="1">
                                        </div>
                                        <div class="form-group">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>حفظ الدرس
                                            </button>
                                            <button type="button" class="btn btn-secondary ms-2" onclick="cancelEdit()">
                                                <i class="fas fa-times me-2"></i>إلغاء
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مؤشر التحميل -->
<div class="loading-spinner" id="loadingSpinner">
    <i class="fas fa-spinner fa-spin fa-2x"></i>
    <p class="mt-2">جاري المعالجة...</p>
</div>
{% endblock %}

{% block extra_js %}
<!-- Quill Editor JS -->
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
<!-- SortableJS -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<!-- محرر المحتوى -->
<script src="{{ url_for('static', filename='js/content-editor.js') }}"></script>

<script>
// إنشاء مثيل من محرر المحتوى
let contentEditor;

// متغير عام لمحرر المحتوى
let contentEditor;

$(document).ready(function() {
    contentEditor = new ContentEditor();
});

// دالة معاينة الكورس
function previewCourse() {
    if (!contentEditor) {
        console.error('contentEditor غير مهيأ');
        return;
    }
    const courseId = contentEditor.courseId;
    if (!courseId) {
        console.error('معرف الكورس غير موجود');
        return;
    }
    console.log('فتح معاينة الكورس:', courseId);
    window.open(`/instructor/courses/${courseId}/preview`, '_blank');
}

// دالة إضافة صفحة جديدة
function addNewPage() {
    contentEditor.addNewPage();
}

// دالة إظهار الإجراءات المتعددة
function showBulkActions() {
    contentEditor.showBulkActions();
}

// دوال إضافية لمحرر الأسئلة متعددة الخيارات
function addMcqOption() {
    const container = document.getElementById('mcqOptions');
    const optionDiv = document.createElement('div');
    optionDiv.className = 'option-input';
    optionDiv.innerHTML = `
        <input type="text" class="form-control" placeholder="خيار جديد" required>
        <input type="checkbox" class="form-check-input" title="إجابة صحيحة">
        <button type="button" class="remove-option-btn" onclick="removeOption(this)">
            <i class="fas fa-times"></i>
        </button>
    `;
    container.appendChild(optionDiv);
    updateRemoveButtons();
}

function removeOption(button) {
    button.parentElement.remove();
    updateRemoveButtons();
}

function updateRemoveButtons() {
    const options = document.querySelectorAll('#mcqOptions .option-input');
    options.forEach((option, index) => {
        const removeBtn = option.querySelector('.remove-option-btn');
        if (options.length > 2) {
            removeBtn.style.display = 'inline-block';
        } else {
            removeBtn.style.display = 'none';
        }
    });
}

function cancelEdit() {
    // إخفاء جميع محررات المحتوى
    document.querySelectorAll('.content-editor-panel').forEach(panel => {
        panel.classList.remove('active');
    });

    // إزالة التحديد من أزرار نوع المحتوى
    document.querySelectorAll('.content-type-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // إعادة تعيين المتغيرات
    if (contentEditor) {
        contentEditor.currentLessonId = null;
        contentEditor.currentContentType = null;
    }
}

function saveDraft() {
    // حفظ مسودة الكورس
    contentEditor.showAlert('تم حفظ المسودة بنجاح', 'success');
}

function publishCourse() {
    if (contentEditor.lessons.length === 0) {
        contentEditor.showAlert('لا يمكن نشر كورس فارغ. يرجى إضافة محتوى أولاً', 'warning');
        return;
    }

    if (confirm('هل أنت متأكد من نشر الكورس؟ سيصبح متاحاً للطلاب فوراً.')) {
        // منطق نشر الكورس
        contentEditor.showAlert('تم نشر الكورس بنجاح', 'success');
    }
}
</script>
{% endblock %}
