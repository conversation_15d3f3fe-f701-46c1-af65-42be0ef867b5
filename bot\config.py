"""
إعدادات البوت
Bot Configuration

إعدادات خاصة ببوت التليجرام منفصلة عن المشروع الرئيسي
"""

import os
from dotenv import load_dotenv

# تحميل متغيرات البيئة من مجلد البوت
bot_dir = os.path.dirname(os.path.abspath(__file__))
env_path = os.path.join(bot_dir, '.env')
load_dotenv(env_path)

class BotConfig:
    """إعدادات البوت الأساسية"""

    # إعدادات Firebase
    FIREBASE_CONFIG = {
        "type": os.environ.get('FIREBASE_TYPE', 'service_account'),
        "project_id": os.environ.get('FIREBASE_PROJECT_ID'),
        "private_key_id": os.environ.get('FIREBASE_PRIVATE_KEY_ID'),
        "private_key": os.environ.get('FIREBASE_PRIVATE_KEY', '').replace('\\n', '\n'),
        "client_email": os.environ.get('FIREBASE_CLIENT_EMAIL'),
        "client_id": os.environ.get('FIREBASE_CLIENT_ID'),
        "auth_uri": os.environ.get('FIREBASE_AUTH_URI', 'https://accounts.google.com/o/oauth2/auth'),
        "token_uri": os.environ.get('FIREBASE_TOKEN_URI', 'https://oauth2.googleapis.com/token'),
        "auth_provider_x509_cert_url": os.environ.get('FIREBASE_AUTH_PROVIDER_X509_CERT_URL', 'https://www.googleapis.com/oauth2/v1/certs'),
        "client_x509_cert_url": os.environ.get('FIREBASE_CLIENT_X509_CERT_URL')
    }

    FIREBASE_DATABASE_URL = os.environ.get('FIREBASE_DATABASE_URL')

    def __getitem__(self, key):
        """للسماح بالوصول للإعدادات كقاموس"""
        return getattr(self, key, None)
    
    # إعدادات بوت التليجرام
    TELEGRAM_BOT_TOKEN = os.environ.get('TELEGRAM_BOT_TOKEN')
    BOT_OWNER_TELEGRAM_ID = os.environ.get('BOT_OWNER_TELEGRAM_ID')
    
    # إعدادات المنصة
    PLATFORM_URL = os.environ.get('PLATFORM_URL', 'http://localhost:5000')
    PLATFORM_NAME = os.environ.get('PLATFORM_NAME', 'منصة الكورسات التعليمية')
    
    # إعدادات JWT
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'bot-jwt-secret-key'
    JWT_EXPIRATION_HOURS = int(os.environ.get('JWT_EXPIRATION_HOURS', 24))
    
    @staticmethod
    def init_bot():
        """تهيئة البوت مع الإعدادات"""
        pass

# إنشاء مثيل من الإعدادات
config = BotConfig()
