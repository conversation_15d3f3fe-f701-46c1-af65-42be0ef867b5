"""
بوت التليجرام الرئيسي لمنصة الكورسات التعليمية
Main Telegram bot for the educational courses platform
"""

import os
import sys
import logging
from dotenv import load_dotenv

# إضافة المجلد الرئيسي للمسار
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# تحميل متغيرات البيئة من مجلد البوت
bot_dir = os.path.dirname(os.path.abspath(__file__))
env_path = os.path.join(bot_dir, '.env')
load_dotenv(env_path)

# استيراد وظائف Firebase
from utils.firebase_utils import get_firebase_manager
from models.database_models import DatabaseModels, UserRole

# استيراد إعدادات البوت
from config import config

# استيراد معالجات إنشاء المدرسين
from handlers.instructor_creation import InstructorCreationHandler

# استيراد معالجات دعوة الطلاب
from handlers.student_invitation import StudentInvitationHandler

# إعداد التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bot/bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

try:
    import telebot
    from telebot import types
except ImportError:
    logger.error("مكتبة telebot غير مثبتة. قم بتثبيتها باستخدام: pip install pyTelegramBotAPI")
    sys.exit(1)

# إعدادات البوت
BOT_TOKEN = config.TELEGRAM_BOT_TOKEN
BOT_OWNER_ID = str(config.BOT_OWNER_TELEGRAM_ID)  # تأكد من أنه string
PLATFORM_URL = config.PLATFORM_URL

if not BOT_TOKEN:
    logger.error("TELEGRAM_BOT_TOKEN غير محدد في متغيرات البيئة")
    sys.exit(1)

if not BOT_OWNER_ID:
    logger.error("BOT_OWNER_TELEGRAM_ID غير محدد في متغيرات البيئة")
    sys.exit(1)



# إنشاء البوت
bot = telebot.TeleBot(BOT_TOKEN)

# تهيئة Firebase
firebase_manager = get_firebase_manager()

# تحويل إعدادات البوت إلى قاموس
firebase_config = {
    'FIREBASE_CONFIG': config.FIREBASE_CONFIG,
    'FIREBASE_DATABASE_URL': config.FIREBASE_DATABASE_URL
}

if firebase_manager.initialize(firebase_config):
    logger.info("تم تهيئة Firebase في البوت بنجاح")
else:
    logger.error("فشل في تهيئة Firebase في البوت")
    sys.exit(1)

# تهيئة معالج إنشاء المدرسين
instructor_handler = InstructorCreationHandler(bot)

# تهيئة معالج دعوة الطلاب
student_invitation_handler = StudentInvitationHandler(bot)

# ===== معالجات الأوامر =====

@bot.message_handler(commands=['start'])
def start_command(message):
    """معالج أمر البدء"""
    user_id = str(message.from_user.id)
    username = message.from_user.username or "غير محدد"
    first_name = message.from_user.first_name or "مستخدم"

    logger.info(f"مستخدم جديد: {first_name} (@{username}) - ID: {user_id}")

    # التحقق من وجود معامل في الأمر (رابط دعوة)
    command_parts = message.text.split()
    if len(command_parts) > 1:
        start_param = command_parts[1]

        # التحقق من رابط دعوة المدرس
        if start_param.startswith('instructor_'):
            link_id = start_param.replace('instructor_', '')
            instructor_handler.handle_invitation_link(message, link_id)
            return

        # التحقق من رابط دعوة الطلاب
        elif start_param.startswith('student_'):
            link_id = start_param.replace('student_', '')
            handle_student_invitation_link(message, link_id)
            return

    # التحقق من مالك البوت
    if user_id == BOT_OWNER_ID:
        welcome_text = f"""
🤖 مرحباً بك يا مالك البوت!

أنت الآن متصل ببوت منصة الكورسات التعليمية.

الأوامر المتاحة:
/admin - لوحة تحكم الأدمن
/create_instructor - إنشاء حساب مدرس جديد
/help - المساعدة

🌐 رابط المنصة: {PLATFORM_URL}
        """
        
        # إنشاء لوحة مفاتيح للأدمن
        markup = types.ReplyKeyboardMarkup(resize_keyboard=True, row_width=2)
        markup.add(
            types.KeyboardButton("👨‍🏫 إنشاء حساب مدرس"),
            types.KeyboardButton("📊 الإحصائيات"),
            types.KeyboardButton("⚙️ الإعدادات"),
            types.KeyboardButton("❓ المساعدة")
        )
        
        bot.send_message(message.chat.id, welcome_text, reply_markup=markup)
    else:
        # التحقق من أن المستخدم مدرس مسجل
        user = firebase_manager.get_user_by_telegram_id(user_id)
        if user and user.get('role') == UserRole.INSTRUCTOR.value:
            # رسالة ترحيب للمدرس
            instructor_name = f"{user.get('first_name', '')} {user.get('last_name', '')}".strip()
            welcome_text = f"""
👋 مرحباً {instructor_name}!

أنت مسجل كمدرس في منصة الكورسات التعليمية.

🎓 الوظائف المتاحة لك:
• إنشاء روابط دعوة للطلاب
• إدارة طلابك
• متابعة الإحصائيات

🌐 رابط المنصة: {PLATFORM_URL}

للمساعدة: /help
            """

            # إنشاء لوحة مفاتيح للمدرس
            markup = types.ReplyKeyboardMarkup(resize_keyboard=True, row_width=2)
            markup.add(
                types.KeyboardButton("🎓 إنشاء رابط دعوة للطلاب"),
                types.KeyboardButton("📊 إحصائياتي"),
                types.KeyboardButton("❓ المساعدة")
            )

            bot.send_message(message.chat.id, welcome_text, reply_markup=markup)
        else:
            welcome_text = f"""
👋 مرحباً {first_name}!

هذا بوت منصة الكورسات التعليمية للتخصصات الطبية.

🎓 للحصول على حساب في المنصة:
• إذا كنت طالباً: استخدم رابط الدعوة من مدرسك
• إذا كنت مدرساً: تواصل مع إدارة المنصة

🌐 رابط المنصة: {PLATFORM_URL}

للمساعدة: /help
            """

            bot.send_message(message.chat.id, welcome_text)

@bot.message_handler(commands=['help'])
def help_command(message):
    """معالج أمر المساعدة"""
    user_id = str(message.from_user.id)
    
    if user_id == BOT_OWNER_ID:
        help_text = """
📚 أوامر مالك البوت:

/start - بدء البوت
/admin - لوحة تحكم الأدمن
/create_instructor - إنشاء حساب مدرس
/stats - إحصائيات المنصة
/help - هذه المساعدة

🔧 الوظائف المتاحة:
• إنشاء حسابات المدرسين
• إدارة التخصصات والصلاحيات
• مراقبة النشاط
• إنشاء أكواد التفعيل الشاملة

🌐 المنصة: {PLATFORM_URL}
        """
    else:
        help_text = """
📚 مساعدة البوت:

هذا بوت منصة الكورسات التعليمية للتخصصات الطبية.

🎯 التخصصات المتاحة:
• التحليل الطبي
• الأشعة
• التخدير

📋 للحصول على حساب:
• الطلاب: رابط دعوة من المدرس
• المدرسين: التواصل مع الإدارة

🌐 المنصة: {PLATFORM_URL}
        """
    
    bot.send_message(message.chat.id, help_text)

def handle_student_invitation_link(message, link_id: str):
    """معالجة رابط دعوة الطلاب"""
    try:
        from utils.user_creation_utils import UserCreationManager
        from bot.utils.invitation_utils import get_bot_invitation_manager

        user_id = str(message.from_user.id)
        username = message.from_user.username or ""
        first_name = message.from_user.first_name or "طالب"

        # التحقق من صحة الرابط
        invitation_manager = get_bot_invitation_manager()
        is_valid, link_data, error_message = invitation_manager.validate_invitation_link(link_id)

        if not is_valid:
            bot.send_message(message.chat.id, f"❌ {error_message}")
            return

        # التحقق من أن الرابط للطلاب
        if link_data.get('link_type') != 'student_invite':
            bot.send_message(message.chat.id, "❌ هذا الرابط غير صالح للطلاب.")
            return

        # التحقق من عدم وجود حساب مسبق لنفس معرف التليجرام
        existing_user = firebase_manager.get_user_by_telegram_id(user_id)
        if existing_user:
            bot.send_message(
                message.chat.id,
                "❌ لديك حساب مسجل مسبقاً في المنصة. لا يمكن إنشاء حساب جديد."
            )
            return

        # إنشاء حساب الطالب
        user_creation_manager = UserCreationManager()
        target_data = link_data.get('target_data', {})

        success, user_data, error_msg = user_creation_manager.create_student_account(
            telegram_id=user_id,
            specialization_id=target_data.get('specialization_id'),
            instructor_id=target_data.get('instructor_id')
        )

        # استخراج كلمة المرور من بيانات المستخدم
        password = user_data.get('password') if user_data else None

        if success:
            # تحديث استخدام الرابط
            invitation_manager.use_invitation_link(link_id, user_id)

            # الحصول على اسم التخصص
            try:
                if hasattr(firebase_manager, 'get_specialization'):
                    specialization = firebase_manager.get_specialization(target_data.get('specialization_id'))
                    specialization_name = specialization.get('name', 'غير محدد') if specialization else 'غير محدد'
                else:
                    # حل بديل: الحصول على جميع التخصصات والبحث
                    all_specs = firebase_manager.get_all_specializations()
                    specialization_name = 'غير محدد'
                    for spec in all_specs:
                        if spec.get('id') == target_data.get('specialization_id'):
                            specialization_name = spec.get('name', 'غير محدد')
                            break
            except Exception as e:
                logger.error(f"خطأ في الحصول على التخصص: {e}")
                specialization_name = 'غير محدد'

            success_text = f"""
✅ تم إنشاء حسابك بنجاح!

🎓 بيانات الدخول:
📧 البريد الإلكتروني: {user_data.get('email')}
🔑 كلمة المرور: {password}

📚 التخصص: {specialization_name}
🌐 رابط المنصة: {PLATFORM_URL}

⚠️ احتفظ ببيانات الدخول في مكان آمن!

🎯 يمكنك الآن تسجيل الدخول إلى المنصة والبدء في التعلم.
            """

            bot.send_message(message.chat.id, success_text)

        else:
            bot.send_message(message.chat.id, f"❌ فشل في إنشاء الحساب: {error_msg}")

    except Exception as e:
        logger.error(f"خطأ في معالجة رابط دعوة الطلاب: {e}")
        bot.send_message(message.chat.id, "❌ حدث خطأ في النظام. حاول مرة أخرى لاحقاً.")

@bot.message_handler(commands=['admin'])
def admin_command(message):
    """معالج أمر لوحة الأدمن"""
    user_id = str(message.from_user.id)
    
    if user_id != BOT_OWNER_ID:
        bot.send_message(message.chat.id, "❌ غير مسموح! هذا الأمر للأدمن فقط.")
        return
    
    admin_text = """
🔧 لوحة تحكم الأدمن

اختر العملية المطلوبة:
    """
    
    markup = types.InlineKeyboardMarkup(row_width=2)
    markup.add(
        types.InlineKeyboardButton("👨‍🏫 إنشاء حساب مدرس", callback_data="create_instructor"),
        types.InlineKeyboardButton("📊 الإحصائيات", callback_data="stats"),
        types.InlineKeyboardButton("🎓 إدارة التخصصات", callback_data="manage_specializations"),
        types.InlineKeyboardButton("🔑 أكواد التفعيل", callback_data="activation_codes"),
        types.InlineKeyboardButton("⚙️ الإعدادات", callback_data="settings"),
        types.InlineKeyboardButton("❓ المساعدة", callback_data="help")
    )
    
    bot.send_message(message.chat.id, admin_text, reply_markup=markup)

# ===== معالجات الرسائل النصية =====

@bot.message_handler(func=lambda message: message.text == "👨‍🏫 إنشاء حساب مدرس")
def create_instructor_button(message):
    """معالج زر إنشاء حساب مدرس"""
    instructor_handler.start_instructor_creation(message, BOT_OWNER_ID)

@bot.message_handler(func=lambda message: message.text == "📊 الإحصائيات")
def stats_button(message):
    """معالج زر الإحصائيات"""
    user_id = str(message.from_user.id)
    
    if user_id != BOT_OWNER_ID:
        bot.send_message(message.chat.id, "❌ غير مسموح! هذه الوظيفة للأدمن فقط.")
        return
    
    stats_text = """
📊 إحصائيات المنصة

👥 المستخدمين: 0
👨‍🏫 المدرسين: 0
👨‍🎓 الطلاب: 0
📚 الكورسات: 0
🎓 التخصصات: 3

⚠️ النظام قيد التطوير
    """
    
    bot.send_message(message.chat.id, stats_text)

@bot.message_handler(func=lambda message: message.text == "🎓 إنشاء رابط دعوة للطلاب")
def create_student_invitation_button(message):
    """معالج زر إنشاء رابط دعوة للطلاب"""
    user_id = str(message.from_user.id)
    student_invitation_handler.start_student_invitation_creation(message, user_id)

@bot.message_handler(func=lambda message: message.text == "📊 إحصائياتي")
def instructor_stats_button(message):
    """معالج زر إحصائيات المدرس"""
    user_id = str(message.from_user.id)

    # التحقق من أن المستخدم مدرس
    user = firebase_manager.get_user_by_telegram_id(user_id)
    if not user or user.get('role') != UserRole.INSTRUCTOR.value:
        bot.send_message(message.chat.id, "❌ هذه الوظيفة متاحة للمدرسين فقط.")
        return

    stats_text = """
📊 إحصائياتك كمدرس

👥 عدد الطلاب: 0
🔗 الروابط النشطة: 0
📚 الكورسات: 0

⚠️ النظام قيد التطوير
    """

    bot.send_message(message.chat.id, stats_text)

@bot.message_handler(func=lambda message: message.text == "❓ المساعدة")
def help_button(message):
    """معالج زر المساعدة"""
    help_command(message)

# ===== معالجات الاستعلامات المضمنة =====

@bot.callback_query_handler(func=lambda call: True)
def callback_handler(call):
    """معالج الاستعلامات المضمنة"""
    user_id = str(call.from_user.id)
    logger.info(f"استعلام مضمن: {call.data} من المستخدم: {user_id}")

    # محاولة معالجة الاستعلام بواسطة معالج إنشاء المدرسين
    if instructor_handler.handle_callback_query(call, BOT_OWNER_ID):
        return

    # محاولة معالجة الاستعلام بواسطة معالج دعوة الطلاب
    # التحقق من أن المستخدم مدرس قبل معالجة استعلامات دعوة الطلاب
    user = firebase_manager.get_user_by_telegram_id(user_id)
    logger.info(f"المستخدم: {user_id}, البيانات: {user}")
    if user and user.get('role') == UserRole.INSTRUCTOR.value:
        logger.info(f"المستخدم مدرس، محاولة معالجة استعلام دعوة الطلاب")
        if student_invitation_handler.handle_callback_query(call, user_id):
            logger.info(f"تم معالجة الاستعلام بواسطة معالج دعوة الطلاب")
            return
    else:
        logger.info(f"المستخدم ليس مدرساً أو غير موجود")

    if call.data == "create_instructor":
        if user_id != BOT_OWNER_ID:
            bot.answer_callback_query(call.id, "❌ غير مسموح!")
            return

        # بدء عملية إنشاء المدرس
        instructor_handler.start_instructor_creation(call.message, BOT_OWNER_ID)
        bot.answer_callback_query(call.id, "✅ بدء إنشاء حساب مدرس")

    elif call.data == "stats":
        if user_id != BOT_OWNER_ID:
            bot.answer_callback_query(call.id, "❌ غير مسموح!")
            return

        stats_button(call.message)

    elif call.data == "help":
        help_command(call.message)

    else:
        bot.answer_callback_query(call.id, "🔧 هذه الوظيفة قيد التطوير...")

# ===== معالج الرسائل العامة =====

@bot.message_handler(func=lambda message: True)
def default_handler(message):
    """معالج الرسائل الافتراضي"""
    user_id = str(message.from_user.id)

    # محاولة معالجة الرسالة بواسطة معالج إنشاء المدرسين
    if instructor_handler.handle_text_message(message, BOT_OWNER_ID):
        return

    # محاولة معالجة الرسالة بواسطة معالج دعوة الطلاب
    # التحقق من أن المستخدم مدرس قبل معالجة رسائل دعوة الطلاب
    user = firebase_manager.get_user_by_telegram_id(user_id)
    if user and user.get('role') == UserRole.INSTRUCTOR.value:
        if student_invitation_handler.handle_text_message(message, user_id):
            return

    if user_id == BOT_OWNER_ID:
        response = """
🤖 مرحباً بك يا مالك البوت!

استخدم الأزرار أدناه أو الأوامر التالية:
/admin - لوحة التحكم
/help - المساعدة
        """
    else:
        response = f"""
👋 مرحباً!

للمساعدة: /help
🌐 المنصة: {PLATFORM_URL}
        """

    bot.send_message(message.chat.id, response)

# ===== تشغيل البوت =====

def main():
    """تشغيل البوت الرئيسي"""
    logger.info("🤖 بدء تشغيل بوت منصة الكورسات التعليمية...")
    logger.info(f"🌐 رابط المنصة: {PLATFORM_URL}")
    
    try:
        # بدء البوت
        bot.infinity_polling(none_stop=True, interval=0, timeout=20)
    except Exception as e:
        logger.error(f"خطأ في تشغيل البوت: {e}")
    finally:
        logger.info("🛑 تم إيقاف البوت")

if __name__ == "__main__":
    main()
