<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مشغل الفيديو المحسن أمنياً</title>
    <link rel="stylesheet" href="static/css/advanced-video-player.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .header p {
            color: #666;
            font-size: 16px;
        }
        .video-container {
            max-width: 800px;
            margin: 0 auto 30px;
        }
        .security-info {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        .security-info h3 {
            color: #495057;
            margin-top: 0;
        }
        .security-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .security-feature {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #28a745;
        }
        .security-feature h4 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 14px;
        }
        .security-feature p {
            margin: 0;
            color: #666;
            font-size: 12px;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 20px;
        }
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        .test-btn:hover {
            background: #0056b3;
        }
        .test-btn.danger {
            background: #dc3545;
        }
        .test-btn.danger:hover {
            background: #c82333;
        }
        .security-log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            background: white;
            border-radius: 3px;
        }
        .log-entry.warning {
            border-left: 3px solid #ffc107;
        }
        .log-entry.error {
            border-left: 3px solid #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 اختبار مشغل الفيديو المحسن أمنياً</h1>
            <p>اختبار شامل لجميع إجراءات الأمان المطبقة في مشغل الفيديو المتقدم</p>
        </div>

        <div class="video-container">
            <div id="secureVideoPlayer"></div>
        </div>

        <div class="security-info">
            <h3>🛡️ الميزات الأمنية المطبقة</h3>
            <div class="security-features">
                <div class="security-feature">
                    <h4>منع أدوات المطور</h4>
                    <p>منع F12, Ctrl+Shift+I, Ctrl+Shift+J</p>
                </div>
                <div class="security-feature">
                    <h4>منع لقطات الشاشة</h4>
                    <p>منع Print Screen و Alt+Print Screen</p>
                </div>
                <div class="security-feature">
                    <h4>منع القائمة السياقية</h4>
                    <p>منع النقر بالزر الأيمن والقوائم</p>
                </div>
                <div class="security-feature">
                    <h4>منع النسخ واللصق</h4>
                    <p>منع Ctrl+C, Ctrl+V, Ctrl+A</p>
                </div>
                <div class="security-feature">
                    <h4>منع عرض المصدر</h4>
                    <p>منع Ctrl+U وعرض كود الصفحة</p>
                </div>
                <div class="security-feature">
                    <h4>حماية من البوتات</h4>
                    <p>التحقق من التفاعل البشري</p>
                </div>
                <div class="security-feature">
                    <h4>مراقبة الأنشطة المشبوهة</h4>
                    <p>تتبع وتسجيل محاولات التلاعب</p>
                </div>
                <div class="security-feature">
                    <h4>طبقات الحماية</h4>
                    <p>طبقات شفافة لمنع الوصول المباشر</p>
                </div>
                <div class="security-feature">
                    <h4>إيماءات الموبايل المتقدمة</h4>
                    <p>سحب أفقي/عمودي للتحكم، نقر مزدوج</p>
                </div>
                <div class="security-feature">
                    <h4>تحسين الاتجاه</h4>
                    <p>تكيف تلقائي مع الوضع الأفقي/العمودي</p>
                </div>
                <div class="security-feature">
                    <h4>وضع توفير الطاقة</h4>
                    <p>تحسين استهلاك البطارية والأداء</p>
                </div>
                <div class="security-feature">
                    <h4>Picture-in-Picture</h4>
                    <p>دعم وضع صورة في صورة للموبايل</p>
                </div>
                <div class="security-feature">
                    <h4>اختصارات لوحة المفاتيح المتقدمة</h4>
                    <p>30+ اختصار للتحكم الكامل في المشغل</p>
                </div>
                <div class="security-feature">
                    <h4>وضع المسرح والمشغل المصغر</h4>
                    <p>أوضاع عرض متقدمة للتجربة المثلى</p>
                </div>
                <div class="security-feature">
                    <h4>اختصارات الماوس المتقدمة</h4>
                    <p>نقر مزدوج، عجلة الماوس، نقر أوسط</p>
                </div>
            </div>

            <div class="test-buttons">
                <button class="test-btn" onclick="testKeyboardShortcuts()">اختبار اختصارات لوحة المفاتيح</button>
                <button class="test-btn" onclick="testAdvancedShortcuts()">اختبار الاختصارات المتقدمة</button>
                <button class="test-btn" onclick="testContextMenu()">اختبار القائمة السياقية</button>
                <button class="test-btn" onclick="testTextSelection()">اختبار تحديد النص</button>
                <button class="test-btn" onclick="testMobileGestures()">اختبار إيماءات الموبايل</button>
                <button class="test-btn" onclick="testOrientationChange()">اختبار تغيير الاتجاه</button>
                <button class="test-btn" onclick="testPowerSaveMode()">اختبار وضع توفير الطاقة</button>
                <button class="test-btn" onclick="testTheaterMode()">اختبار وضع المسرح</button>
                <button class="test-btn" onclick="testMiniPlayer()">اختبار المشغل المصغر</button>
                <button class="test-btn danger" onclick="simulateDevToolsAttempt()">محاكاة فتح أدوات المطور</button>
                <button class="test-btn danger" onclick="simulateScreenCapture()">محاكاة لقطة شاشة</button>
                <button class="test-btn" onclick="clearSecurityLog()">مسح سجل الأمان</button>
            </div>

            <div class="security-log" id="securityLog">
                <div class="log-entry">📋 سجل الأحداث الأمنية - جاهز للمراقبة</div>
            </div>
        </div>
    </div>

    <script src="static/js/advanced-video-player.js"></script>
    <script>
        // إنشاء مشغل الفيديو المحسن أمنياً
        let securePlayer;
        
        document.addEventListener('DOMContentLoaded', function() {
            // إنشاء مشغل فيديو تجريبي
            securePlayer = new AdvancedVideoPlayer('secureVideoPlayer', 'dQw4w9WgXcQ', {
                autoplay: false,
                showControls: false,
                enableKeyboard: true
            });

            // تخصيص دالة تسجيل الأحداث الأمنية لعرضها في الصفحة
            const originalLogSecurityEvent = securePlayer.logSecurityEvent.bind(securePlayer);
            securePlayer.logSecurityEvent = function(eventType) {
                // استدعاء الدالة الأصلية
                originalLogSecurityEvent(eventType);
                
                // إضافة الحدث لسجل الصفحة
                addToSecurityLog(eventType, 'warning');
            };

            // تخصيص دالة التحذيرات الأمنية
            const originalShowSecurityWarning = securePlayer.showSecurityWarning.bind(securePlayer);
            securePlayer.showSecurityWarning = function(message) {
                // استدعاء الدالة الأصلية
                originalShowSecurityWarning(message);
                
                // إضافة التحذير لسجل الصفحة
                addToSecurityLog(`تحذير: ${message}`, 'error');
            };

            // تهيئة المشغل
            securePlayer.init();
        });

        function addToSecurityLog(message, type = 'info') {
            const logContainer = document.getElementById('securityLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function testKeyboardShortcuts() {
            addToSecurityLog('⌨️ اختبار اختصارات لوحة المفاتيح المتقدمة...');
            addToSecurityLog('اختصارات التشغيل:');
            addToSecurityLog('• Space أو K - تشغيل/إيقاف');
            addToSecurityLog('• M - كتم/إلغاء كتم الصوت');
            addToSecurityLog('• F - شاشة كاملة');
            addToSecurityLog('');
            addToSecurityLog('اختصارات التنقل:');
            addToSecurityLog('• ← → - تقديم/تأخير 5 ثوان');
            addToSecurityLog('• Shift + ← → - تقديم/تأخير 10 ثوان');
            addToSecurityLog('• Ctrl + ← → - تقديم/تأخير 30 ثانية');
            addToSecurityLog('• J L - تأخير/تقديم 10 ثوان');
            addToSecurityLog('• Home/End - بداية/نهاية الفيديو');
            addToSecurityLog('• 0-9 - الانتقال لنسبة مئوية');
            addToSecurityLog('');
            addToSecurityLog('اختصارات الصوت والسرعة:');
            addToSecurityLog('• ↑ ↓ - زيادة/تقليل الصوت');
            addToSecurityLog('• , . - تقليل/زيادة السرعة');
            addToSecurityLog('• / - سرعة عادية');
            addToSecurityLog('');
            addToSecurityLog('اختصارات متقدمة:');
            addToSecurityLog('• C - إظهار/إخفاء عناصر التحكم');
            addToSecurityLog('• I - معلومات الفيديو');
            addToSecurityLog('• ? - قائمة الاختصارات');
            addToSecurityLog('• Ctrl+Shift+F - وضع المسرح');
            addToSecurityLog('• Ctrl+Shift+M - مشغل مصغر');
            addToSecurityLog('');
            addToSecurityLog('✨ انقر على مشغل الفيديو أولاً ثم جرب الاختصارات!');
        }

        function testContextMenu() {
            addToSecurityLog('🧪 اختبار القائمة السياقية...');
            addToSecurityLog('جرب النقر بالزر الأيمن على مشغل الفيديو');
        }

        function testTextSelection() {
            addToSecurityLog('🧪 اختبار تحديد النص...');
            addToSecurityLog('جرب تحديد النص في منطقة مشغل الفيديو');
        }

        function simulateDevToolsAttempt() {
            addToSecurityLog('⚠️ محاكاة محاولة فتح أدوات المطور', 'warning');
            if (securePlayer && securePlayer.logSecurityEvent) {
                securePlayer.logSecurityEvent('simulated_devtools_attempt');
            }
        }

        function simulateScreenCapture() {
            addToSecurityLog('⚠️ محاكاة محاولة أخذ لقطة شاشة', 'warning');
            if (securePlayer && securePlayer.logSecurityEvent) {
                securePlayer.logSecurityEvent('simulated_screen_capture');
            }
        }

        function clearSecurityLog() {
            const logContainer = document.getElementById('securityLog');
            logContainer.innerHTML = '<div class="log-entry">📋 تم مسح سجل الأحداث الأمنية</div>';
        }

        function testMobileGestures() {
            addToSecurityLog('📱 اختبار إيماءات الموبايل...');
            addToSecurityLog('جرب الإيماءات التالية على مشغل الفيديو:');
            addToSecurityLog('• السحب الأفقي: التقديم والتأخير');
            addToSecurityLog('• السحب العمودي (يمين): التحكم في الصوت');
            addToSecurityLog('• السحب العمودي (يسار): التحكم في السطوع');
            addToSecurityLog('• النقر المزدوج: الشاشة الكاملة');
            addToSecurityLog('• اللمس العادي: إظهار/إخفاء عناصر التحكم');
        }

        function testOrientationChange() {
            addToSecurityLog('🔄 اختبار تغيير الاتجاه...');
            addToSecurityLog('قم بتدوير الجهاز لاختبار التكيف مع الاتجاه');

            // محاكاة تغيير الاتجاه
            if (securePlayer && securePlayer.handleOrientationChange) {
                securePlayer.handleOrientationChange();
                addToSecurityLog('✅ تم تطبيق تحسينات الاتجاه');
            }
        }

        function testPowerSaveMode() {
            addToSecurityLog('⚡ اختبار وضع توفير الطاقة...');

            if (securePlayer && securePlayer.enterPowerSaveMode) {
                securePlayer.enterPowerSaveMode();
                addToSecurityLog('✅ تم تفعيل وضع توفير الطاقة');

                setTimeout(() => {
                    if (securePlayer.resumeNormalMode) {
                        securePlayer.resumeNormalMode();
                        addToSecurityLog('✅ تم العودة للوضع العادي');
                    }
                }, 3000);
            }
        }

        function testAdvancedShortcuts() {
            addToSecurityLog('🚀 اختبار الاختصارات المتقدمة...');
            addToSecurityLog('اختصارات الجودة والسرعة:');
            addToSecurityLog('• Ctrl+Shift+Q - تدوير جودة الفيديو');
            addToSecurityLog('• Ctrl + - تقليل السرعة');
            addToSecurityLog('• Ctrl + = زيادة السرعة');
            addToSecurityLog('• Ctrl + 0 - سرعة عادية');
            addToSecurityLog('');
            addToSecurityLog('اختصارات التنقل المتقدم:');
            addToSecurityLog('• Shift + N - الفصل التالي');
            addToSecurityLog('• Shift + P - الفصل السابق');
            addToSecurityLog('');
            addToSecurityLog('اختصارات الأوضاع:');
            addToSecurityLog('• Ctrl+Shift+F - وضع المسرح');
            addToSecurityLog('• Ctrl+Shift+M - المشغل المصغر');
            addToSecurityLog('');
            addToSecurityLog('اختصارات الماوس:');
            addToSecurityLog('• نقر مزدوج - شاشة كاملة');
            addToSecurityLog('• Ctrl + عجلة الماوس - تحكم في الصوت');
            addToSecurityLog('• نقر أوسط - تشغيل/إيقاف');
        }

        function testTheaterMode() {
            addToSecurityLog('🎭 اختبار وضع المسرح...');

            if (securePlayer && securePlayer.toggleTheaterMode) {
                securePlayer.toggleTheaterMode();
                addToSecurityLog('✅ تم تفعيل/إلغاء وضع المسرح');
                addToSecurityLog('💡 جرب Ctrl+Shift+F للتبديل بلوحة المفاتيح');
            }
        }

        function testMiniPlayer() {
            addToSecurityLog('📱 اختبار المشغل المصغر...');

            if (securePlayer && securePlayer.toggleMiniplayer) {
                securePlayer.toggleMiniplayer();
                addToSecurityLog('✅ تم تفعيل/إلغاء المشغل المصغر');
                addToSecurityLog('💡 جرب Ctrl+Shift+M للتبديل بلوحة المفاتيح');
            }
        }

        // مراقبة إضافية للاختبار
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
                addToSecurityLog('🚫 تم اكتشاف محاولة فتح أدوات المطور', 'error');
            }
        });

        document.addEventListener('contextmenu', function(e) {
            addToSecurityLog('🚫 تم اكتشاف محاولة فتح القائمة السياقية', 'warning');
        });
    </script>
</body>
</html>
