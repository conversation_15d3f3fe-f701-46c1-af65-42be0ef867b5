# 🚀 دليل التثبيت والإعداد
## Installation and Setup Guide

دليل شامل لتثبيت وإعداد منصة الكورسات التعليمية من الصفر حتى التشغيل الكامل.

---

## 📋 متطلبات النظام

### المتطلبات الأساسية
- **Python**: 3.8 أو أحدث
- **pip**: مدير حزم Python
- **Git**: لتحميل المشروع
- **Node.js**: 14+ (اختياري للتطوير)

### متطلبات الخادم (للإنتاج)
- **RAM**: 2GB كحد أدنى، 4GB مُوصى به
- **Storage**: 10GB مساحة فارغة
- **Network**: اتصال إنترنت مستقر
- **OS**: Linux (Ubuntu 18.04+) أو Windows 10+

---

## 📥 تحميل المشروع

### 1. استنساخ المستودع
```bash
# تحميل المشروع من GitHub
git clone <repository-url>
cd educational-courses-platform

# أو تحميل كملف ZIP وفك الضغط
wget <repository-zip-url>
unzip educational-courses-platform.zip
cd educational-courses-platform
```

### 2. فحص محتويات المشروع
```bash
# عرض هيكل المشروع
ls -la

# التأكد من وجود الملفات الأساسية
ls app.py config.py requirements.txt
```

---

## 🐍 إعداد بيئة Python

### 1. إنشاء بيئة افتراضية (مُوصى به)
```bash
# إنشاء بيئة افتراضية
python -m venv venv

# تفعيل البيئة الافتراضية
# على Windows:
venv\Scripts\activate
# على Linux/Mac:
source venv/bin/activate
```

### 2. تحديث pip
```bash
# تحديث pip لآخر إصدار
python -m pip install --upgrade pip
```

### 3. تثبيت المتطلبات
```bash
# تثبيت جميع المكتبات المطلوبة
pip install -r requirements.txt

# التحقق من التثبيت
pip list
```

---

## 🔧 إعداد متغيرات البيئة

### 1. إنشاء ملف .env
```bash
# نسخ ملف المثال
cp .env.example .env

# أو إنشاء ملف جديد
touch .env
```

### 2. تعديل ملف .env
```env
# إعدادات Flask الأساسية
FLASK_SECRET_KEY=your_very_secure_secret_key_here
FLASK_ENV=development
FLASK_DEBUG=True
FLASK_PORT=5000

# إعدادات Firebase
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour_Private_Key_Here\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_DATABASE_URL=https://your-project-default-rtdb.firebaseio.com/

# إعدادات بوت التليجرام
TELEGRAM_BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz
BOT_OWNER_TELEGRAM_ID=123456789

# إعدادات المنصة
PLATFORM_URL=http://localhost:5000
PLATFORM_NAME=منصة الكورسات التعليمية
ADMIN_EMAIL=<EMAIL>

# إعدادات JWT
JWT_SECRET_KEY=your_jwt_secret_key
JWT_EXPIRATION_HOURS=24

# إعدادات التخزين المؤقت
CACHE_TYPE=simple
CACHE_DEFAULT_TIMEOUT=300
```

---

## 🔥 إعداد Firebase

### 1. إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انقر على "إنشاء مشروع" أو "Create Project"
3. اتبع الخطوات لإنشاء المشروع

### 2. تفعيل الخدمات المطلوبة
```bash
# في Firebase Console:
# 1. Authentication > Sign-in method > Email/Password (تفعيل)
# 2. Realtime Database > إنشاء قاعدة بيانات
# 3. Storage > البدء
```

### 3. الحصول على مفاتيح الخدمة
1. اذهب إلى Project Settings > Service Accounts
2. انقر على "Generate new private key"
3. احفظ الملف وانسخ البيانات إلى .env

### 4. إعداد قواعد الأمان
```json
// Realtime Database Rules
{
  "rules": {
    ".read": "auth != null",
    ".write": "auth != null"
  }
}

// Storage Rules
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

---

## 🤖 إعداد بوت التليجرام

### 1. إنشاء البوت
1. ابحث عن @BotFather في تليجرام
2. أرسل `/newbot`
3. اتبع التعليمات لإنشاء البوت
4. احفظ الـ Token في ملف .env

### 2. الحصول على Telegram ID
```bash
# أرسل رسالة للبوت ثم استخدم:
curl https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates

# أو استخدم @userinfobot للحصول على ID الخاص بك
```

### 3. إعداد Webhook (للإنتاج)
```bash
# تعيين webhook للبوت
curl -X POST "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/setWebhook" \
     -d "url=https://yourdomain.com/webhook"
```

---

## 🗄️ إعداد قاعدة البيانات

### 1. تهيئة هيكل قاعدة البيانات
```bash
# تشغيل سكريبت التهيئة
python check_database.py

# أو تشغيل التطبيق لأول مرة (سيقوم بالتهيئة تلقائياً)
python app.py
```

### 2. إنشاء حساب الأدمن الأول
```bash
# استخدام سكريبت إنشاء الأدمن
python create_admin.py

# أو استخدام السكريبت المبسط
python create_admin_simple.py
```

### 3. إضافة بيانات تجريبية (اختياري)
```bash
# إنشاء مدرس تجريبي
python create_test_instructor.py

# إنشاء مستخدم تجريبي
python create_test_user.py
```

---

## 🏃‍♂️ تشغيل النظام

### 1. تشغيل المنصة الرئيسية
```bash
# الطريقة الأولى: تشغيل مباشر
python app.py

# الطريقة الثانية: استخدام سكريبت التشغيل
python run_server.py

# الطريقة الثالثة: استخدام Flask CLI
export FLASK_APP=app.py
flask run
```

### 2. تشغيل بوت التليجرام
```bash
# في terminal منفصل
cd bot
python main.py

# أو من المجلد الرئيسي
python bot/main.py
```

### 3. التحقق من التشغيل
```bash
# فحص المنصة
curl http://localhost:5000

# فحص API
curl http://localhost:5000/api/health

# فحص البوت (في تليجرام)
# أرسل /start للبوت
```

---

## 🧪 تشغيل الاختبارات

### 1. الاختبارات الأساسية
```bash
# تشغيل جميع الاختبارات
python run_tests.py

# تشغيل اختبارات محددة
python -m unittest tests.test_simple -v
python -m unittest tests.test_performance -v
```

### 2. فحص النظام
```bash
# فحص قاعدة البيانات
python check_database.py

# فحص المستخدمين
python check_user.py

# فحص Firebase
python test_firebase.py
```

---

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في Firebase
```bash
# التحقق من صحة الإعدادات
python test_firebase.py

# فحص ملف .env
cat .env | grep FIREBASE
```

#### 2. مشاكل البوت
```bash
# فحص سجل البوت
tail -f bot.log

# التحقق من Token
curl https://api.telegram.org/bot<TOKEN>/getMe
```

#### 3. مشاكل المكتبات
```bash
# إعادة تثبيت المتطلبات
pip uninstall -r requirements.txt -y
pip install -r requirements.txt

# فحص التعارضات
pip check
```

#### 4. مشاكل الأداء
```bash
# تشغيل اختبارات الأداء
python -m unittest tests.test_performance -v

# مراقبة استخدام الموارد
top -p $(pgrep -f "python app.py")
```

---

## 📊 التحقق من التثبيت

### قائمة فحص التثبيت
- [ ] Python 3.8+ مثبت
- [ ] جميع المكتبات مثبتة بنجاح
- [ ] ملف .env معد بشكل صحيح
- [ ] Firebase متصل ويعمل
- [ ] بوت التليجرام يستجيب
- [ ] المنصة تعمل على localhost:5000
- [ ] قاعدة البيانات مهيأة
- [ ] حساب الأدمن منشأ
- [ ] الاختبارات تمر بنجاح

### أوامر التحقق السريع
```bash
# فحص شامل للنظام
python -c "
import sys
print(f'Python: {sys.version}')
import flask
print(f'Flask: {flask.__version__}')
import firebase_admin
print('Firebase: OK')
print('System Ready!')
"
```

---

## 🎯 الخطوات التالية

بعد التثبيت الناجح:

1. **راجع [دليل الإعدادات](configuration-guide.md)** للتخصيص المتقدم
2. **اقرأ [أدلة المستخدمين](user-guides/)** لفهم كيفية الاستخدام
3. **راجع [وثائق API](api/api-documentation.md)** للتطوير
4. **اطلع على [دليل النشر](deployment/deployment-guide.md)** للإنتاج

---

## 📞 الحصول على المساعدة

إذا واجهت مشاكل في التثبيت:

- **راجع قسم استكشاف الأخطاء** أعلاه
- **فحص ملفات السجل** في `logs/` و `bot.log`
- **تشغيل الاختبارات** للتأكد من سلامة النظام
- **مراجعة الوثائق** للحصول على تفاصيل إضافية

---

**آخر تحديث:** 2025-07-03  
**الإصدار:** 1.0.0  
**حالة الدليل:** مكتمل ✅
