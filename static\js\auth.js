/**
 * نظام إدارة المصادقة والجلسات
 * Authentication and Session Management System
 */

class AuthManager {
    constructor() {
        this.token = null;
        this.user = null;
        this.refreshTimer = null;
        this.init();
    }

    /**
     * تهيئة نظام المصادقة
     */
    init() {
        // تحميل البيانات المحفوظة
        this.loadStoredData();
        
        // التحقق من صحة الجلسة
        this.validateSession();
        
        // إعداد تجديد التوكين التلقائي
        this.setupAutoRefresh();
        
        // إعداد معالجات الأحداث
        this.setupEventListeners();
    }

    /**
     * تحميل البيانات المحفوظة من localStorage
     */
    loadStoredData() {
        try {
            this.token = localStorage.getItem('auth_token');
            const userData = localStorage.getItem('user_data');
            if (userData) {
                this.user = JSON.parse(userData);
            }
        } catch (error) {
            console.error('خطأ في تحميل البيانات المحفوظة:', error);
            this.clearStoredData();
        }
    }

    /**
     * حفظ البيانات في localStorage
     */
    saveStoredData() {
        try {
            if (this.token) {
                localStorage.setItem('auth_token', this.token);
            }
            if (this.user) {
                localStorage.setItem('user_data', JSON.stringify(this.user));
            }
        } catch (error) {
            console.error('خطأ في حفظ البيانات:', error);
        }
    }

    /**
     * مسح البيانات المحفوظة
     */
    clearStoredData() {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
        this.token = null;
        this.user = null;
    }

    /**
     * التحقق من صحة الجلسة الحالية
     */
    async validateSession() {
        if (!this.token) {
            return false;
        }

        try {
            const response = await this.makeAuthenticatedRequest('/api/auth/me');
            
            if (response.success) {
                this.user = response.user;
                this.saveStoredData();
                this.updateUI();
                return true;
            } else {
                this.clearStoredData();
                return false;
            }
        } catch (error) {
            console.error('خطأ في التحقق من الجلسة:', error);
            this.clearStoredData();
            return false;
        }
    }

    /**
     * تسجيل الدخول
     */
    async login(email, password, remember = false) {
        try {
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email: email,
                    password: password,
                    remember: remember
                })
            });

            const data = await response.json();

            if (data.success) {
                this.token = data.token;
                this.user = data.user;
                this.saveStoredData();
                this.updateUI();
                this.setupAutoRefresh();
                return { success: true, message: data.message };
            } else {
                return { success: false, message: data.message };
            }
        } catch (error) {
            console.error('خطأ في تسجيل الدخول:', error);
            return { success: false, message: 'حدث خطأ في الاتصال' };
        }
    }

    /**
     * تسجيل الخروج
     */
    async logout() {
        try {
            if (this.token) {
                await this.makeAuthenticatedRequest('/api/auth/logout', 'POST');
            }
        } catch (error) {
            console.error('خطأ في تسجيل الخروج:', error);
        } finally {
            this.clearStoredData();
            this.clearAutoRefresh();
            this.updateUI();
            
            // إعادة التوجيه لصفحة تسجيل الدخول
            if (window.location.pathname !== '/login') {
                window.location.href = '/login';
            }
        }
    }

    /**
     * تجديد التوكين
     */
    async refreshToken() {
        if (!this.token) {
            return false;
        }

        try {
            const response = await this.makeAuthenticatedRequest('/api/auth/refresh', 'POST');
            
            if (response.success) {
                this.token = response.token;
                this.saveStoredData();
                return true;
            } else {
                this.clearStoredData();
                return false;
            }
        } catch (error) {
            console.error('خطأ في تجديد التوكين:', error);
            this.clearStoredData();
            return false;
        }
    }

    /**
     * إجراء طلب مصادق عليه
     */
    async makeAuthenticatedRequest(url, method = 'GET', data = null) {
        const headers = {
            'Content-Type': 'application/json',
        };

        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }

        const options = {
            method: method,
            headers: headers,
        };

        if (data && method !== 'GET') {
            options.body = JSON.stringify(data);
        }

        const response = await fetch(url, options);
        return await response.json();
    }

    /**
     * إعداد تجديد التوكين التلقائي
     */
    setupAutoRefresh() {
        this.clearAutoRefresh();
        
        if (this.token) {
            // تجديد التوكين كل 23 ساعة (قبل انتهاء الصلاحية بساعة)
            this.refreshTimer = setInterval(() => {
                this.refreshToken();
            }, 23 * 60 * 60 * 1000);
        }
    }

    /**
     * إلغاء تجديد التوكين التلقائي
     */
    clearAutoRefresh() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
    }

    /**
     * إعداد معالجات الأحداث
     */
    setupEventListeners() {
        // معالجة أزرار تسجيل الخروج
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('logout-btn') || e.target.closest('.logout-btn')) {
                e.preventDefault();
                this.logout();
            }
        });

        // معالجة انتهاء الجلسة عند إغلاق النافذة
        window.addEventListener('beforeunload', () => {
            this.clearAutoRefresh();
        });
    }

    /**
     * تحديث واجهة المستخدم
     */
    updateUI() {
        // إخفاء/إظهار عناصر حسب حالة تسجيل الدخول
        const authElements = document.querySelectorAll('[data-auth]');
        const guestElements = document.querySelectorAll('[data-guest]');
        const userNameElements = document.querySelectorAll('[data-user-name]');
        const userRoleElements = document.querySelectorAll('[data-user-role]');
        const studentOnlyElements = document.querySelectorAll('.student-only');

        if (this.isAuthenticated()) {
            authElements.forEach(el => el.style.display = '');
            guestElements.forEach(el => el.style.display = 'none');
            userNameElements.forEach(el => el.textContent = this.user.full_name || this.user.first_name);
            userRoleElements.forEach(el => el.textContent = this.getRoleDisplayName(this.user.role));

            // إظهار عناصر خاصة بالطلاب فقط
            if (this.hasRole('student')) {
                studentOnlyElements.forEach(el => el.style.display = '');
            } else {
                studentOnlyElements.forEach(el => el.style.display = 'none');
            }
        } else {
            authElements.forEach(el => el.style.display = 'none');
            guestElements.forEach(el => el.style.display = '');
            studentOnlyElements.forEach(el => el.style.display = 'none');
        }
    }

    /**
     * التحقق من تسجيل الدخول
     */
    isAuthenticated() {
        return this.token && this.user;
    }

    /**
     * التحقق من دور المستخدم
     */
    hasRole(role) {
        return this.isAuthenticated() && this.user.role === role;
    }

    /**
     * التحقق من وجود أي من الأدوار المحددة
     */
    hasAnyRole(roles) {
        return this.isAuthenticated() && roles.includes(this.user.role);
    }

    /**
     * الحصول على اسم الدور للعرض
     */
    getRoleDisplayName(role) {
        const roleNames = {
            'admin': 'مدير النظام',
            'instructor': 'مدرس',
            'student': 'طالب'
        };
        return roleNames[role] || role;
    }

    /**
     * الحصول على بيانات المستخدم الحالي
     */
    getCurrentUser() {
        return this.user;
    }

    /**
     * الحصول على التوكين الحالي
     */
    getToken() {
        return this.token;
    }
}

// إنشاء مثيل مشترك
const authManager = new AuthManager();

// تصدير للاستخدام العام
window.authManager = authManager;
