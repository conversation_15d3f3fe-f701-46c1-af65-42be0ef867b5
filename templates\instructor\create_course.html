{% extends "base.html" %}

{% block title %}إنشاء كورس جديد - {{ platform_name }}{% endblock %}

{% block extra_css %}
<style>
    .course-form-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }
    
    .form-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 20px 20px 0 0;
        padding: 2rem;
        text-align: center;
    }
    
    .form-body {
        padding: 2rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        display: block;
    }
    
    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        background: white;
    }
    
    .btn-create {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        width: 100%;
    }
    
    .btn-create:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        color: white;
    }
    
    .btn-cancel {
        background: #6c757d;
        border: none;
        border-radius: 10px;
        padding: 0.75rem 2rem;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        width: 100%;
        margin-top: 0.5rem;
    }
    
    .btn-cancel:hover {
        background: #5a6268;
        color: white;
    }
    
    .permissions-info {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid #667eea;
    }
    
    .permissions-title {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }
    
    .permissions-list {
        margin: 0;
        padding-left: 1rem;
        color: #6c757d;
    }
    
    .stage-checkbox {
        margin-right: 0.5rem;
    }
    
    .stage-label {
        margin-right: 1rem;
        font-weight: 500;
    }
    
    .error-message {
        background: #f8d7da;
        color: #721c24;
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 1rem;
        border-left: 4px solid #dc3545;
    }
    
    .loading-spinner {
        display: none;
        text-align: center;
        padding: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="course-form-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="form-card">
                    <div class="form-header">
                        <h2><i class="fas fa-plus-circle me-2"></i>إنشاء كورس جديد</h2>
                        <p class="mb-0">أنشئ كورساً تعليمياً جديداً ضمن صلاحياتك</p>
                    </div>
                    
                    <div class="form-body">
                        <!-- معلومات الصلاحيات -->
                        <div class="permissions-info" id="permissionsInfo">
                            <div class="permissions-title">
                                <i class="fas fa-info-circle me-2"></i>صلاحياتك الحالية
                            </div>
                            <div id="permissionsContent">
                                <div class="loading-spinner">
                                    <i class="fas fa-spinner fa-spin"></i> جاري تحميل الصلاحيات...
                                </div>
                            </div>
                        </div>
                        
                        <!-- رسائل الخطأ -->
                        <div id="errorContainer"></div>
                        
                        <!-- نموذج إنشاء الكورس -->
                        <form id="createCourseForm">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="courseTitle" class="form-label">
                                            <i class="fas fa-heading me-2"></i>عنوان الكورس
                                        </label>
                                        <input type="text" class="form-control" id="courseTitle" name="title" 
                                               placeholder="أدخل عنوان الكورس" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="courseDescription" class="form-label">
                                            <i class="fas fa-align-left me-2"></i>وصف الكورس
                                        </label>
                                        <textarea class="form-control" id="courseDescription" name="description" 
                                                  rows="4" placeholder="أدخل وصفاً مفصلاً للكورس"></textarea>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="courseType" class="form-label">
                                            <i class="fas fa-layer-group me-2"></i>نوع الكورس
                                        </label>
                                        <select class="form-select" id="courseType" name="type" required>
                                            <option value="">اختر نوع الكورس</option>
                                            <option value="specialization">كورس تخصص</option>
                                            <option value="general" style="display: none;">كورس عام</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="courseStage" class="form-label">
                                            <i class="fas fa-graduation-cap me-2"></i>المرحلة الدراسية
                                        </label>
                                        <select class="form-select" id="courseStage" name="stage" required>
                                            <option value="">اختر المرحلة</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group" id="specializationGroup">
                                        <label for="courseSpecialization" class="form-label">
                                            <i class="fas fa-stethoscope me-2"></i>التخصص
                                        </label>
                                        <select class="form-select" id="courseSpecialization" name="specialization_id">
                                            <option value="">اختر التخصص</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="courseStatus" class="form-label">
                                            <i class="fas fa-toggle-on me-2"></i>حالة الكورس
                                        </label>
                                        <select class="form-select" id="courseStatus" name="status" required>
                                            <option value="draft">مسودة</option>
                                            <option value="published">منشور</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <button type="submit" class="btn btn-create">
                                        <i class="fas fa-plus-circle me-2"></i>إنشاء الكورس
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <a href="{{ url_for('instructor_dashboard') }}" class="btn btn-cancel">
                                        <i class="fas fa-times me-2"></i>إلغاء
                                    </a>
                                </div>
                            </div>
                        </form>
                        
                        <!-- مؤشر التحميل -->
                        <div class="loading-spinner" id="loadingSpinner">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                            <p class="mt-2">جاري إنشاء الكورس...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/instructor-create-course.js') }}"></script>
{% endblock %}
