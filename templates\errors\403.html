{% extends "base.html" %}

{% block title %}غير مسموح - {{ platform_name }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 text-center py-5">
            <div class="error-page">
                <div class="error-number mb-4">
                    <h1 class="display-1 fw-bold text-warning">403</h1>
                </div>
                
                <div class="error-icon mb-4">
                    <i class="fas fa-lock fa-5x text-danger"></i>
                </div>
                
                <h2 class="fw-bold mb-3">غير مسموح بالوصول</h2>
                <p class="text-muted mb-4">
                    عذراً، ليس لديك صلاحية للوصول إلى هذه الصفحة أو الموقع.
                </p>
                
                <div class="error-actions">
                    <a href="{{ url_for('index') }}" class="btn btn-primary me-3">
                        <i class="fas fa-home me-2"></i>العودة للرئيسية
                    </a>
                    <a href="{{ url_for('login') }}" class="btn btn-outline-primary">
                        <i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول
                    </a>
                </div>
                
                <div class="mt-5">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع المدرس أو الإدارة
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    animation: fadeInUp 0.6s ease-out;
}

.error-number h1 {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.error-icon i {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
{% endblock %}
