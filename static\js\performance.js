/**
 * تحسينات الأداء والسرعة
 * Performance and Speed Optimizations
 */

class PerformanceOptimizer {
    constructor() {
        this.init();
    }

    init() {
        this.setupLazyLoading();
        this.setupImageOptimization();
        this.setupScrollOptimization();
        this.setupFormOptimization();
        this.setupTableOptimization();
        this.setupModalOptimization();
        this.setupNavigationOptimization();
        this.setupCacheOptimization();
    }

    // تحسين Lazy Loading
    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const lazyImageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const lazyImage = entry.target;
                        lazyImage.src = lazyImage.dataset.src;
                        lazyImage.classList.remove('lazy');
                        lazyImage.classList.add('loaded');
                        lazyImageObserver.unobserve(lazyImage);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            document.querySelectorAll('img[data-src]').forEach(lazyImage => {
                lazyImageObserver.observe(lazyImage);
            });

            // Lazy loading للمحتوى
            const lazyContentObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const lazyContent = entry.target;
                        if (lazyContent.dataset.src) {
                            this.loadContent(lazyContent);
                            lazyContentObserver.unobserve(lazyContent);
                        }
                    }
                });
            });

            document.querySelectorAll('[data-lazy-content]').forEach(content => {
                lazyContentObserver.observe(content);
            });
        }
    }

    // تحسين الصور
    setupImageOptimization() {
        // تحسين جودة الصور حسب حجم الشاشة
        const images = document.querySelectorAll('img[data-sizes]');
        images.forEach(img => {
            const sizes = JSON.parse(img.dataset.sizes);
            const screenWidth = window.innerWidth;
            
            let bestSize = sizes[0];
            for (const size of sizes) {
                if (screenWidth >= size.minWidth) {
                    bestSize = size;
                }
            }
            
            if (img.src !== bestSize.src) {
                img.src = bestSize.src;
            }
        });

        // تحسين تحميل الصور
        document.addEventListener('DOMContentLoaded', () => {
            const images = document.querySelectorAll('img');
            images.forEach(img => {
                if (img.complete) {
                    img.classList.add('loaded');
                } else {
                    img.addEventListener('load', () => {
                        img.classList.add('loaded');
                    });
                }
            });
        });
    }

    // تحسين التمرير
    setupScrollOptimization() {
        let ticking = false;

        const updateScrollPosition = () => {
            // تحديث موقع التمرير بكفاءة
            const scrollTop = window.pageYOffset;
            document.body.style.setProperty('--scroll-y', scrollTop + 'px');
            ticking = false;
        };

        const requestTick = () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollPosition);
                ticking = true;
            }
        };

        window.addEventListener('scroll', requestTick, { passive: true });

        // تحسين التمرير السلس
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    // تحسين النماذج
    setupFormOptimization() {
        // تحسين التحقق من صحة النماذج
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            const inputs = form.querySelectorAll('input, select, textarea');
            
            inputs.forEach(input => {
                // تأخير التحقق لتحسين الأداء
                let validationTimeout;
                
                input.addEventListener('input', () => {
                    clearTimeout(validationTimeout);
                    validationTimeout = setTimeout(() => {
                        this.validateField(input);
                    }, 300);
                });

                // تحسين الـ autocomplete
                if (input.type === 'text' || input.type === 'email') {
                    input.addEventListener('focus', () => {
                        input.setAttribute('autocomplete', 'on');
                    });
                }
            });
        });
    }

    // تحسين الجداول
    setupTableOptimization() {
        const tables = document.querySelectorAll('table.table-performance');
        
        tables.forEach(table => {
            // تحسين عرض الجداول الكبيرة
            if (table.rows.length > 100) {
                this.setupVirtualScrolling(table);
            }

            // تحسين الفرز والتصفية
            const headers = table.querySelectorAll('th[data-sortable]');
            headers.forEach(header => {
                header.addEventListener('click', () => {
                    this.sortTable(table, header);
                });
            });
        });
    }

    // تحسين المودالات
    setupModalOptimization() {
        const modals = document.querySelectorAll('.modal');
        
        modals.forEach(modal => {
            // تحميل محتوى المودال عند الحاجة فقط
            modal.addEventListener('show.bs.modal', () => {
                if (modal.dataset.lazyContent) {
                    this.loadModalContent(modal);
                }
            });

            // تنظيف المودال عند الإغلاق
            modal.addEventListener('hidden.bs.modal', () => {
                if (modal.dataset.cleanup === 'true') {
                    this.cleanupModal(modal);
                }
            });
        });
    }

    // تحسين التنقل
    setupNavigationOptimization() {
        // تحسين تحميل الصفحات
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href^="/"]');
            if (link && !link.hasAttribute('download')) {
                // إضافة مؤشر التحميل
                link.classList.add('loading');
                
                // إزالة مؤشر التحميل بعد فترة
                setTimeout(() => {
                    link.classList.remove('loading');
                }, 3000);
            }
        });

        // تحسين القوائم المنسدلة
        const dropdowns = document.querySelectorAll('.dropdown-toggle');
        dropdowns.forEach(dropdown => {
            dropdown.addEventListener('show.bs.dropdown', () => {
                const menu = dropdown.nextElementSibling;
                if (menu && menu.dataset.lazyContent) {
                    this.loadDropdownContent(menu);
                }
            });
        });
    }

    // تحسين التخزين المؤقت
    setupCacheOptimization() {
        // تخزين مؤقت للبيانات المتكررة
        this.cache = new Map();
        
        // تنظيف التخزين المؤقت كل 5 دقائق
        setInterval(() => {
            this.cleanupCache();
        }, 5 * 60 * 1000);
    }

    // دوال مساعدة
    loadContent(element) {
        const url = element.dataset.src;
        if (this.cache.has(url)) {
            element.innerHTML = this.cache.get(url);
            return;
        }

        fetch(url)
            .then(response => response.text())
            .then(html => {
                element.innerHTML = html;
                this.cache.set(url, html);
            })
            .catch(error => {
                console.error('خطأ في تحميل المحتوى:', error);
            });
    }

    validateField(field) {
        // تحقق سريع من صحة الحقل
        if (field.checkValidity()) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        } else {
            field.classList.remove('is-valid');
            field.classList.add('is-invalid');
        }
    }

    setupVirtualScrolling(table) {
        // تطبيق التمرير الافتراضي للجداول الكبيرة
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const rowHeight = 50; // ارتفاع الصف
        const visibleRows = Math.ceil(window.innerHeight / rowHeight);
        
        let startIndex = 0;
        let endIndex = visibleRows;

        const updateVisibleRows = () => {
            rows.forEach((row, index) => {
                if (index >= startIndex && index <= endIndex) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        };

        table.addEventListener('scroll', () => {
            const scrollTop = table.scrollTop;
            startIndex = Math.floor(scrollTop / rowHeight);
            endIndex = startIndex + visibleRows;
            updateVisibleRows();
        });

        updateVisibleRows();
    }

    sortTable(table, header) {
        // فرز الجدول بكفاءة
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        const isAscending = header.classList.contains('sort-asc');

        rows.sort((a, b) => {
            const aValue = a.cells[columnIndex].textContent.trim();
            const bValue = b.cells[columnIndex].textContent.trim();
            
            if (isAscending) {
                return bValue.localeCompare(aValue);
            } else {
                return aValue.localeCompare(bValue);
            }
        });

        // تحديث الجدول
        rows.forEach(row => tbody.appendChild(row));
        
        // تحديث أيقونة الفرز
        header.classList.toggle('sort-asc');
        header.classList.toggle('sort-desc');
    }

    loadModalContent(modal) {
        const url = modal.dataset.lazyContent;
        const body = modal.querySelector('.modal-body');
        
        if (this.cache.has(url)) {
            body.innerHTML = this.cache.get(url);
            return;
        }

        body.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div></div>';
        
        fetch(url)
            .then(response => response.text())
            .then(html => {
                body.innerHTML = html;
                this.cache.set(url, html);
            })
            .catch(error => {
                body.innerHTML = '<div class="alert alert-danger">خطأ في تحميل المحتوى</div>';
                console.error('خطأ في تحميل محتوى المودال:', error);
            });
    }

    cleanupModal(modal) {
        const body = modal.querySelector('.modal-body');
        body.innerHTML = '';
    }

    loadDropdownContent(menu) {
        const url = menu.dataset.lazyContent;
        
        if (this.cache.has(url)) {
            menu.innerHTML = this.cache.get(url);
            return;
        }

        fetch(url)
            .then(response => response.text())
            .then(html => {
                menu.innerHTML = html;
                this.cache.set(url, html);
            })
            .catch(error => {
                console.error('خطأ في تحميل محتوى القائمة:', error);
            });
    }

    cleanupCache() {
        // تنظيف التخزين المؤقت القديم
        if (this.cache.size > 50) {
            const entries = Array.from(this.cache.entries());
            const toDelete = entries.slice(0, 25);
            toDelete.forEach(([key]) => {
                this.cache.delete(key);
            });
        }
    }
}

// تهيئة محسن الأداء عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.performanceOptimizer = new PerformanceOptimizer();
});

// تصدير الكلاس للاستخدام في ملفات أخرى
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceOptimizer;
}
