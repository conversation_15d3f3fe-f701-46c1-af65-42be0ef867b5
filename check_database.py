#!/usr/bin/env python3
"""
فحص قاعدة البيانات
"""

import firebase_admin
from firebase_admin import credentials, db

def check_database():
    """فحص محتويات قاعدة البيانات"""
    
    try:
        # إعداد Firebase Admin SDK
        if not firebase_admin._apps:
            cred_dict = *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            
            cred = credentials.Certificate(cred_dict)
            firebase_admin.initialize_app(cred, {
                'databaseURL': 'https://cors-dd880-default-rtdb.firebaseio.com/'
            })
        
        # الحصول على مرجع قاعدة البيانات
        ref = db.reference('users')
        
        # جلب جميع المستخدمين
        users = ref.get()
        
        if not users:
            print("❌ لا توجد بيانات في قاعدة البيانات")
            return
        
        print(f"📊 إجمالي المستخدمين: {len(users)}")
        print("=" * 50)
        
        # تصنيف المستخدمين حسب الدور
        admins = []
        instructors = []
        students = []
        
        for user_id, user_data in users.items():
            role = user_data.get('role', 'غير محدد')
            
            if role == 'admin':
                admins.append((user_id, user_data))
            elif role == 'instructor':
                instructors.append((user_id, user_data))
            elif role == 'student':
                students.append((user_id, user_data))
        
        # عرض الأدمن
        print(f"👨‍💼 الأدمن ({len(admins)}):")
        for user_id, user_data in admins:
            print(f"  - {user_data.get('full_name', 'بدون اسم')} ({user_data.get('email', 'بدون إيميل')})")
            print(f"    ID: {user_id}")
        
        print()
        
        # عرض المدرسين
        print(f"👨‍🏫 المدرسين ({len(instructors)}):")
        for user_id, user_data in instructors:
            print(f"  - {user_data.get('full_name', 'بدون اسم')} ({user_data.get('email', 'بدون إيميل')})")
            print(f"    ID: {user_id}")
        
        print()
        
        # عرض الطلاب
        print(f"👨‍🎓 الطلاب ({len(students)}):")
        for user_id, user_data in students:
            instructor_id = user_data.get('assigned_instructor', 'غير مرتبط')
            print(f"  - {user_data.get('full_name', 'بدون اسم')} ({user_data.get('email', 'بدون إيميل')})")
            print(f"    ID: {user_id}")
            print(f"    المدرس المسؤول: {instructor_id}")
        
        print()
        print("=" * 50)
        
        # اختبار استعلام الطلاب
        print("🔍 اختبار استعلام الطلاب...")
        students_query = ref.order_by_child('role').equal_to('student').get()
        
        if students_query:
            print(f"✅ تم العثور على {len(students_query)} طالب عبر الاستعلام")
            for user_id, user_data in students_query.items():
                print(f"  - {user_data.get('full_name', 'بدون اسم')}")
        else:
            print("❌ لم يتم العثور على طلاب عبر الاستعلام")
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_database()
