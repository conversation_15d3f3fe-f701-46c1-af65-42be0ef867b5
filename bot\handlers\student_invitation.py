"""
معالجات دعوة الطلاب
Student Invitation Handlers

هذا الملف يحتوي على معالجات البوت الخاصة بإنشاء روابط دعوة الطلاب
ويتضمن وظائف للمدرسين لإنشاء روابط دعوة موحدة لجذب الطلاب لتخصصهم
"""

import logging
import sys
import os
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timezone

# إضافة المجلد الرئيسي للمسار
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# إعداد نظام التسجيل
logger = logging.getLogger(__name__)

from utils.firebase_utils import get_firebase_manager
from models.database_models import UserRole
from bot.utils.invitation_utils import get_bot_invitation_manager

try:
    from telebot import types
except ImportError:
    logger.error("مكتبة telebot غير مثبتة")
    sys.exit(1)

# حالات المحادثة
CONVERSATION_STATES = {
    'WAITING_FOR_LINK_DURATION': 'waiting_for_link_duration',
    'WAITING_FOR_CONFIRMATION': 'waiting_for_confirmation'
}

# تخزين مؤقت لحالات المحادثة
instructor_conversations = {}

class StudentInvitationHandler:
    """معالج دعوة الطلاب"""
    
    def __init__(self, bot):
        """تهيئة المعالج"""
        self.bot = bot
        self.firebase_manager = get_firebase_manager()
        self.invitation_manager = get_bot_invitation_manager()
    
    def start_student_invitation_creation(self, message, instructor_telegram_id: str):
        """بدء عملية إنشاء رابط دعوة للطلاب"""
        try:
            # التحقق من أن المستخدم مدرس مسجل
            instructor = self.firebase_manager.get_user_by_telegram_id(instructor_telegram_id)
            if not instructor:
                self.bot.send_message(
                    message.chat.id, 
                    "❌ لم يتم العثور على حسابك في النظام. تأكد من تسجيلك كمدرس أولاً."
                )
                return False
            
            if instructor.get('role') != UserRole.INSTRUCTOR.value:
                self.bot.send_message(
                    message.chat.id, 
                    "❌ هذه الوظيفة متاحة للمدرسين فقط."
                )
                return False
            
            # بدء المحادثة
            instructor_conversations[instructor_telegram_id] = {
                'state': CONVERSATION_STATES['WAITING_FOR_LINK_DURATION'],
                'data': {
                    'instructor_id': instructor['user_id'],
                    'instructor_name': f"{instructor.get('first_name', '')} {instructor.get('last_name', '')}".strip(),
                    'specialization_id': instructor.get('specialization_id'),
                    'telegram_id': instructor_telegram_id
                }
            }
            
            welcome_text = """
🎓 إنشاء رابط دعوة للطلاب

سأقوم بإنشاء رابط دعوة خاص بك لجذب الطلاب إلى تخصصك.

⏰ كم يوماً تريد أن يبقى الرابط فعالاً؟

اختر من الخيارات أدناه أو أدخل عدد الأيام (1-365):
            """
            
            # إنشاء لوحة مفاتيح للمدة
            markup = types.InlineKeyboardMarkup(row_width=3)
            markup.add(
                types.InlineKeyboardButton("7 أيام", callback_data="student_invite_duration_7"),
                types.InlineKeyboardButton("30 يوماً", callback_data="student_invite_duration_30"),
                types.InlineKeyboardButton("90 يوماً", callback_data="student_invite_duration_90")
            )
            markup.add(
                types.InlineKeyboardButton("6 أشهر", callback_data="student_invite_duration_180"),
                types.InlineKeyboardButton("سنة كاملة", callback_data="student_invite_duration_365")
            )
            markup.add(
                types.InlineKeyboardButton("❌ إلغاء", callback_data="student_invite_cancel")
            )
            
            self.bot.send_message(message.chat.id, welcome_text, reply_markup=markup)
            return True
            
        except Exception as e:
            logger.error(f"خطأ في بدء إنشاء رابط دعوة الطلاب: {e}")
            self.bot.send_message(
                message.chat.id, 
                "❌ حدث خطأ في النظام. حاول مرة أخرى لاحقاً."
            )
            return False
    
    def handle_callback_query(self, call, instructor_telegram_id: str) -> bool:
        """معالجة استعلامات الأزرار المضمنة"""
        try:
            logger.info(f"معالجة استعلام دعوة الطلاب: {call.data} من المستخدم: {instructor_telegram_id}")

            # التحقق من أن الاستعلام متعلق بدعوة الطلاب
            if not call.data.startswith('student_invite_'):
                logger.info(f"الاستعلام لا يبدأ بـ student_invite_: {call.data}")
                return False
            
            # التحقق من وجود محادثة نشطة
            if instructor_telegram_id not in instructor_conversations:
                self.bot.answer_callback_query(call.id, "❌ لا توجد عملية نشطة")
                return True
            
            conversation = instructor_conversations[instructor_telegram_id]
            
            if call.data.startswith('student_invite_duration_'):
                # معالجة اختيار المدة
                duration_str = call.data.replace('student_invite_duration_', '')
                try:
                    duration_days = int(duration_str)
                    conversation['data']['duration_days'] = duration_days
                    conversation['state'] = CONVERSATION_STATES['WAITING_FOR_CONFIRMATION']
                    
                    # عرض تأكيد البيانات
                    self._show_confirmation(call.message, instructor_telegram_id)
                    self.bot.answer_callback_query(call.id, f"✅ تم اختيار {duration_days} يوماً")
                    
                except ValueError:
                    self.bot.answer_callback_query(call.id, "❌ قيمة غير صحيحة")
                
            elif call.data == 'student_invite_confirm':
                # تأكيد إنشاء الرابط
                self._create_invitation_link(call.message, instructor_telegram_id)
                self.bot.answer_callback_query(call.id, "✅ جاري إنشاء الرابط...")
                
            elif call.data == 'student_invite_cancel':
                # إلغاء العملية
                if instructor_telegram_id in instructor_conversations:
                    del instructor_conversations[instructor_telegram_id]
                
                self.bot.edit_message_text(
                    "❌ تم إلغاء عملية إنشاء رابط الدعوة.",
                    call.message.chat.id,
                    call.message.message_id
                )
                self.bot.answer_callback_query(call.id, "تم الإلغاء")
            
            return True
            
        except Exception as e:
            logger.error(f"خطأ في معالجة استعلام دعوة الطلاب: {e}")
            self.bot.answer_callback_query(call.id, "❌ حدث خطأ")
            return True
    
    def handle_text_message(self, message, instructor_telegram_id: str) -> bool:
        """معالجة الرسائل النصية"""
        try:
            # التحقق من وجود محادثة نشطة
            if instructor_telegram_id not in instructor_conversations:
                return False
            
            conversation = instructor_conversations[instructor_telegram_id]
            
            if conversation['state'] == CONVERSATION_STATES['WAITING_FOR_LINK_DURATION']:
                # معالجة إدخال مدة مخصصة
                try:
                    duration_days = int(message.text.strip())
                    
                    if duration_days < 1 or duration_days > 365:
                        self.bot.send_message(
                            message.chat.id,
                            "❌ المدة يجب أن تكون بين 1 و 365 يوماً. حاول مرة أخرى:"
                        )
                        return True
                    
                    conversation['data']['duration_days'] = duration_days
                    conversation['state'] = CONVERSATION_STATES['WAITING_FOR_CONFIRMATION']
                    
                    # عرض تأكيد البيانات
                    self._show_confirmation(message, instructor_telegram_id)
                    
                except ValueError:
                    self.bot.send_message(
                        message.chat.id,
                        "❌ يرجى إدخال رقم صحيح للأيام (1-365):"
                    )
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"خطأ في معالجة رسالة دعوة الطلاب: {e}")
            return False
    
    def _show_confirmation(self, message, instructor_telegram_id: str):
        """عرض تأكيد البيانات قبل إنشاء الرابط"""
        try:
            conversation = instructor_conversations[instructor_telegram_id]
            data = conversation['data']
            
            # الحصول على اسم التخصص
            try:
                if hasattr(self.firebase_manager, 'get_specialization'):
                    specialization = self.firebase_manager.get_specialization(data['specialization_id'])
                    specialization_name = specialization.get('name', 'غير محدد') if specialization else 'غير محدد'
                else:
                    # حل بديل: الحصول على جميع التخصصات والبحث
                    all_specs = self.firebase_manager.get_all_specializations()
                    specialization_name = 'غير محدد'
                    for spec in all_specs:
                        if spec.get('id') == data['specialization_id']:
                            specialization_name = spec.get('name', 'غير محدد')
                            break
            except Exception as e:
                logger.error(f"خطأ في الحصول على التخصص: {e}")
                specialization_name = 'غير محدد'
            
            confirmation_text = f"""
📋 تأكيد بيانات رابط الدعوة

👨‍🏫 المدرس: {data['instructor_name']}
🎓 التخصص: {specialization_name}
⏰ مدة الرابط: {data['duration_days']} يوماً
📅 تاريخ الانتهاء: {self._calculate_expiry_date(data['duration_days'])}

ℹ️ ملاحظات مهمة:
• الرابط سيكون صالحاً لعدد غير محدود من الاستخدامات
• كل طالب يمكنه إنشاء حساب واحد فقط باستخدام معرف التليجرام
• سيتم إنشاء حسابات تلقائية للطلاب بصيغة: معرف_التليجرام@rray.com

هل تريد المتابعة؟
            """
            
            markup = types.InlineKeyboardMarkup(row_width=2)
            markup.add(
                types.InlineKeyboardButton("✅ تأكيد وإنشاء", callback_data="student_invite_confirm"),
                types.InlineKeyboardButton("❌ إلغاء", callback_data="student_invite_cancel")
            )
            
            self.bot.send_message(message.chat.id, confirmation_text, reply_markup=markup)
            
        except Exception as e:
            logger.error(f"خطأ في عرض تأكيد دعوة الطلاب: {e}")
    
    def _calculate_expiry_date(self, days: int) -> str:
        """حساب تاريخ انتهاء الرابط"""
        try:
            from datetime import timedelta
            expiry_date = datetime.now() + timedelta(days=days)
            return expiry_date.strftime("%Y-%m-%d")
        except:
            return "غير محدد"
    
    def _create_invitation_link(self, message, instructor_telegram_id: str):
        """إنشاء رابط الدعوة الفعلي"""
        try:
            conversation = instructor_conversations[instructor_telegram_id]
            data = conversation['data']
            
            # إنشاء رابط الدعوة
            success, invitation_url, error_message = self.invitation_manager.create_student_invitation_link(
                created_by=instructor_telegram_id,
                instructor_id=data['instructor_id'],
                specialization_id=data['specialization_id'],
                expires_days=data['duration_days']
            )
            
            if success and invitation_url:
                # الحصول على اسم التخصص
                try:
                    if hasattr(self.firebase_manager, 'get_specialization'):
                        specialization = self.firebase_manager.get_specialization(data['specialization_id'])
                        specialization_name = specialization.get('name', 'غير محدد') if specialization else 'غير محدد'
                    else:
                        # حل بديل: الحصول على جميع التخصصات والبحث
                        all_specs = self.firebase_manager.get_all_specializations()
                        specialization_name = 'غير محدد'
                        for spec in all_specs:
                            if spec.get('id') == data['specialization_id']:
                                specialization_name = spec.get('name', 'غير محدد')
                                break
                except Exception as e:
                    logger.error(f"خطأ في الحصول على التخصص: {e}")
                    specialization_name = 'غير محدد'
                
                success_text = f"""
✅ تم إنشاء رابط الدعوة بنجاح!

🎓 التخصص: {specialization_name}
👨‍🏫 المدرس: {data['instructor_name']}
⏰ صالح لمدة: {data['duration_days']} يوماً

🔗 رابط الدعوة:
{invitation_url}

📋 تعليمات للطلاب:
1. انقر على الرابط أعلاه
2. ابدأ محادثة مع البوت
3. سيتم إنشاء حساب تلقائياً
4. ستحصل على بيانات الدخول فوراً

💡 يمكنك مشاركة هذا الرابط مع طلابك عبر:
• مجموعات التليجرام
• الواتساب
• وسائل التواصل الاجتماعي
• البريد الإلكتروني

⚠️ ملاحظة: كل طالب يمكنه إنشاء حساب واحد فقط
                """
                
                self.bot.send_message(message.chat.id, success_text)
                
                # تنظيف المحادثة
                if instructor_telegram_id in instructor_conversations:
                    del instructor_conversations[instructor_telegram_id]
                
            else:
                error_text = f"❌ فشل في إنشاء رابط الدعوة: {error_message}"
                self.bot.send_message(message.chat.id, error_text)
                
        except Exception as e:
            logger.error(f"خطأ في إنشاء رابط دعوة الطلاب: {e}")
            self.bot.send_message(
                message.chat.id,
                "❌ حدث خطأ في إنشاء الرابط. حاول مرة أخرى لاحقاً."
            )
