{% extends "base.html" %}

{% block title %}إدارة أكواد التفعيل - {{ platform_name }}{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .code-card {
        border: 1px solid #e3e6f0;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        background: white;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
    }
    
    .code-card:hover {
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }
    
    .code-display {
        font-family: 'Courier New', monospace;
        font-size: 1.2rem;
        font-weight: bold;
        color: #5a5c69;
        background: #f8f9fc;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        border: 2px dashed #d1d3e2;
        text-align: center;
        margin: 1rem 0;
    }
    
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .status-active {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .status-expired {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .btn-copy {
        background: linear-gradient(135deg, #36b9cc 0%, #258391 100%);
        border: none;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-copy:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 10px rgba(0,0,0,0.2);
    }
    
    .create-code-form {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 0.75rem 2rem;
        border-radius: 25px;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }
    
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-0">
                    <i class="fas fa-key me-3"></i>
                    إدارة أكواد التفعيل
                </h1>
                <p class="mb-0 mt-2 opacity-75">إنشاء وإدارة أكواد التفعيل لكورساتك</p>
            </div>
            <div class="col-md-4 text-end">
                <button class="btn btn-light btn-lg" data-bs-toggle="modal" data-bs-target="#createCodeModal">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء كود جديد
                </button>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ stats.total_codes or 0 }}</div>
                <div>إجمالي الأكواد</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ stats.active_codes or 0 }}</div>
                <div>الأكواد النشطة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ stats.used_codes or 0 }}</div>
                <div>الأكواد المستخدمة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ stats.total_uses or 0 }}</div>
                <div>إجمالي الاستخدامات</div>
            </div>
        </div>
    </div>

    <!-- قائمة أكواد التفعيل -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-list me-2"></i>
                        أكواد التفعيل الخاصة بك
                    </h5>
                </div>
                <div class="card-body">
                    {% if activation_codes %}
                        <div id="codesContainer">
                            {% for code in activation_codes %}
                            <div class="code-card" data-code-id="{{ code.id }}">
                                <div class="row align-items-center">
                                    <div class="col-md-4">
                                        <h6 class="mb-1">{{ code.course_title or 'كورس غير محدد' }}</h6>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            {{ code.created_at[:10] if code.created_at else 'غير محدد' }}
                                        </small>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="code-display">{{ code.code }}</div>
                                        <div class="text-center">
                                            <button class="btn-copy" onclick="copyCode('{{ code.code }}')">
                                                <i class="fas fa-copy me-1"></i>
                                                نسخ الكود
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-4 text-end">
                                        <div class="mb-2">
                                            {% if code.expired %}
                                                <span class="status-badge status-expired">منتهي الصلاحية</span>
                                            {% elif code.active %}
                                                <span class="status-badge status-active">نشط</span>
                                            {% else %}
                                                <span class="status-badge status-inactive">غير نشط</span>
                                            {% endif %}
                                        </div>
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                الاستخدامات: {{ code.current_uses or 0 }} / {{ code.max_uses or 1 }}
                                            </small>
                                        </div>
                                        <div class="btn-group">
                                            {% if code.active and not code.expired %}
                                                <button class="btn btn-sm btn-warning" onclick="deactivateCode('{{ code.id }}')">
                                                    <i class="fas fa-pause"></i>
                                                    تعطيل
                                                </button>
                                            {% endif %}
                                            {% if code.current_uses == 0 %}
                                                <button class="btn btn-sm btn-danger" onclick="deleteCode('{{ code.id }}')">
                                                    <i class="fas fa-trash"></i>
                                                    حذف
                                                </button>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-key fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد أكواد تفعيل</h5>
                            <p class="text-muted">قم بإنشاء أول كود تفعيل لكورساتك</p>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createCodeModal">
                                <i class="fas fa-plus me-2"></i>
                                إنشاء كود جديد
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal إنشاء كود جديد -->
<div class="modal fade" id="createCodeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>
                    إنشاء كود تفعيل جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="createCodeForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="courseSelect" class="form-label">اختر الكورس</label>
                        <select class="form-select" id="courseSelect" name="course_id" required>
                            <option value="">-- اختر الكورس --</option>
                            {% for course in courses %}
                                <option value="{{ course.id }}">{{ course.title }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="maxUses" class="form-label">عدد الاستخدامات المسموحة</label>
                        <input type="number" class="form-control" id="maxUses" name="max_uses" value="1" min="1" max="1000" required>
                        <div class="form-text">الحد الأقصى لعدد الطلاب الذين يمكنهم استخدام هذا الكود</div>
                    </div>
                    <div class="mb-3">
                        <label for="expiresInDays" class="form-label">انتهاء الصلاحية (بالأيام)</label>
                        <input type="number" class="form-control" id="expiresInDays" name="expires_in_days" min="1" max="365">
                        <div class="form-text">اتركه فارغاً إذا كنت لا تريد انتهاء صلاحية</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء الكود
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// نسخ الكود
function copyCode(code) {
    navigator.clipboard.writeText(code).then(function() {
        showAlert('تم نسخ الكود بنجاح!', 'success');
    }).catch(function() {
        // Fallback للمتصفحات القديمة
        const textArea = document.createElement('textarea');
        textArea.value = code;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showAlert('تم نسخ الكود بنجاح!', 'success');
    });
}

// إنشاء كود جديد
document.getElementById('createCodeForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    // تحويل القيم الرقمية
    if (data.max_uses) data.max_uses = parseInt(data.max_uses);
    if (data.expires_in_days) data.expires_in_days = parseInt(data.expires_in_days);
    
    fetch('/api/activation-codes/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('createCodeModal')).hide();
            location.reload(); // إعادة تحميل الصفحة لعرض الكود الجديد
        } else {
            showAlert(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('حدث خطأ في النظام', 'error');
    });
});

// تعطيل كود
function deactivateCode(codeId) {
    if (confirm('هل أنت متأكد من تعطيل هذا الكود؟')) {
        fetch(`/api/activation-codes/${codeId}/deactivate`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                location.reload();
            } else {
                showAlert(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ في النظام', 'error');
        });
    }
}

// حذف كود
function deleteCode(codeId) {
    if (confirm('هل أنت متأكد من حذف هذا الكود؟ لا يمكن التراجع عن هذا الإجراء.')) {
        fetch(`/api/activation-codes/${codeId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                location.reload();
            } else {
                showAlert(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('حدث خطأ في النظام', 'error');
        });
    }
}

// عرض التنبيهات
function showAlert(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // إضافة التنبيه في أعلى الصفحة
    const container = document.querySelector('.container');
    container.insertAdjacentHTML('afterbegin', alertHtml);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
