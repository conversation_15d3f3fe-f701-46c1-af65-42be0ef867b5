# 🎨 التصميم والواجهات المتقدمة - منصة الكورسات التعليمية

## 📱 هيكل الصفحات وتجربة المستخدم (UX Flow)

### 🏠 صفحات الهبوط (Landing Pages):

#### الصفحة الرئيسية:
```html
<!-- Hero Section -->
<section class="hero-section">
    <div class="hero-content">
        <h1 class="hero-title">منصة التعليم الطبي المتقدمة</h1>
        <p class="hero-subtitle">تعلم التخصصات الطبية مع أفضل المدرسين</p>
        <div class="hero-cta">
            <button class="btn-primary btn-lg">ابدأ التعلم الآن</button>
            <button class="btn-secondary btn-lg">تصفح الكورسات</button>
        </div>
        <div class="hero-stats">
            <div class="stat-item">
                <span class="stat-number">1000+</span>
                <span class="stat-label">طالب</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">50+</span>
                <span class="stat-label">كورس</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">20+</span>
                <span class="stat-label">مدرس</span>
            </div>
        </div>
    </div>
    <div class="hero-visual">
        <img src="hero-illustration.svg" alt="رسم توضيحي للتعليم الطبي" class="hero-image">
        <div class="floating-cards">
            <div class="floating-card card-1">🔬 التحليل الطبي</div>
            <div class="floating-card card-2">📡 الأشعة</div>
            <div class="floating-card card-3">💉 التخدير</div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section">
    <div class="container">
        <h2 class="section-title">لماذا تختار منصتنا؟</h2>
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">🎓</div>
                <h3>كورسات متخصصة</h3>
                <p>محتوى تعليمي متقدم في التحليل والأشعة والتخدير</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">👨‍🏫</div>
                <h3>مدرسين خبراء</h3>
                <p>نخبة من أفضل المدرسين في المجال الطبي</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <h3>تعلم في أي مكان</h3>
                <p>منصة متجاوبة تعمل على جميع الأجهزة</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">🏆</div>
                <h3>شهادات معتمدة</h3>
                <p>احصل على شهادات إتمام معتمدة</p>
            </div>
        </div>
    </div>
</section>

<!-- Specializations Section -->
<section class="specializations-section">
    <div class="container">
        <h2 class="section-title">التخصصات المتاحة</h2>
        <div class="specializations-grid">
            <div class="specialization-card">
                <div class="specialization-icon">🔬</div>
                <h3>التحليل الطبي</h3>
                <p>تعلم أساسيات وتقنيات التحليل الطبي المتقدمة</p>
                <div class="specialization-stages">
                    <span class="stage-badge">المرحلة 2</span>
                    <span class="stage-badge">المرحلة 3</span>
                    <span class="stage-badge">المرحلة 4</span>
                </div>
                <button class="btn-outline">استكشف الكورسات</button>
            </div>
            <div class="specialization-card">
                <div class="specialization-icon">📡</div>
                <h3>الأشعة</h3>
                <p>دراسة تقنيات التصوير الطبي والتشخيص بالأشعة</p>
                <div class="specialization-stages">
                    <span class="stage-badge">المرحلة 2</span>
                    <span class="stage-badge">المرحلة 3</span>
                    <span class="stage-badge">المرحلة 4</span>
                </div>
                <button class="btn-outline">استكشف الكورسات</button>
            </div>
            <div class="specialization-card">
                <div class="specialization-icon">💉</div>
                <h3>التخدير</h3>
                <p>تعلم أساليب وتقنيات التخدير الحديثة</p>
                <div class="specialization-stages">
                    <span class="stage-badge">المرحلة 3</span>
                    <span class="stage-badge">المرحلة 4</span>
                </div>
                <button class="btn-outline">استكشف الكورسات</button>
            </div>
        </div>
    </div>
</section>
```

#### صفحة تسجيل الدخول:
```html
<div class="auth-container">
    <div class="auth-background">
        <div class="auth-pattern"></div>
    </div>
    <div class="auth-card">
        <div class="auth-header">
            <img src="logo.svg" alt="شعار المنصة" class="auth-logo">
            <h2>مرحباً بعودتك</h2>
            <p>ادخل بياناتك للوصول لحسابك</p>
        </div>
        <form class="auth-form">
            <div class="form-group">
                <label for="email">البريد الإلكتروني</label>
                <div class="input-wrapper">
                    <i class="input-icon">📧</i>
                    <input type="email" id="email" class="form-control" placeholder="<EMAIL>">
                </div>
            </div>
            <div class="form-group">
                <label for="password">كلمة المرور</label>
                <div class="input-wrapper">
                    <i class="input-icon">🔒</i>
                    <input type="password" id="password" class="form-control" placeholder="••••••••">
                    <button type="button" class="password-toggle">👁️</button>
                </div>
            </div>
            <div class="form-options">
                <label class="checkbox-wrapper">
                    <input type="checkbox" class="checkbox">
                    <span class="checkmark"></span>
                    تذكرني
                </label>
                <a href="#" class="forgot-password">نسيت كلمة المرور؟</a>
            </div>
            <button type="submit" class="btn-primary btn-full">تسجيل الدخول</button>
        </form>
        <div class="auth-footer">
            <p>لا تملك حساب؟ <a href="#" class="auth-link">تواصل مع مدرسك</a></p>
        </div>
    </div>
</div>
```

### 🎯 تجربة المستخدم (UX Flow):

#### رحلة الطالب:
1. **الوصول للمنصة** → صفحة الهبوط مع عرض الميزات والتخصصات
2. **تسجيل الدخول** → واجهة تسجيل دخول أنيقة مع تأثيرات بصرية
3. **لوحة التحكم** → عرض شخصي للكورسات والتقدم مع إحصائيات
4. **تصفح الكورسات** → صفحة تصفح مع فلاتر ذكية وبحث متقدم
5. **تفعيل كورس** → صفحة إدخال كود التفعيل مع تأكيد بصري
6. **دراسة الكورس** → واجهة المحتوى مع مشغل الفيديو المتقدم
7. **تتبع التقدم** → صفحة إحصائيات التقدم مع رسوم بيانية

#### رحلة المدرس:
1. **إنشاء الحساب** → عبر رابط من البوت مع معالج الإعداد
2. **إعداد الملف الشخصي** → واجهة تفاعلية لإضافة المعلومات
3. **لوحة تحكم المدرس** → لوحة شاملة لإدارة الكورسات والطلاب
4. **إنشاء كورس جديد** → معالج إنشاء الكورس خطوة بخطوة مع معاينة
5. **إدارة المحتوى** → محرر المحتوى الغني مع drag & drop
6. **إنشاء أكواد التفعيل** → واجهة إنتاج الأكواد مع QR codes
7. **إدارة الطلاب** → قائمة الطلاب مع أدوات الإدارة والتواصل

#### رحلة الأدمن:
1. **لوحة التحكم الرئيسية** → نظرة شاملة على النظام مع KPIs
2. **إدارة التخصصات** → واجهة إنشاء وتعديل التخصصات مع الأيقونات
3. **إدارة المدرسين** → قائمة المدرسين مع الصلاحيات والإحصائيات
4. **إدارة الطلاب** → عرض شامل للطلاب مع فلاتر متقدمة
5. **التحليلات المتقدمة** → لوحة تحليلات مع رسوم بيانية تفاعلية

### 🖥️ لوحات التحكم المفصلة:

#### لوحة تحكم الأدمن:
```html
<div class="admin-dashboard">
    <!-- Header مع الإحصائيات السريعة -->
    <div class="dashboard-header">
        <div class="welcome-section">
            <h1>مرحباً، أدمن</h1>
            <p>إليك نظرة سريعة على أداء المنصة اليوم</p>
        </div>
        <div class="stats-grid">
            <div class="stat-card primary">
                <div class="stat-icon">👥</div>
                <div class="stat-content">
                    <h3>1,234</h3>
                    <p>إجمالي المستخدمين</p>
                    <span class="stat-change positive">+12% من الشهر الماضي</span>
                </div>
            </div>
            <div class="stat-card success">
                <div class="stat-icon">📚</div>
                <div class="stat-content">
                    <h3>89</h3>
                    <p>الكورسات النشطة</p>
                    <span class="stat-change positive">+5 كورسات جديدة</span>
                </div>
            </div>
            <div class="stat-card warning">
                <div class="stat-icon">🎫</div>
                <div class="stat-content">
                    <h3>456</h3>
                    <p>أكواد التفعيل المستخدمة</p>
                    <span class="stat-change neutral">هذا الأسبوع</span>
                </div>
            </div>
            <div class="stat-card info">
                <div class="stat-icon">💰</div>
                <div class="stat-content">
                    <h3>$12,345</h3>
                    <p>الإيرادات الشهرية</p>
                    <span class="stat-change positive">+8% نمو</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Navigation Tabs -->
    <div class="dashboard-nav">
        <button class="nav-tab active" data-tab="overview">
            <i class="tab-icon">📊</i>
            <span>نظرة عامة</span>
        </button>
        <button class="nav-tab" data-tab="instructors">
            <i class="tab-icon">👨‍🏫</i>
            <span>المدرسين</span>
        </button>
        <button class="nav-tab" data-tab="students">
            <i class="tab-icon">👨‍🎓</i>
            <span>الطلاب</span>
        </button>
        <button class="nav-tab" data-tab="courses">
            <i class="tab-icon">📚</i>
            <span>الكورسات</span>
        </button>
        <button class="nav-tab" data-tab="specializations">
            <i class="tab-icon">🎯</i>
            <span>التخصصات</span>
        </button>
        <button class="nav-tab" data-tab="analytics">
            <i class="tab-icon">📈</i>
            <span>التحليلات</span>
        </button>
    </div>
    
    <!-- Content Area -->
    <div class="dashboard-content">
        <div class="content-section active" id="overview">
            <div class="charts-grid">
                <div class="chart-card">
                    <h3>نمو المستخدمين</h3>
                    <canvas id="usersChart"></canvas>
                </div>
                <div class="chart-card">
                    <h3>الكورسات الأكثر شعبية</h3>
                    <canvas id="coursesChart"></canvas>
                </div>
            </div>
            <div class="recent-activities">
                <h3>الأنشطة الأخيرة</h3>
                <div class="activity-list">
                    <!-- قائمة الأنشطة -->
                </div>
            </div>
        </div>
        <!-- باقي أقسام المحتوى -->
    </div>
</div>
```

#### لوحة تحكم المدرس:
```html
<div class="instructor-dashboard">
    <div class="dashboard-sidebar">
        <div class="instructor-profile">
            <div class="profile-avatar-wrapper">
                <img src="avatar.jpg" alt="صورة المدرس" class="profile-avatar">
                <div class="status-indicator online"></div>
            </div>
            <div class="profile-info">
                <h3>د. أحمد محمد</h3>
                <p class="profile-title">أستاذ التحليل الطبي</p>
                <div class="profile-stats">
                    <div class="stat-item">
                        <span class="stat-number">15</span>
                        <span class="stat-label">كورس</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">234</span>
                        <span class="stat-label">طالب</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">4.8</span>
                        <span class="stat-label">تقييم</span>
                    </div>
                </div>
            </div>
        </div>
        
        <nav class="sidebar-nav">
            <a href="#" class="nav-item active">
                <i class="nav-icon">📊</i>
                <span>لوحة التحكم</span>
            </a>
            <a href="#" class="nav-item">
                <i class="nav-icon">📚</i>
                <span>كورساتي</span>
                <span class="nav-badge">15</span>
            </a>
            <a href="#" class="nav-item">
                <i class="nav-icon">👥</i>
                <span>طلابي</span>
                <span class="nav-badge">234</span>
            </a>
            <a href="#" class="nav-item">
                <i class="nav-icon">🎫</i>
                <span>أكواد التفعيل</span>
            </a>
            <a href="#" class="nav-item">
                <i class="nav-icon">📈</i>
                <span>الإحصائيات</span>
            </a>
            <a href="#" class="nav-item">
                <i class="nav-icon">⚙️</i>
                <span>الإعدادات</span>
            </a>
        </nav>
        
        <div class="quick-actions">
            <h4>إجراءات سريعة</h4>
            <button class="quick-action-btn primary">
                <i class="btn-icon">➕</i>
                <span>كورس جديد</span>
            </button>
            <button class="quick-action-btn secondary">
                <i class="btn-icon">🎫</i>
                <span>كود تفعيل</span>
            </button>
        </div>
    </div>
    
    <div class="dashboard-main">
        <div class="main-header">
            <h1>لوحة التحكم</h1>
            <div class="header-actions">
                <button class="btn-icon-only" title="الإشعارات">
                    <i class="icon">🔔</i>
                    <span class="notification-badge">3</span>
                </button>
                <button class="btn-icon-only" title="الرسائل">
                    <i class="icon">💬</i>
                </button>
            </div>
        </div>
        
        <div class="main-content">
            <!-- محتوى لوحة التحكم -->
        </div>
    </div>
</div>
```

#### لوحة تحكم الطالب:
```html
<div class="student-dashboard">
    <div class="dashboard-header">
        <div class="welcome-section">
            <h1>مرحباً بك، أحمد</h1>
            <p>استمر في رحلتك التعليمية واحقق أهدافك</p>
        </div>
        <div class="header-actions">
            <button class="btn-primary">
                <i class="btn-icon">🎫</i>
                <span>تفعيل كورس جديد</span>
            </button>
        </div>
    </div>

    <div class="progress-overview">
        <div class="progress-card main">
            <h3>التقدم الإجمالي</h3>
            <div class="progress-circle-wrapper">
                <div class="progress-circle" data-progress="75">
                    <svg class="progress-ring" width="120" height="120">
                        <circle class="progress-ring-circle" cx="60" cy="60" r="54"></circle>
                    </svg>
                    <div class="progress-text">
                        <span class="progress-percentage">75%</span>
                        <span class="progress-label">مكتمل</span>
                    </div>
                </div>
            </div>
            <div class="progress-stats">
                <div class="stat">
                    <span class="stat-number">8</span>
                    <span class="stat-label">كورسات مكتملة</span>
                </div>
                <div class="stat">
                    <span class="stat-number">3</span>
                    <span class="stat-label">قيد الدراسة</span>
                </div>
            </div>
        </div>

        <div class="recent-activity">
            <h3>النشاط الأخير</h3>
            <div class="activity-list">
                <div class="activity-item">
                    <div class="activity-icon">📚</div>
                    <div class="activity-content">
                        <h4>أكملت درس "أساسيات التحليل"</h4>
                        <p>كورس التحليل الطبي - المرحلة 2</p>
                        <span class="activity-time">منذ ساعتين</span>
                    </div>
                </div>
                <div class="activity-item">
                    <div class="activity-icon">🎯</div>
                    <div class="activity-content">
                        <h4>حصلت على 95% في الاختبار</h4>
                        <p>اختبار الوحدة الثالثة</p>
                        <span class="activity-time">أمس</span>
                    </div>
                </div>
                <div class="activity-item">
                    <div class="activity-icon">🏆</div>
                    <div class="activity-content">
                        <h4>حصلت على شارة "المتفوق"</h4>
                        <p>لإكمال 5 كورسات بتفوق</p>
                        <span class="activity-time">منذ 3 أيام</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="courses-section">
        <div class="section-header">
            <h2>كورساتي</h2>
            <div class="section-filters">
                <button class="filter-btn active" data-filter="all">الكل</button>
                <button class="filter-btn" data-filter="in-progress">قيد الدراسة</button>
                <button class="filter-btn" data-filter="completed">مكتملة</button>
            </div>
        </div>

        <div class="courses-grid">
            <div class="course-card in-progress">
                <div class="course-thumbnail">
                    <img src="course-thumb.jpg" alt="صورة الكورس">
                    <div class="course-progress-overlay">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 60%"></div>
                        </div>
                        <span class="progress-text">60% مكتمل</span>
                    </div>
                </div>
                <div class="course-content">
                    <div class="course-meta">
                        <span class="course-category">التحليل الطبي</span>
                        <span class="course-level">المرحلة 3</span>
                    </div>
                    <h3 class="course-title">تحليل الدم المتقدم</h3>
                    <p class="course-instructor">د. أحمد محمد</p>
                    <div class="course-stats">
                        <span class="stat">
                            <i class="icon">📹</i>
                            <span>12/20 درس</span>
                        </span>
                        <span class="stat">
                            <i class="icon">⏱️</i>
                            <span>3 ساعات متبقية</span>
                        </span>
                    </div>
                    <button class="btn-primary btn-full">متابعة الدراسة</button>
                </div>
            </div>
            <!-- المزيد من بطاقات الكورسات -->
        </div>
    </div>
</div>
```

## 🎨 نظام التصميم المتقدم (Design System)

### 🌈 الهوية البصرية والألوان:

#### التدرجات اللونية:
```css
:root {
    /* التدرجات الأساسية */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-warning: linear-gradient(135deg, #f9ca24 0%, #f0932b 100%);
    --gradient-danger: linear-gradient(135deg, #eb4d4b 0%, #6c5ce7 100%);

    /* تدرجات الخلفيات */
    --gradient-bg-light: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    --gradient-bg-dark: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
    --gradient-bg-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

    /* تدرجات الأزرار */
    --gradient-btn-primary: linear-gradient(45deg, #667eea, #764ba2);
    --gradient-btn-secondary: linear-gradient(45deg, #f093fb, #f5576c);
    --gradient-btn-success: linear-gradient(45deg, #4facfe, #00f2fe);
}
```

#### نظام الظلال (Shadows):
```css
:root {
    /* ظلال البطاقات */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* ظلال ملونة */
    --shadow-primary: 0 4px 14px 0 rgba(37, 99, 235, 0.25);
    --shadow-success: 0 4px 14px 0 rgba(5, 150, 105, 0.25);
    --shadow-warning: 0 4px 14px 0 rgba(217, 119, 6, 0.25);
    --shadow-danger: 0 4px 14px 0 rgba(220, 38, 38, 0.25);

    /* ظلال التفاعل */
    --shadow-hover: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-focus: 0 0 0 3px rgba(37, 99, 235, 0.1);
}
```

### 📝 نظام الطباعة المتقدم (Typography):

#### الخطوط والأحجام:
```css
/* استيراد الخطوط */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

:root {
    /* عائلات الخطوط */
    --font-primary: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-secondary: 'Poppins', 'Arial', sans-serif;
    --font-mono: 'Fira Code', 'SF Mono', 'Monaco', 'Inconsolata', monospace;

    /* أحجام الخطوط */
    --text-xs: 0.75rem;     /* 12px */
    --text-sm: 0.875rem;    /* 14px */
    --text-base: 1rem;      /* 16px */
    --text-lg: 1.125rem;    /* 18px */
    --text-xl: 1.25rem;     /* 20px */
    --text-2xl: 1.5rem;     /* 24px */
    --text-3xl: 1.875rem;   /* 30px */
    --text-4xl: 2.25rem;    /* 36px */
    --text-5xl: 3rem;       /* 48px */
    --text-6xl: 3.75rem;    /* 60px */

    /* ارتفاع الأسطر */
    --leading-tight: 1.25;
    --leading-normal: 1.5;
    --leading-relaxed: 1.75;
    --leading-loose: 2;
}

/* فئات الطباعة */
.heading-1 {
    font-size: var(--text-5xl);
    font-weight: 800;
    line-height: var(--leading-tight);
    letter-spacing: -0.025em;
}

.heading-2 {
    font-size: var(--text-4xl);
    font-weight: 700;
    line-height: var(--leading-tight);
    letter-spacing: -0.025em;
}

.heading-3 {
    font-size: var(--text-3xl);
    font-weight: 600;
    line-height: var(--leading-normal);
}

.body-large {
    font-size: var(--text-lg);
    line-height: var(--leading-relaxed);
}

.body-normal {
    font-size: var(--text-base);
    line-height: var(--leading-normal);
}

.body-small {
    font-size: var(--text-sm);
    line-height: var(--leading-normal);
}

.caption {
    font-size: var(--text-xs);
    line-height: var(--leading-normal);
    color: var(--text-muted);
}
```
